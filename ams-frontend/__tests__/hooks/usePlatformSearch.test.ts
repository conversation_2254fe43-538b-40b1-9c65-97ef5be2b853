import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { usePlatformSearch, useBackendPagination } from '@/hooks/usePlatformSearch'
import { IPlatform } from '@/store/interfaces'

describe('usePlatformSearch', () => {
  const mockPlatforms: IPlatform[] = [
    { id: '1', name: 'Mobile App', description: 'Mobile platform' },
    { id: '2', name: 'Web Portal', description: 'Web platform' },
    { id: '3', name: 'API Gateway', description: 'API platform' },
    { id: '4', name: 'Mobile Banking', description: 'Banking mobile app' },
    { id: '5', name: 'Web Banking', description: 'Banking web portal' },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initial state', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms })
      )

      expect(result.current.searchQuery).toBe('')
      expect(result.current.currentPage).toBe(1)
      expect(result.current.filteredPlatforms).toEqual(mockPlatforms)
      expect(result.current.displayedPlatforms).toEqual(mockPlatforms.slice(0, 10))
      expect(result.current.totalPages).toBe(1)
      expect(result.current.hasMore).toBe(false)
    })

    it('should handle custom itemsPerPage', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms, itemsPerPage: 2 })
      )

      expect(result.current.displayedPlatforms).toEqual(mockPlatforms.slice(0, 2))
      expect(result.current.totalPages).toBe(3)
      expect(result.current.hasMore).toBe(true)
    })

    it('should handle empty platforms array', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: [] })
      )

      expect(result.current.filteredPlatforms).toEqual([])
      expect(result.current.displayedPlatforms).toEqual([])
      expect(result.current.totalPages).toBe(0)
      expect(result.current.hasMore).toBe(false)
    })
  })

  describe('search functionality', () => {
    it('should filter platforms by name (case insensitive)', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms })
      )

      act(() => {
        result.current.setSearchQuery('mobile')
      })

      expect(result.current.searchQuery).toBe('mobile')
      expect(result.current.filteredPlatforms).toHaveLength(2)
      expect(result.current.filteredPlatforms[0].name).toBe('Mobile App')
      expect(result.current.filteredPlatforms[1].name).toBe('Mobile Banking')
      expect(result.current.currentPage).toBe(1) // Should reset to page 1
    })

    it('should handle search with no results', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms })
      )

      act(() => {
        result.current.setSearchQuery('nonexistent')
      })

      expect(result.current.filteredPlatforms).toHaveLength(0)
      expect(result.current.displayedPlatforms).toHaveLength(0)
      expect(result.current.totalPages).toBe(0)
      expect(result.current.hasMore).toBe(false)
    })

    it('should handle empty search query', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms })
      )

      // First set a search query
      act(() => {
        result.current.setSearchQuery('mobile')
      })

      // Then clear it
      act(() => {
        result.current.setSearchQuery('')
      })

      expect(result.current.filteredPlatforms).toEqual(mockPlatforms)
      expect(result.current.currentPage).toBe(1)
    })

    it('should handle whitespace-only search query', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms })
      )

      act(() => {
        result.current.setSearchQuery('   ')
      })

      expect(result.current.filteredPlatforms).toEqual(mockPlatforms)
    })
  })

  describe('pagination functionality', () => {
    it('should load more platforms when hasMore is true', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms, itemsPerPage: 2 })
      )

      expect(result.current.displayedPlatforms).toHaveLength(2)
      expect(result.current.hasMore).toBe(true)

      act(() => {
        result.current.loadMore()
      })

      expect(result.current.currentPage).toBe(2)
      expect(result.current.displayedPlatforms).toHaveLength(4)
      expect(result.current.hasMore).toBe(true)

      act(() => {
        result.current.loadMore()
      })

      expect(result.current.currentPage).toBe(3)
      expect(result.current.displayedPlatforms).toHaveLength(5)
      expect(result.current.hasMore).toBe(false)
    })

    it('should not load more when hasMore is false', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms, itemsPerPage: 10 })
      )

      expect(result.current.hasMore).toBe(false)
      const initialPage = result.current.currentPage

      act(() => {
        result.current.loadMore()
      })

      expect(result.current.currentPage).toBe(initialPage)
    })

    it('should reset pagination', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms, itemsPerPage: 2 })
      )

      // Load more pages
      act(() => {
        result.current.loadMore()
        result.current.loadMore()
      })

      expect(result.current.currentPage).toBe(3)

      // Reset pagination
      act(() => {
        result.current.resetPagination()
      })

      expect(result.current.currentPage).toBe(1)
      expect(result.current.displayedPlatforms).toHaveLength(2)
    })
  })

  describe('combined search and pagination', () => {
    it('should reset pagination when search query changes', () => {
      const { result } = renderHook(() =>
        usePlatformSearch({ platforms: mockPlatforms, itemsPerPage: 2 })
      )

      // Load more pages
      act(() => {
        result.current.loadMore()
      })

      expect(result.current.currentPage).toBe(2)

      // Change search query
      act(() => {
        result.current.setSearchQuery('web')
      })

      expect(result.current.currentPage).toBe(1)
      expect(result.current.filteredPlatforms).toHaveLength(2)
    })
  })
})

describe('useBackendPagination', () => {
  const mockFetchFunction = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('initial state', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction })
      )

      expect(result.current.currentPage).toBe(1)
      expect(result.current.searchQuery).toBe('')
      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('loadMore functionality', () => {
    it('should load more data successfully', async () => {
      mockFetchFunction.mockResolvedValue(undefined)

      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction, itemsPerPage: 5 })
      )

      await act(async () => {
        await result.current.loadMore()
      })

      expect(mockFetchFunction).toHaveBeenCalledWith(2, 5, '')
      expect(result.current.currentPage).toBe(2)
      expect(result.current.isLoading).toBe(false)
    })

    it('should handle loadMore error', async () => {
      const error = new Error('Load more failed')
      mockFetchFunction.mockRejectedValue(error)

      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction })
      )

      await act(async () => {
        await result.current.loadMore()
      })

      expect(console.error).toHaveBeenCalledWith('Error loading more data:', error)
      expect(result.current.currentPage).toBe(1) // Should not increment on error
      expect(result.current.isLoading).toBe(false)
    })

    it('should not load more when already loading', async () => {
      mockFetchFunction.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction })
      )

      // Start first load
      act(() => {
        result.current.loadMore()
      })

      expect(result.current.isLoading).toBe(true)

      // Try to load more while loading
      await act(async () => {
        await result.current.loadMore()
      })

      expect(mockFetchFunction).toHaveBeenCalledTimes(1)
    })
  })

  describe('refresh functionality', () => {
    it('should refresh data successfully', async () => {
      mockFetchFunction.mockResolvedValue(undefined)

      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction, itemsPerPage: 5 })
      )

      // First load more to change page
      await act(async () => {
        await result.current.loadMore()
      })

      expect(result.current.currentPage).toBe(2)

      // Then refresh
      await act(async () => {
        await result.current.refresh()
      })

      expect(mockFetchFunction).toHaveBeenLastCalledWith(1, 5, '')
      expect(result.current.currentPage).toBe(1)
      expect(result.current.isLoading).toBe(false)
    })

    it('should handle refresh error', async () => {
      const error = new Error('Refresh failed')
      mockFetchFunction.mockRejectedValue(error)

      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction })
      )

      await act(async () => {
        await result.current.refresh()
      })

      expect(console.error).toHaveBeenCalledWith('Error refreshing data:', error)
      expect(result.current.isLoading).toBe(false)
    })

    it('should not refresh when already loading', async () => {
      mockFetchFunction.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)))

      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction })
      )

      // Start loading
      act(() => {
        result.current.loadMore()
      })

      expect(result.current.isLoading).toBe(true)

      // Try to refresh while loading
      await act(async () => {
        await result.current.refresh()
      })

      expect(mockFetchFunction).toHaveBeenCalledTimes(1)
    })
  })

  describe('search functionality', () => {
    it('should set search query and refresh', async () => {
      mockFetchFunction.mockResolvedValue(undefined)

      const { result } = renderHook(() =>
        useBackendPagination({ fetchFunction: mockFetchFunction })
      )

      await act(async () => {
        result.current.setSearchQuery('test query')
      })

      expect(result.current.searchQuery).toBe('test query')
      expect(mockFetchFunction).toHaveBeenCalledWith(1, 10, 'test query')
    })
  })
})
