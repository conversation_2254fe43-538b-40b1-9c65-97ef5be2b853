import { describe, it, expect } from 'vitest'
import approvalRequestsReducer, {
  setOpenReviewRequest,
  setLoadingApprovals,
  setApprovals,
  setApprovalDrawerOpen,
  setPendingCustomerApprovals,
  setPendingCustomerApprovalsResponse,
  setPendingSingleCustomerApprovals,
  setPendingSingleCustomerApprovalsResponse,
  setCustomersWithPendingApprovals,
  setSelectedApprovalRequest,
  setUserApprovalRequestsFilters,
  setUserApprovalRequestSearch,
  setApprovalRequestResponse,
  setRequestTypes,
  setLoadingRequestTypes,
  setRequestTypesSuccess,
  setMakerCheckerTypes,
  setApprovalActions,
  setOpenDialog,
  type IState,
} from '@/store/reducers/ApprovalRequests'
import {
  IApprovalRequest,
  IApprovalRequestsResponse,
  IMakerCheckerType,
  IProfileApprovalRequests,
  RequestType,
} from '@/store/interfaces'

describe('approvalRequestsReducer', () => {
  const initialState: IState = {
    loading: false,
    openReviewRequest: {
      open: false,
      request: null,
    },
    error: null,
    isLoadingRequests: false,
    approvalRequests: [],
    approvalRequestsCount: 0,
    selectedApprovalRequest: {} as IApprovalRequest,
    userApprovalRequestFilters: {},
    approvalRequestSearch: {
      searchBy: ['status'],
      searchTerm: '',
    },
    approvalRequestResponse: {} as IApprovalRequestsResponse,
    requestTypes: [],
    isRequestTypesLoading: false,
    isRequestTypesSuccess: false,
    makerCheckerTypes: [],
    pendingCustomerApprovalRequests: [],
    pendingCustomerApprovalRequestResponse: {} as IApprovalRequestsResponse,
    pendingSingleCustomerApprovalRequests: [],
    customersWithPendingApprovals: [],
    pendingSingleCustomerApprovalRequestResponse:
      {} as IApprovalRequestsResponse,
    approvalDrawerOpen: false,
    approvalActions: false,
    openDialog: false,
  }

  it('should return the initial state', () => {
    expect(approvalRequestsReducer(undefined, { type: 'unknown' })).toEqual(
      initialState
    )
  })

  it('should handle setOpenReviewRequest', () => {
    const reviewRequest = {
      open: true,
      request: {
        id: '1',
        requestType: 'CREATE_USER',
        status: 'PENDING',
        createdBy: 'user1',
        createdAt: '2023-01-01',
      } as IApprovalRequest,
    }
    const actual = approvalRequestsReducer(
      initialState,
      setOpenReviewRequest(reviewRequest)
    )
    expect(actual.openReviewRequest).toEqual(reviewRequest)
  })

  it('should handle setLoadingApprovals', () => {
    const actual = approvalRequestsReducer(
      initialState,
      setLoadingApprovals(true)
    )
    expect(actual.isLoadingRequests).toBe(true)
  })

  it('should handle setApprovals', () => {
    const approvals: IApprovalRequest[] = [
      {
        id: '1',
        requestType: 'CREATE_USER',
        status: 'PENDING',
        createdBy: 'user1',
        createdAt: '2023-01-01',
        requestData: {},
      },
      {
        id: '2',
        requestType: 'UPDATE_ROLE',
        status: 'APPROVED',
        createdBy: 'user2',
        createdAt: '2023-01-02',
        requestData: {},
      },
    ]
    const actual = approvalRequestsReducer(
      initialState,
      setApprovals(approvals)
    )
    expect(actual.approvalRequests).toEqual(approvals)
  })

  it('should handle setApprovalDrawerOpen', () => {
    const actual = approvalRequestsReducer(
      initialState,
      setApprovalDrawerOpen(true)
    )
    expect(actual.approvalDrawerOpen).toBe(true)
  })

  it('should handle setPendingCustomerApprovals', () => {
    const pendingApprovals: IApprovalRequest[] = [
      {
        id: '3',
        requestType: 'CREATE_CUSTOMER',
        status: 'PENDING',
        createdBy: 'user3',
        createdAt: '2023-01-03',
        requestData: {},
      },
    ]
    const actual = approvalRequestsReducer(
      initialState,
      setPendingCustomerApprovals(pendingApprovals)
    )
    expect(actual.pendingCustomerApprovalRequests).toEqual(pendingApprovals)
  })

  it('should handle setPendingCustomerApprovalsResponse', () => {
    const response: IApprovalRequestsResponse = {
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 3,
      totalElements: 25,
      data: [],
    }
    const actual = approvalRequestsReducer(
      initialState,
      setPendingCustomerApprovalsResponse(response)
    )
    expect(actual.pendingCustomerApprovalRequestResponse).toEqual(response)
  })

  it('should handle setPendingSingleCustomerApprovals', () => {
    const singleCustomerApprovals: IApprovalRequest[] = [
      {
        id: '4',
        requestType: 'UPDATE_CUSTOMER',
        status: 'PENDING',
        createdBy: 'user4',
        createdAt: '2023-01-04',
        requestData: {},
      },
    ]
    const actual = approvalRequestsReducer(
      initialState,
      setPendingSingleCustomerApprovals(singleCustomerApprovals)
    )
    expect(actual.pendingSingleCustomerApprovalRequests).toEqual(
      singleCustomerApprovals
    )
  })

  it('should handle setPendingSingleCustomerApprovalsResponse', () => {
    const response: IApprovalRequestsResponse = {
      pageNumber: 2,
      pageSize: 5,
      totalNumberOfPages: 4,
      totalElements: 20,
      data: [],
    }
    const actual = approvalRequestsReducer(
      initialState,
      setPendingSingleCustomerApprovalsResponse(response)
    )
    expect(actual.pendingSingleCustomerApprovalRequestResponse).toEqual(
      response
    )
  })

  it('should handle setCustomersWithPendingApprovals', () => {
    const customers: IProfileApprovalRequests[] = [
      {
        customerId: '1',
        customerName: 'John Doe',
        pendingRequestsCount: 3,
        requests: [],
      },
    ]
    const actual = approvalRequestsReducer(
      initialState,
      setCustomersWithPendingApprovals(customers)
    )
    expect(actual.customersWithPendingApprovals).toEqual(customers)
  })

  it('should handle setSelectedApprovalRequest', () => {
    const request: IApprovalRequest = {
      id: '5',
      requestType: 'DELETE_USER',
      status: 'REJECTED',
      createdBy: 'user5',
      createdAt: '2023-01-05',
      requestData: {},
    }
    const actual = approvalRequestsReducer(
      initialState,
      setSelectedApprovalRequest(request)
    )
    expect(actual.selectedApprovalRequest).toEqual(request)
  })

  it('should handle setUserApprovalRequestsFilters', () => {
    const filters = { status: 'PENDING', requestType: 'CREATE_USER' }
    const actual = approvalRequestsReducer(
      initialState,
      setUserApprovalRequestsFilters(filters)
    )
    expect(actual.userApprovalRequestFilters).toEqual(filters)
  })

  it('should handle setUserApprovalRequestSearch', () => {
    const search = {
      searchBy: ['requestType', 'createdBy'] as Array<keyof IApprovalRequest>,
      searchTerm: 'user',
    }
    const actual = approvalRequestsReducer(
      initialState,
      setUserApprovalRequestSearch(search)
    )
    expect(actual.approvalRequestSearch).toEqual(search)
  })

  it('should handle setApprovalRequestResponse', () => {
    const response: IApprovalRequestsResponse = {
      pageNumber: 3,
      pageSize: 15,
      totalNumberOfPages: 5,
      totalElements: 75,
      data: [],
    }
    const actual = approvalRequestsReducer(
      initialState,
      setApprovalRequestResponse(response)
    )
    expect(actual.approvalRequestResponse).toEqual(response)
  })

  it('should handle setRequestTypes', () => {
    const requestTypes: RequestType[] = [
      { id: '1', name: 'CREATE_USER', description: 'Create user request' },
      { id: '2', name: 'UPDATE_ROLE', description: 'Update role request' },
    ]
    const actual = approvalRequestsReducer(
      initialState,
      setRequestTypes(requestTypes)
    )
    expect(actual.requestTypes).toEqual(requestTypes)
  })

  it('should handle request types loading states', () => {
    expect(
      approvalRequestsReducer(initialState, setLoadingRequestTypes(true))
        .isRequestTypesLoading
    ).toBe(true)
    expect(
      approvalRequestsReducer(initialState, setRequestTypesSuccess(true))
        .isRequestTypesSuccess
    ).toBe(true)
  })

  it('should handle setMakerCheckerTypes', () => {
    const makerCheckerTypes: IMakerCheckerType[] = [
      {
        id: '1',
        name: 'USER_MANAGEMENT',
        description: 'User management operations',
        makerPermissions: ['CREATE_USER'],
        checkerPermissions: ['APPROVE_USER'],
      },
    ]
    const actual = approvalRequestsReducer(
      initialState,
      setMakerCheckerTypes(makerCheckerTypes)
    )
    expect(actual.makerCheckerTypes).toEqual(makerCheckerTypes)
  })

  it('should handle setApprovalActions', () => {
    const actual = approvalRequestsReducer(
      initialState,
      setApprovalActions(true)
    )
    expect(actual.approvalActions).toBe(true)
  })

  it('should handle setOpenDialog', () => {
    const actual = approvalRequestsReducer(initialState, setOpenDialog(true))
    expect(actual.openDialog).toBe(true)
  })
})
