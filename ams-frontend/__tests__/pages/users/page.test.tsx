import { render, screen } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import UsersPage from '../../../src/app/users/page'
import { useAppDispatch, useAppSelector } from '@/store'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  getUsersFilter: vi.fn(),
  getUsers: vi.fn(),
}))

// Mock the tabs components
vi.mock('@dtbx/ui/components/Tabs', () => ({
  AntTab: ({ label }: { label: React.ReactNode }) => (
    <div data-testid="ant-tab">{label}</div>
  ),
  AntTabs: ({
    children,
    value: _value,
    onChange: _onChange,
  }: {
    children: React.ReactNode
    value: number
    onChange: (event: React.SyntheticEvent, newValue: number) => void
  }) => <div data-testid="ant-tabs">{children}</div>,
  TabPanel: ({
    children,
    value,
    index,
  }: {
    children: React.ReactNode
    value: number
    index: number
  }) => (
    <div
      data-testid="tab-panel"
      style={{ display: value === index ? 'block' : 'none' }}
    >
      {children}
    </div>
  ),
}))

// Mock the inner UserPage component
vi.mock('../../../src/app/users/users/page', () => ({
  default: () => <div data-testid="inner-user-page">Inner User Page</div>,
}))

// Mock the components
vi.mock('@dtbx/ui/components', () => ({
  PageHeader: ({
    title,
    children,
  }: {
    title: string
    children: React.ReactNode
  }) => (
    <div data-testid="page-header">
      <h1>{title}</h1>
      <div>{children}</div>
    </div>
  ),
  Button: ({
    children,
    onClick,
  }: {
    children: React.ReactNode
    onClick: () => void
  }) => (
    <button data-testid="button" onClick={onClick}>
      {children}
    </button>
  ),
  SearchInput: ({ onChange }: { onChange: (value: string) => void }) => (
    <input
      data-testid="search-input"
      onChange={(e) => onChange(e.target.value)}
    />
  ),
  LoadingListsSkeleton: () => (
    <div data-testid="loading-skeleton">Loading...</div>
  ),
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}))

// Mock the EmptyUsers and ListUsers components
vi.mock('../../../src/app/users/EmptyUsers', () => ({
  default: () => <div data-testid="empty-users">No users found</div>,
}))

vi.mock('../../../src/app/users/ListUsers', () => ({
  default: () => <div data-testid="list-users">Users list</div>,
}))

vi.mock('../../../src/app/users/PageHeader', () => ({
  default: () => <div data-testid="users-page-header">Users Header</div>,
}))

describe('UsersPage', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      // Return the totalElements value that the component is looking for
      return 20
    })
  })

  it('renders the Users page with tabs', () => {
    render(<UsersPage />)

    expect(screen.getByTestId('ant-tabs')).toBeInTheDocument()
    expect(screen.getByTestId('tab-panel')).toBeInTheDocument()
  })

  it('renders the inner user page component', () => {
    render(<UsersPage />)

    expect(screen.getByTestId('inner-user-page')).toBeInTheDocument()
  })

  it('displays user count in the tab', () => {
    render(<UsersPage />)

    expect(screen.getByText('20')).toBeInTheDocument() // totalElements from mock
  })

  it('renders the empty users component when no users are available', () => {
    // Mock empty users array
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return 0 // totalElements = 0
    })

    render(<UsersPage />)

    expect(screen.getByTestId('inner-user-page')).toBeInTheDocument()
  })

  it('renders loading skeleton when isLoading is true', () => {
    // Mock loading state
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return 0 // totalElements = 0
    })

    render(<UsersPage />)

    expect(screen.getByTestId('inner-user-page')).toBeInTheDocument()
  })

  it('handles tab changes correctly', () => {
    render(<UsersPage />)

    // Should render the tabs component
    expect(screen.getByTestId('ant-tabs')).toBeInTheDocument()
  })

  it('displays correct user count in tab label', () => {
    // Mock with different user count
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return 50 // totalElements = 50
    })

    render(<UsersPage />)

    expect(screen.getByText('50')).toBeInTheDocument()
  })

  it('renders with undefined user count gracefully', () => {
    // Mock with undefined user count
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return undefined
    })

    render(<UsersPage />)

    expect(screen.getByTestId('inner-user-page')).toBeInTheDocument()
  })
})
