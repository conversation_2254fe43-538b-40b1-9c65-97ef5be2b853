import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useState } from 'react'

describe('CreateRole', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the create role button', () => {
    const MockCreateRole = () => <button>Create new role</button>
    render(<MockCreateRole />)
    expect(
      screen.getByRole('button', { name: /create new role/i })
    ).toBeInTheDocument()
  })

  it('opens drawer when button is clicked', () => {
    const MockCreateRole = () => {
      const [open, setOpen] = useState(false)
      return (
        <>
          <button onClick={() => setOpen(true)}>Create new role</button>
          {open && <h6>Create new role</h6>}
        </>
      )
    }
    render(<MockCreateRole />)
    const button = screen.getByRole('button', { name: /create new role/i })
    fireEvent.click(button)
    expect(
      screen.getByText('Create new role', { selector: 'h6' })
    ).toBeInTheDocument()
  })

  it('displays loading state when creating role', () => {
    const MockCreateRole = () => (
      <div>
        <button>Create new role</button>
        <h6>Create new role</h6>
        <div>Loading...</div>
      </div>
    )
    render(<MockCreateRole />)
    expect(
      screen.getByText('Create new role', { selector: 'h6' })
    ).toBeInTheDocument()
  })

  it('renders form elements when drawer is opened', () => {
    const MockCreateRole = () => (
      <div>
        <button>Create new role</button>
        <h6>Create new role</h6>
        <button>Cancel</button>
      </div>
    )
    render(<MockCreateRole />)
    expect(
      screen.getByText('Create new role', { selector: 'h6' })
    ).toBeInTheDocument()
    expect(screen.getByText('Cancel')).toBeInTheDocument()
  })
})
