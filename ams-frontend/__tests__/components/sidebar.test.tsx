import { describe, it, expect, vi } from 'vitest'
import { sidebarConfig } from '@/app/sidebar'

// Mock the icons
vi.mock('@dtbx/ui/icons', () => ({
  RightsIcon: () => <div data-testid="rights-icon" />,
  StaffUsersIcon: () => <div data-testid="staff-users-icon" />,
  CustomersIcon: () => <div data-testid="customers-icon" />,
  RequestsApprovalIcon: () => <div data-testid="requests-approval-icon" />,
}))

describe('Sidebar Configuration', () => {
  it('should have correct number of sidebar items', () => {
    expect(sidebarConfig).toHaveLength(4)
  })

  it('should have Users configuration', () => {
    const usersConfig = sidebarConfig.find(item => item.id === '1')
    
    expect(usersConfig).toBeDefined()
    expect(usersConfig?.title).toBe('Users')
    expect(usersConfig?.path).toBe('/users')
    expect(usersConfig?.module).toBe('users')
    expect(usersConfig?.isProductionReady).toBe(true)
  })

  it('should have Roles configuration', () => {
    const rolesConfig = sidebarConfig.find(item => item.id === '2')
    
    expect(rolesConfig).toBeDefined()
    expect(rolesConfig?.title).toBe('Roles')
    expect(rolesConfig?.path).toBe('/roles')
    expect(rolesConfig?.module).toBe('users')
    expect(rolesConfig?.isProductionReady).toBe(true)
  })

  it('should have Rights configuration', () => {
    const rightsConfig = sidebarConfig.find(item => item.id === '3')
    
    expect(rightsConfig).toBeDefined()
    expect(rightsConfig?.title).toBe('Rights')
    expect(rightsConfig?.path).toBe('/rights')
    expect(rightsConfig?.module).toBe('users')
    expect(rightsConfig?.isProductionReady).toBe(true)
  })

  it('should have Approval Requests configuration', () => {
    const approvalConfig = sidebarConfig.find(item => item.id === '4')
    
    expect(approvalConfig).toBeDefined()
    expect(approvalConfig?.title).toBe('Approval Requests')
    expect(approvalConfig?.path).toBe('/approval-requests')
    expect(approvalConfig?.module).toBe('users')
    expect(approvalConfig?.isProductionReady).toBe(true)
  })

  it('should have all items marked as production ready', () => {
    const allProductionReady = sidebarConfig.every(item => item.isProductionReady === true)
    expect(allProductionReady).toBe(true)
  })

  it('should have all items in the users module', () => {
    const allUsersModule = sidebarConfig.every(item => item.module === 'users')
    expect(allUsersModule).toBe(true)
  })

  it('should have unique IDs for all items', () => {
    const ids = sidebarConfig.map(item => item.id)
    const uniqueIds = new Set(ids)
    expect(uniqueIds.size).toBe(sidebarConfig.length)
  })

  it('should have unique paths for all items', () => {
    const paths = sidebarConfig.map(item => item.path)
    const uniquePaths = new Set(paths)
    expect(uniquePaths.size).toBe(sidebarConfig.length)
  })

  it('should have icons for all items', () => {
    sidebarConfig.forEach(item => {
      expect(item.icon).toBeDefined()
    })
  })
})
