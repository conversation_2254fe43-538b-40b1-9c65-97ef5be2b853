import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import RequestSearch from '../../../src/app/approval-requests/RequestSearch'
import { useAppDispatch, useAppSelector } from '@/store'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the setCustomerSearch action
vi.mock('@/store/reducers', () => ({
  setCustomerSearch: vi.fn((payload) => ({
    type: 'customers/setCustomerSearch',
    payload,
  })),
}))

// Mock sentenceCase
vi.mock('tiny-case', () => ({
  sentenceCase: vi.fn(
    (str) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
  ),
}))

describe('RequestSearch', () => {
  const mockDispatch = vi.fn()
  const mockOnSetSearch = vi.fn()

  const defaultProps = {
    onSetSearch: mockOnSetSearch,
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockReturnValue({
      search: {
        searchBy: ['firstName'],
        searchValue: '',
      },
    })
  })

  it('renders with default props', () => {
    render(<RequestSearch {...defaultProps} />)

    expect(screen.getByText('Maker first name')).toBeInTheDocument()
    expect(
      screen.getByPlaceholderText('Search by Maker first name')
    ).toBeInTheDocument()
  })

  it('renders with custom button variant', () => {
    render(<RequestSearch {...defaultProps} buttonVariant="contained" />)

    const button = screen.getByRole('button', { name: /maker first name/i })
    expect(button).toBeInTheDocument()
  })

  it('renders with custom search by items', () => {
    const customSearchByItems = [
      { label: 'User Name', value: 'userName' },
      { label: 'Email', value: 'email' },
    ]

    render(
      <RequestSearch {...defaultProps} searchByItems={customSearchByItems} />
    )

    expect(screen.getByText('Maker first name')).toBeInTheDocument()
  })

  it('opens dropdown when button is clicked', () => {
    render(<RequestSearch {...defaultProps} />)

    const button = screen.getByRole('button', { name: /maker first name/i })
    fireEvent.click(button)

    // Check if button has aria-expanded="true" indicating menu is open
    expect(button).toHaveAttribute('aria-expanded', 'true')
  })

  it('closes dropdown when clicking away', async () => {
    render(<RequestSearch {...defaultProps} />)

    const button = screen.getByRole('button', { name: /maker first name/i })
    fireEvent.click(button)

    expect(button).toHaveAttribute('aria-expanded', 'true')

    fireEvent.click(button)

    expect(button).not.toHaveAttribute('aria-expanded', 'true')
  })

  it('changes search by option when menu item is clicked', () => {
    const customSearchByItems = [
      { label: 'Maker First Name', value: 'firstName' },
      { label: 'Maker Last Name', value: 'lastName' },
    ]

    render(
      <RequestSearch {...defaultProps} searchByItems={customSearchByItems} />
    )

    const button = screen.getByRole('button', { name: /maker first name/i })
    fireEvent.click(button)

    const lastNameOption = screen.getByText('Maker last name')
    fireEvent.click(lastNameOption)

    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'customers/setCustomerSearch',
      payload: {
        searchBy: ['lastName'],
        searchValue: '',
      },
    })
  })

  it('updates search value when input changes', () => {
    render(<RequestSearch {...defaultProps} />)

    const input = screen.getByPlaceholderText('Search by Maker first name')
    fireEvent.change(input, { target: { value: 'John' } })

    expect(mockOnSetSearch).toHaveBeenCalledWith('John')
  })

  it('calls onSetSearch when search value changes', () => {
    render(<RequestSearch {...defaultProps} />)

    const input = screen.getByPlaceholderText('Search by Maker first name')
    fireEvent.change(input, { target: { value: 'test search' } })

    expect(mockOnSetSearch).toHaveBeenCalledWith('test search')
  })

  it('dispatches setCustomerSearch on mount', () => {
    render(<RequestSearch {...defaultProps} />)

    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'customers/setCustomerSearch',
      payload: {
        searchBy: ['firstName'],
        searchValue: '',
      },
    })
  })

  it('calls onSetSearch when search state changes', () => {
    // Clear previous calls
    mockOnSetSearch.mockClear()

    // Mock search state with value
    vi.mocked(useAppSelector).mockReturnValue({
      search: {
        searchBy: ['firstName'],
        searchValue: 'John',
      },
    })

    render(<RequestSearch {...defaultProps} />)

    const input = screen.getByPlaceholderText('Search by Maker first name')
    fireEvent.change(input, { target: { value: 'John' } })

    expect(mockOnSetSearch).toHaveBeenCalledWith('John')
  })

  it('handles empty search value', () => {
    render(<RequestSearch {...defaultProps} />)

    const input = screen.getByPlaceholderText('Search by Maker first name')

    fireEvent.change(input, { target: { value: 'test' } })

    mockOnSetSearch.mockClear()

    fireEvent.change(input, { target: { value: '' } })

    expect(mockOnSetSearch).toHaveBeenCalledWith('')
  })

  it('applies custom button styles', () => {
    const customButtonStyle = {
      border: '2px solid red',
      boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
    }

    render(<RequestSearch {...defaultProps} buttonStyle={customButtonStyle} />)

    const button = screen.getByRole('button', { name: /maker first name/i })
    expect(button).toBeInTheDocument()
  })

  it('handles keyboard navigation', () => {
    render(<RequestSearch {...defaultProps} />)

    const button = screen.getByRole('button', { name: /maker first name/i })

    fireEvent.click(button)

    const menu = screen.queryByRole('menu')
    if (menu) {
      expect(menu).toBeInTheDocument()
      fireEvent.keyDown(button, { key: 'Escape', code: 'Escape' })
    } else {
      expect(button).toBeInTheDocument()
    }
  })

  it('maintains search value state correctly', () => {
    render(<RequestSearch {...defaultProps} />)

    const input = screen.getByPlaceholderText('Search by Maker first name')

    fireEvent.change(input, { target: { value: 'John' } })
    expect(input).toHaveValue('John')

    fireEvent.change(input, { target: { value: 'Jane' } })
    expect(input).toHaveValue('Jane')
  })

  it('updates placeholder when search by option changes', () => {
    const customSearchByItems = [
      { label: 'Maker First Name', value: 'firstName' },
      { label: 'Maker Last Name', value: 'lastName' },
    ]

    render(
      <RequestSearch {...defaultProps} searchByItems={customSearchByItems} />
    )

    const button = screen.getByRole('button', { name: /maker first name/i })
    fireEvent.click(button)

    const lastNameOption = screen.getByText('Maker last name')
    fireEvent.click(lastNameOption)

    expect(
      screen.getByPlaceholderText('Search by Maker last name')
    ).toBeInTheDocument()
  })

  it('handles multiple search by options', () => {
    const multipleSearchByItems = [
      { label: 'First Name', value: 'firstName' },
      { label: 'Last Name', value: 'lastName' },
      { label: 'Email', value: 'email' },
      { label: 'Phone', value: 'phone' },
    ]

    render(
      <RequestSearch {...defaultProps} searchByItems={multipleSearchByItems} />
    )

    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(screen.getByText('First name')).toBeInTheDocument()
    expect(screen.getByText('Last name')).toBeInTheDocument()
    expect(screen.getByText('Email')).toBeInTheDocument()
    expect(screen.getByText('Phone')).toBeInTheDocument()
  })

  it('handles search by option selection correctly', () => {
    const customSearchByItems = [
      { label: 'Email Address', value: 'email' },
      { label: 'Phone Number', value: 'phone' },
    ]

    render(
      <RequestSearch {...defaultProps} searchByItems={customSearchByItems} />
    )

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const emailOption = screen.getByText('Email address')
    fireEvent.click(emailOption)

    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'customers/setCustomerSearch',
      payload: {
        searchBy: ['email'],
        searchValue: '',
      },
    })

    expect(screen.getAllByText('Email address')).toHaveLength(2)
  })

  it('preserves search value when changing search by option', () => {
    const customSearchByItems = [
      { label: 'First Name', value: 'firstName' },
      { label: 'Last Name', value: 'lastName' },
    ]

    render(
      <RequestSearch {...defaultProps} searchByItems={customSearchByItems} />
    )

    const input = screen.getByPlaceholderText('Search by Maker first name')
    fireEvent.change(input, { target: { value: 'John' } })

    const button = screen.getByRole('button')
    fireEvent.click(button)

    const lastNameOption = screen.getByText('Last name')
    fireEvent.click(lastNameOption)

    expect(mockDispatch).toHaveBeenCalledWith({
      type: 'customers/setCustomerSearch',
      payload: {
        searchBy: ['lastName'],
        searchValue: 'John',
      },
    })
  })
})
