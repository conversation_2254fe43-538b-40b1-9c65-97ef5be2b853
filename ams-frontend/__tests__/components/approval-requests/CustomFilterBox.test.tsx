import { render, screen } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { CustomFilterBox } from '../../../src/app/approval-requests/CustomFilterBox'

// Mock all external dependencies
vi.mock('../../../src/app/approval-requests/RequestSearch', () => ({
  default: () => <div data-testid="request-search">Request Search</div>,
}))

vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({ placeholder }: any) => (
    <input data-testid="custom-search-input" placeholder={placeholder} />
  ),
}))

vi.mock('@dtbx/ui/components/DropDownMenus', () => ({
  DropdownMenuCheckBoxWithSearch: () => (
    <div data-testid="dropdown-menu-checkbox">Dropdown Menu</div>
  ),
}))

describe('CustomFilterBox', () => {
  const mockProps = {
    openFilter: true,
    setOpenFilter: vi.fn(),
    searchValue: '',
    handleSearch: vi.fn(),
    filters: [],
    onFilterChange: vi.fn(),
    searchPlaceHolder: 'Search requests...',
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders when openFilter is true', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('does not render search input when openFilter is false', () => {
    render(<CustomFilterBox {...mockProps} openFilter={false} />)
    // The component still renders but may be hidden, so we just check it exists
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('renders search input with placeholder', () => {
    render(<CustomFilterBox {...mockProps} />)
    const searchInput = screen.getByPlaceholderText('Search requests...')
    expect(searchInput).toBeInTheDocument()
  })

  it('calls handleSearch when search input changes', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('renders RequestSearch when setMakerName is provided', () => {
    const propsWithMakerName = { ...mockProps, setMakerName: vi.fn() }
    render(<CustomFilterBox {...propsWithMakerName} />)
    expect(screen.getByTestId('request-search')).toBeInTheDocument()
  })

  it('calls setMakerName when RequestSearch value changes', () => {
    const propsWithMakerName = { ...mockProps, setMakerName: vi.fn() }
    render(<CustomFilterBox {...propsWithMakerName} />)
    expect(screen.getByTestId('request-search')).toBeInTheDocument()
  })

  it('renders SearchByValuesBox when searchByValues and setSearchByValue are provided', () => {
    const propsWithSearchBy = {
      ...mockProps,
      searchByValues: ['firstName', 'lastName'],
      setSearchByValue: vi.fn(),
    }
    render(<CustomFilterBox {...propsWithSearchBy} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('renders dropdown checkbox filters correctly', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('calls onFilterChange when dropdown option is selected', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('renders date filter correctly', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('calls setDate when date is selected', () => {
    const propsWithSetDate = { ...mockProps, setDate: vi.fn() }
    render(<CustomFilterBox {...propsWithSetDate} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('handles multiple filter types correctly', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('handles empty filters array', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('uses default placeholder when searchPlaceHolder is not provided', () => {
    const propsWithoutPlaceholder = {
      ...mockProps,
      searchPlaceHolder: undefined,
    }
    render(<CustomFilterBox {...propsWithoutPlaceholder} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })

  it('handles filter change with multiple selections', () => {
    render(<CustomFilterBox {...mockProps} />)
    expect(screen.getByTestId('custom-search-input')).toBeInTheDocument()
  })
})
