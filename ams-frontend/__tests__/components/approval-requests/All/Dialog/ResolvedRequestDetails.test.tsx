import { render, screen, fireEvent, waitFor } from '../../../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import ResolvedRequestDetails from '../../../../../src/app/approval-requests/All/Dialog/ResolvedRequestDetails'
import { useAppDispatch } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock useCustomRouter
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    pushWithTrailingSlash: vi.fn(),
  }),
}))

// Mock ApprovalRequestRouting
vi.mock('../../../../../src/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn(),
}))

// Mock formatTimestamp utility
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
}))

// Mock sentenceCase
vi.mock('tiny-case', () => ({
  sentenceCase: vi.fn((str) => {
    if (!str) return ''
    return str
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, (l: string) => l.toUpperCase())
  }),
}))

describe('ResolvedRequestDetails', () => {
  const mockDispatch = vi.fn()

  const mockRequest: IApprovalRequest = {
    id: '123',
    maker: 'John Doe',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-01T10:00:00Z',
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_USERS'],
      description: 'Create user request',
      makerPermissions: ['CREATE_USERS'],
      module: 'users',
      name: 'Create User',
      overridePermissions: [],
      type: 'CREATE_USER',
    },
    entityId: 'user-123',
    entity: 'User',
    diff: [
      {
        field: 'firstName',
        oldValue: '',
        newValue: 'John',
      },
    ],
    makerComments: 'Creating new user for the team',
    status: 'APPROVED',
    checker: 'Jane Smith',
    checkerComments: 'Approved successfully',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the menu item trigger', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    expect(screen.getByText('See request summary')).toBeInTheDocument()
  })

  it('opens dialog when menu item is clicked', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByText('Approval request details')).toBeInTheDocument()
  })

  it('displays request details in dialog', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('Create User')).toBeInTheDocument()
    expect(screen.getByDisplayValue('users')).toBeInTheDocument()
    expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument()
    expect(
      screen.getAllByDisplayValue('Formatted: 2023-01-01T10:00:00Z')
    ).toHaveLength(2)
  })

  it('displays maker comments', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(
      screen.getByDisplayValue('Creating new user for the team')
    ).toBeInTheDocument()
  })

  it('displays checker information when available', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('Jane Smith')).toBeInTheDocument()
    expect(
      screen.getByDisplayValue('Approved successfully')
    ).toBeInTheDocument()
  })

  it('handles request without type gracefully', () => {
    const requestWithoutType: IApprovalRequest = {
      ...mockRequest,
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        type: '',
      },
    }

    render(<ResolvedRequestDetails request={requestWithoutType} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('No type')).toBeInTheDocument()
  })

  it('handles request without checker comments', () => {
    const requestWithoutCheckerComments: IApprovalRequest = {
      ...mockRequest,
      checkerComments: undefined,
    }

    render(<ResolvedRequestDetails request={requestWithoutCheckerComments} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('No comment')).toBeInTheDocument()
  })

  it('handles request without maker comments', () => {
    const requestWithoutMakerComments: IApprovalRequest = {
      ...mockRequest,
      makerComments: undefined,
    }

    render(<ResolvedRequestDetails request={requestWithoutMakerComments} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('No comment')).toBeInTheDocument()
  })

  it('closes dialog when close button is clicked', async () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    // Find the close button by its icon
    const closeButton = screen.getByTestId('CloseRoundedIcon').closest('button')
    fireEvent.click(closeButton!)

    await waitFor(() => {
      expect(
        screen.queryByText('Approval request details')
      ).not.toBeInTheDocument()
    })
  })

  it('closes dialog when Back button is clicked', async () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    await waitFor(() => {
      expect(
        screen.queryByText('Approval request details')
      ).not.toBeInTheDocument()
    })
  })

  it('navigates to module when "Go to module" button is clicked', async () => {
    const { ApprovalRequestRouting } = await import(
      '../../../../../src/app/approval-requests/RequestRouting'
    )

    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const goToModuleButton = screen.getByText('Go to module')
    fireEvent.click(goToModuleButton)

    expect(ApprovalRequestRouting).toHaveBeenCalledWith(
      mockRequest,
      mockDispatch,
      expect.any(Object)
    )

    await waitFor(() => {
      expect(
        screen.queryByText('Approval request details')
      ).not.toBeInTheDocument()
    })
  })

  it('prevents dialog close on backdrop click', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const dialog = screen.getByRole('dialog')
    fireEvent.click(dialog)

    // Dialog should still be open
    expect(screen.getByText('Approval request details')).toBeInTheDocument()
  })

  it('displays all form fields correctly', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    // Check all expected fields are present
    expect(screen.getByLabelText('Approval request type')).toBeInTheDocument()
    expect(screen.getByLabelText('Module')).toBeInTheDocument()
    expect(screen.getByLabelText('Maker')).toBeInTheDocument()
    expect(screen.getByLabelText('Maker timestamp')).toBeInTheDocument()
    expect(screen.getByLabelText('Maker comment')).toBeInTheDocument()
    expect(screen.getByLabelText('Checker')).toBeInTheDocument()
    expect(screen.getByLabelText('Checker comment')).toBeInTheDocument()
  })

  it('renders RequestsApprovalIcon in dialog header', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const dialog = screen.getByRole('dialog')
    expect(dialog).toBeInTheDocument()
  })

  it('handles different request statuses', () => {
    const rejectedRequest: IApprovalRequest = {
      ...mockRequest,
      status: 'REJECTED',
      checkerComments: 'Request rejected due to insufficient information',
    }

    render(<ResolvedRequestDetails request={rejectedRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(
      screen.getByDisplayValue(
        'Request rejected due to insufficient information'
      )
    ).toBeInTheDocument()
  })

  it('handles requests with different modules', () => {
    const roleRequest: IApprovalRequest = {
      ...mockRequest,
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        module: 'groups',
        type: 'CREATE_ROLE',
        name: 'Create Role',
      },
    }

    render(<ResolvedRequestDetails request={roleRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('Create Role')).toBeInTheDocument()
    expect(screen.getByDisplayValue('groups')).toBeInTheDocument()
  })

  it('handles keyboard interactions', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')

    fireEvent.click(menuItem)
    expect(screen.getByText('Approval request details')).toBeInTheDocument()

    const dialog = screen.getByRole('dialog')
    fireEvent.keyDown(dialog, { key: 'Escape', code: 'Escape' })
  })

  it('maintains dialog state correctly', () => {
    render(<ResolvedRequestDetails request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')

    fireEvent.click(menuItem)
    expect(screen.getByText('Approval request details')).toBeInTheDocument()

    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    fireEvent.click(menuItem)
    expect(screen.getByText('Approval request details')).toBeInTheDocument()
  })
})
