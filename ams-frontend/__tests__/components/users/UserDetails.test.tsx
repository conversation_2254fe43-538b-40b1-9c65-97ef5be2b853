import { render, screen } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { UserDetails } from '@/app/users/details/UserDetails/UserDetails'
import { useAppDispatch, useAppSelector } from '@/store'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  getUserById: vi.fn(),
}))

// Mock the useParams hook
vi.mock('next/navigation', async () => {
  const actual = await vi.importActual('next/navigation')
  return {
    ...actual,
    useParams: () => ({ id: '123' }),
    useRouter: () => ({
      push: vi.fn(),
      back: vi.fn(),
    }),
  }
})

// Mock the UI components
vi.mock('@dtbx/ui/components', () => ({
  PageHeader: ({
    title,
    children,
  }: {
    title: string
    children: React.ReactNode
  }) => (
    <div data-testid="page-header">
      <h1>{title}</h1>
      <div>{children}</div>
    </div>
  ),
  Button: ({
    children,
    onClick,
  }: {
    children: React.ReactNode
    onClick: () => void
  }) => (
    <button data-testid="button" onClick={onClick}>
      {children}
    </button>
  ),
  LoadingDetailsSkeleton: () => (
    <div data-testid="loading-skeleton">Loading...</div>
  ),
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  StatusChip: ({ status }: { status: string }) => (
    <div data-testid="status-chip">{status}</div>
  ),
}))

describe('UserDetails', () => {
  const mockDispatch = vi.fn()
  const mockUser = {
    id: '123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'ACTIVE',
    createdAt: '2023-01-01',
    roles: [{ id: 'role1', name: 'Admin' }],
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      // Return the specific values the component needs
      return {
        selectedApprovalRequest: {
          status: 'PENDING',
          makerCheckerType: {
            type: 'CREATE_USERS',
          },
          diff: [
            { field: 'firstName', newValue: 'John' },
            { field: 'lastName', newValue: 'Doe' },
          ],
          maker: 'maker123',
        },
        singleUserData: mockUser,
        isLoadingEditUser: false,
      }
    })
  })

  it('renders the user details when data is loaded', () => {
    render(<UserDetails />)

    // Use getAllByText since the name appears in multiple places (breadcrumb and main content)
    const nameElements = screen.getAllByText('John Doe')
    expect(nameElements.length).toBeGreaterThan(0)

    // Check for the status chip that is actually rendered
    expect(screen.getByText('Active')).toBeInTheDocument()
  })

  it('renders loading buttons when isLoadingEditUser is true', () => {
    // Mock loading state
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        selectedApprovalRequest: {
          status: 'PENDING',
          makerCheckerType: {
            type: 'CREATE_USERS',
          },
          diff: [
            { field: 'firstName', newValue: 'John' },
            { field: 'lastName', newValue: 'Doe' },
          ],
          maker: 'maker123',
        },
        singleUserData: mockUser,
        isLoadingEditUser: true,
      }
    })

    render(<UserDetails />)

    // Should show loading buttons instead of approve/reject
    expect(screen.queryByText('Approve')).not.toBeInTheDocument()
    expect(screen.queryByText('Reject')).not.toBeInTheDocument()
  })

  it('displays user status correctly', () => {
    render(<UserDetails />)

    // Status is displayed as a chip
    expect(screen.getByText('Active')).toBeInTheDocument()
  })

  it('shows approval request details button when status is pending', () => {
    render(<UserDetails />)

    expect(
      screen.getByText('View Approval Request Details')
    ).toBeInTheDocument()
  })
})
