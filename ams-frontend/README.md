## DTBX Backoffice Access Management

The DTBX Backoffice includes a robust access management system to ensure secure and controlled interactions across the platform. The system is designed around the following core elements:

- Users
- Roles
- Rights
- Approval Requests

The DTBX Backoffice Access Management system ensures that users have the appropriate level of access, fostering a secure and efficient working environment while supporting the scalability of the platform.

## Overview

This project is one of the applications within a monorepo that also includes:

- landing : A Next.js app.
- lms : A Next.js app.
- x247 : A Next.js app.
- eatta : A Next.js app.
- Shared libraries

  - @dtbx/ui: React component library.
  - @dtbx/store: Redux store.
  - @dtbx/eslint-config: ESLint configurations.
  - @dtbx/typescript-config: TypeScript configurations.
  - @dtbx/vitest-config: Base configuration for Vitest.
  - @dtbx/e2e: Cypress setup for end-to-end testing.

## Features

- Framework: Built with Next.js.
- TypeScript: Ensures static type checking for reliability.
- Reusable Components: Shared UI components from @dtbx/ui.
- Redux Integration: Centralized state management using @dtbx/store.
- Code Quality Tools:
  - Linting: ESLint.
  - Formatting: Prettier.
  - Testing: Vitest and Cypress.

## Setup and Development

1. Prerequisites

   Ensure you have the following installed:

   - Node.js (v14 or later)
   - pnpm (preferred package manager)
   - Docker (optional for containerized development)

2. Install Dependencies
   Run the following commands from the monorepo’s root directory:
   ```
   cd backoffice-portal-microfrontend
   pnpm dev --filter=access-management
   ```
3. Development Environment

   Create a .env.local file in the apps/landing directory and populate it with environment variables required for the application.

   Start the development server:

   ```
   cd backoffice-portal-microfrontend
   pnpm dev --filter=access-management
   ```

4. Building the Application

   Build the landing page for production:

   ```
   pnpm build --filter=access-management
   ```

   ## Docker Support

   Each app in this monorepo includes a Dockerfile. To build and run the Landing Page app using Docker:

   1. Build the Docker image:
      ```
      docker build -t dtbx-access-management -f apps/ams/Dockerfile .
      ```
   2. Run the Docker container:
      ```
      docker run -p 3000:3000 dtbx-landing
      ```
      Alternatively, use the provided Docker Compose setup for all applications:
      ```
      docker compose -f docker/local/compose.yml up -d
      ```

   ## Useful Links

- [Next.js Documentation](https://nextjs.org/docs)
- [Turborepo Documentation](https://turbo.build/repo/docs)
- [ESLint](https://eslint.org/docs/latest)
- [Prettier](https://prettier.io)
- [Vitest](https://vitest.dev)
- [Cypress](https://www.cypress.io)
