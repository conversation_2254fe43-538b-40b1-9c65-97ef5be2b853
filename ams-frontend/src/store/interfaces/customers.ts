import { Dispatch } from 'redux'

export interface ICustomersFilter {
  dateCreatedFrom?: string
  dateCreatedTo?: string
  size: number
  page: number
  firstName?: string
  otherNames?: string
  lastName?: string
  phoneNumber?: string
  email?: string
  nationality?: string
  idNumber?: string
  accountNumber?: string
}

export interface IPendingCustomersFilter {
  size?: number
  page?: number
  module?: string
  requestType?: string
  status?: string
  startDate?: string
  endDate?: string
  maker?: string | string[]
  makerSearchType?: string
}

export interface ISetCustomerSearch {
  searchBy: string[]
  searchValue: string
}

export interface IGetCustomersRespons {
  data: ICustomer[]
  totalElements: number
  totalPages: number
  size: number
  page: number
}

export interface IGetCustomerResponse {
  data: ICustomer
}

export interface CustomerErrorResponse {
  status: number
  message: string
  error: string
}

export interface ICustomersDataResponse {
  data: ICustomer[]
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
}

export interface ICustomer {
  id?: string
  blockReason: string
  country: string
  firstName: string
  otherNames: string
  lastName: string
  phoneNumber: string
  accountNumber?: string
  email: string
  nationality: string
  idNumber: string
  idType: string
  onboardingType: string
  isBlocked: boolean
  dateCreated: string
  dateModified: string
  sex: string
  storeOfValues: StoreOfValue[]
  profileAccountStoreIds: IprofileAccountStoreIds[]
}
export interface ICustomerAccountLink {
  accounts: ICustomerAccount[]
}
export interface ICustomerCreate {
  blockReason: string
  country: string
  firstName: string
  branchCode: string
  otherNames: string
  lastName: string
  phoneNumber: string
  email: string
  nationality: string
  idNumber: string
  idType: string
  onboardingType: string
  isBlocked: boolean
  customerAccounts: ICustomerAccount[]
}
export interface StoreOfValue {
  storeCode: string
  customerId: string
}

export interface IprofileAccountStoreIds {
  profileId: string
  storeCode: string
  description: string
  customerId: string
}

export interface IDevicesResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IDevice[]
}

export interface IDevice {
  deviceId: string
  deviceType: string
  deviceName: string
  deviceStatus: string
  deviceModel: string
  uuid: string
  dateCreated: Date
  devicePlatform: string
  phoneNumber: string
}

export interface ICustomerAccount {
  accNumber: string
  accOpenDate: string
  customerType: string
  customerCategory: string
  accBranchCode: string
  accClass: string
  accClassDesc: string
  accCurrency: string
  accDormant: string
  accStatus: string
  accRecordStatus: string
  accStatBlock: string
  accFrozen: string
  accNoDebit: string
  accNoCredit: string
  accStopPay: string
  jointAccIndicator: string
  customerRecordStatus: string
  accountClass: string | null
  isMobileLinkable: boolean
  tariffName?: string
}
export interface ICustomerProfileAccount {
  profileId: string
  id: {
    storeCode: string
    description: string
    profileId: string
    accountType: string
    accountNo: string
  }
  profile: {
    id: string
    firstName: string
    otherNames: string
    lastName: string
    email: string
    phoneNumber: string
    dateCreated: string
    dateModified: string
  }
  storeCode: string
  description: string
  accountNo: string
  accountType: string
  mandate: string
  isMobileLinked: boolean
  fullName: string
  shortName: string
  branchCode: string
  currency: string
  isDormant: boolean
  isBlocked: boolean
  isFrozen: boolean
  isNoDebit: boolean
  isNoCredit: boolean
  isStopPay: boolean
  status: string
  tariffName: string
  dateCreated: string
  dateModified: string
}

export interface IUpdateCustomerDetails {
  email: string
  profileID: string
  dispatch: Dispatch
}

export interface IApproveRejectCustomerUpdate {
  approvalID: string
  comments: string
  dispatch: Dispatch
}
export interface ICustomerAccountEventHistory {
  type: string
  maker: string
  makerTimestamp: string
  checker: string
  checkerTimestamp: string
}

export interface ICustomerAccountDetails {
  firstName: string
  lastName: string
  email: string
  phoneNumber: string
  postalAddress: string
  gender: string
  idType: string
  idValue: string
  cif: string
  physicalAddress: string
  country: string
  customerPrefix: string
  dateOfBirth: string
  nationality: string
  customerCategory: string
  customerType: string
  customerAccounts: ICustomerAccount[]
  comments: string | null
}
export interface ICustomerAccountHistoryLogs {
  id: string
  event: string
  eventSource: string
  eventDate: string
}

export interface ILogs {
  id: string
  event: string
  eventSource: string
  eventDate: string
}

export interface ICustomerPinReset {
  profileID?: string
  comments?: string
  dispatch: Dispatch
  pinType?: string
}

export interface IApproveCustomerPinReset extends ICustomerPinReset {
  approvalID?: string
  type: string
}

export interface ICustomerPinDetails {
  profileId: string
  status: string
  type: string
  attempts: number
  dateFirstCreated: string
  dateLastChanged: string
}

// Interfaces for the parameters for restricting an account
export interface IRestrictAccountParams {
  profileId: string
  accountNo: string
  comments: string
  dispatch: Dispatch
}

export interface IAcceptRejectRestrictAccountApprovals {
  approvalId: string
  // accountNo: string
  comments: string
  dispatch: Dispatch
}
// Interfaces for Activate customer profile
export interface IActivateCustomerProfile {
  profileId: string
  comments: string
  dispatch: Dispatch
}

export interface IApproveRejectCustomerProfileActivation {
  approvalId: string
  comments: string
  dispatch: Dispatch
}
export interface ICustomerPinLog {
  id: string
  event: string
  eventSource: string
  eventDate: string
  deviceId?: string
}
export interface ICustomerPinLogResponse {
  data: ICustomerPinLog[]
  totalElements: number
  totalNumberOfPages: number
  pageNumber: number
  pageSize: number
}

export interface IGetCustomerDevicesParams {
  profileID: string
  dateCreatedFrom?: string
  dateCreatedTo?: string
  status?: string
  deviceType?: string
  deviceId?: string
  page: number
  size: number
}

export interface IGetCustomerDevicesProps {
  params: IGetCustomerDevicesParams
  dispatch: Dispatch
}

export interface IGetCustomerDeviceDetail {
  profileID: string
  deviceID: string
  dispatch: Dispatch
}

export interface IDeactivateCustomerDeviceParams
  extends IGetCustomerDeviceDetail {
  comments: string
}
export interface IRejectCustomerDeviceParams {
  approvalId: string
  comments: string
  dispatch: Dispatch
}
export interface IDeactivateCustomer {
  profileID: string
  reason: string
  dispatch: Dispatch
}
export interface IDeactivateCustomerApprovals {
  approvalID: string
  comments: string
  dispatch: Dispatch
}
export interface IFetchCustomerAccount {
  account: string
  dispatch: Dispatch
}
export interface ICreateCustomerAccount {
  account: ICustomerAccountDetails
  dispatch: Dispatch
  setOpen?: (val: boolean) => void
  setStep?: (val: string) => void
}
export interface ICreateCustomerApprovals {
  approvalId: string
  comments: string
  dispatch: Dispatch
}
export interface IDeactivateCustomer {
  profileID: string
  reason: string
  dispatch: Dispatch
}
export interface IDeactivateCustomerApprovals {
  approvalID: string
  comments: string
  dispatch: Dispatch
}

export interface ICreateCustomerApprovals {
  approvalId: string
  comments: string
  dispatch: Dispatch
}
export interface ICreateCustomerDeactivate {
  approvalId: string
  comments: string
  accountNo: string
  profileId: string
  dispatch: Dispatch
}
export interface IAccountLinkingCompletion {
  approvalId: string
  comments: string
  profileId: string
  dispatch: Dispatch
  action: 'approve' | 'reject'
}
export interface IAccountActivation {
  accountNo: string
  profileId: string
  comments: string
  setOpen: (open: boolean) => void
  setIsLoading: (loading: boolean) => void
  dispatch: Dispatch
}

export interface INotificationEventSettings {
  id: string
  notificationType: string
  deliveryMode: string
  templateName: string | null
}

export interface INotificationEventSubscribers {
  id: string
  recipient: string
  deliveryMode: string
}
export interface INotificationEventTemplates {
  id: string
  templateName: string
  templateSubject: string
  templateContent: string
  templateDescription: string
  htmlContent: boolean
}

export interface INotificationEvents {
  id: string
  eventType: string
  eventName: string
  settings: INotificationEventSettings[]
  subscribers: INotificationEventSubscribers[]
  placeHolders: string[]
  templates: INotificationEventTemplates[]
}
export interface INotificationFrequencies {
  id: string
  name: string
  interval: number
  frequencyType: string
  description: string
}

export interface INotificationEventsPayload {
  eventId: string
  profileId: string
  accountSource: string
  branchCode: string
  accountId: string
  frequencyId: string
  thresholdAmount: string
  subscribers: INotificationEventSubscriberPayload[]
  comments: string
}

export interface INotificationEventSubscriberPayload {
  recipients: string[]
  deliveryMode: string
  name: string
  recipientType: string
}

export interface INotificationEventType {
  id: string
  eventType: string
  eventName: string
  platform: string | null
}

export interface INotificationEventsPerAccount {
  accountSource: string
  accountId: string
  alertFrequency: INotificationFrequencies
  thresholdAmount: number
  optedInDate: string
  status: string
  numberOfSends: number
  event: INotificationEventType
  subscribers: INotificationEventSubscribers[]
}
