export interface IApprovalRequest {
  checker?: string
  checkerComments?: string
  id: string
  maker: string
  dateCreated: string
  dateModified: string
  makerCheckerType: {
    channel: string
    checkerPermissions: string[]
    description?: string
    makerPermissions: string[]
    module: string
    name: string
    overridePermissions: string[]
    type: string
  }
  entityId?: string
  entity?: string
  diff: IDiffValues[]
  makerComments?: string
  status: string
}
export interface IApprovalRequestsResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IApprovalRequest[]
}
export interface IProfileApprovalRequests {
  requests: IApprovalRequest[]
  profileId: string
}
export interface IDiffValues {
  field: string
  name?: string
  oldValue: IDiffValues[] | string
  newValue: IDiffValues[] | string
}

export interface IApprovalPermission {
  approvalRequest: null | string
  createdBy: null | string
  dateCreated: null | string
  dateModified: null | string
  id: string
  module: {
    moduleName: string
  }
  name: string
  permissionId: string
  updatedBy: null | string
  visible: boolean
  field: string
  oldValue: IDiffValues[] | string
  newValue: IDiffValues[] | string
}

export interface RequestType {
  name: string
  id: string
  description: string
}

export interface IMakerCheckerType {
  name: string
  id: string
  description: string
  makerPermissions: IMakerCheckerTypePermission[]
  checkerPermissions: IMakerCheckerTypePermission[]
}

export interface IMakerCheckerTypePermission {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  updatedBy: string
  approvalRequest: string
  name: string
  permissionId: string
  visible: boolean
  module: IMakerCheckerTypeModule
}
export interface IMakerCheckerTypeModule {
  id: string
  dateCreated: string
  dateModified: string
  createdBy: string
  updatedBy: string
  approvalRequest: string
  moduleName: string
}
