import { Dispatch } from 'redux'

import {
  setGeneratedUserReportFailure,
  setGeneratedUserReportLoading,
  setGeneratedUserReportSuccess,
  setIsLoadingCreateUser,
  setIsLoadingEditUser,
  setIsLoadingUsers,
  setLoadedADUserDetails,
  setLoadingADFailure,
  setLoadingADSuccess,
  setLoadingADUserDetails,
  setRoleUsers,
  setSingleUserData,
  setUsersResponse,
  setIsLoadingLoanProducts,
  setLoanProducts,
  setLoanProductsSummary,
  setPlatforms,
  setIsLoadingPlatforms,
  setPlatformsSummary,
  setUserProducts,
} from '../reducers'
import { setNotification } from '@dtbx/store/reducers'

import { getApprovals } from './approvalRequests'
import { refreshToken } from '@dtbx/store/actions'
import { secureapi, secureapi2, downloadBlob } from '@dtbx/store/utils'
import {
  FileFormat,
  ICheckUser,
  I<PERSON><PERSON><PERSON><PERSON>,
  IUpdate<PERSON>ser,
  <PERSON>ecode<PERSON>oken,
  ILoanProduct,
  ILoanRequestsSummary,
} from '../interfaces'
import { jwtDecode } from 'jwt-decode'

export const getUsers = async (
  dispatch: Dispatch,
  params?: {
    firstName?: string
    lastName?: string
    email?: string
    status?: string
    phoneNumber?: string
    size?: number
    page?: number
    roleIds?: string[]
    dateCreatedFrom?: string
    dateCreatedTo?: string
    lastLoginDateFrom?: string
    lastLoginDateTo?: string
  }
) => {
  if (params) {
    const {
      firstName,
      lastName,
      email,
      status,
      phoneNumber,
      size,
      page,
      roleIds,
      dateCreatedFrom,
      dateCreatedTo,
      lastLoginDateFrom,
      lastLoginDateTo,
    } = params
    dispatch(setIsLoadingUsers(true))
    try {
      const paramString = [
        `page=${page ?? 1}`,
        `size=${size ?? 10}`,
        firstName && `firstName=${firstName}`,
        lastName && `lastName=${lastName}`,
        email && `email=${email}`,
        status && `status=${status}`,
        phoneNumber && `phoneNumber=${phoneNumber}`,
        dateCreatedFrom && `dateCreatedFrom=${dateCreatedFrom}`,
        dateCreatedTo && `dateCreatedTo=${dateCreatedTo}`,
        lastLoginDateFrom && `lastLoginDateFrom=${lastLoginDateFrom}`,
        lastLoginDateTo && `lastLoginDateTo=${lastLoginDateTo}`,
        roleIds && roleIds.length > 0 && `roleIds=${roleIds.join('&roleIds=')}`,
      ].filter(Boolean) // filter out any undefined or false values

      const url = '/backoffice-auth/users?' + paramString.join('&')

      const res = await secureapi.get(url)
      dispatch(setUsersResponse(res.data))
      dispatch(setIsLoadingUsers(false))
    } catch (e) {
      const message = (e as Error).message
      dispatch(
        setNotification({
          message: message,
          type: 'error',
        })
      )
      dispatch(setIsLoadingUsers(false))
    }
  }
}
export const getUsersByRoleId = async (
  dispatch: Dispatch,
  roleId: string,
  page: number,
  size: number,
  params?: string
) => {
  dispatch(setIsLoadingUsers(true))
  try {
    const res = await secureapi.get(
      `/backoffice-auth/roles/${roleId}/users?page=${page}&size=${size}${params ? params : ''}`
    )
    dispatch(setRoleUsers(res?.data))
    dispatch(setIsLoadingUsers(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingUsers(false))
  }
}
export const getUserById = async (dispatch: Dispatch, userId: string) => {
  try {
    const res = await secureapi.get(`/backoffice-auth/users/${userId}`)
    dispatch(setSingleUserData(res.data))
  } catch (e) {
    console.error('Error fetching user by id', e)
  }
}
//Creation apis
export const createUser = async (data: ICreateUser, dispatch: Dispatch) => {
  dispatch(setIsLoadingCreateUser(true))
  try {
    await secureapi.post('/backoffice-auth/users', data)
    dispatch(setIsLoadingCreateUser(false))
    dispatch(
      setNotification({
        message: 'User was Successfully Created',
        type: 'success',
      })
    )
    await getUsers(dispatch, {
      size: 10,
      page: 1,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingCreateUser(false))
  }
}
export const makeCreateUser = async (data: ICreateUser, dispatch: Dispatch) => {
  dispatch(setIsLoadingCreateUser(true))
  try {
    await secureapi.post('/backoffice-auth/users/make', data)
    dispatch(setIsLoadingCreateUser(false))
    dispatch(
      setNotification({
        message: 'User was Successfully Created awaiting approval',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingCreateUser(false))
  }
}
export const approveCreateUser = async (
  approvalId: string,
  comments: string,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    await secureapi.put(`/backoffice-auth/users/approve/${approvalId}`, {
      comments,
    })
    dispatch(setIsLoadingEditUser(false))
    dispatch(
      setNotification({
        message: 'Create staff user request has been approved.',
        type: 'success',
      })
    )
    await getApprovals(dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}

export const rejectCreateUser = async (
  approvalId: string,
  comments: string,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    await secureapi.put(`/backoffice-auth/users/reject/${approvalId}`, {
      comments,
    })
    dispatch(setIsLoadingEditUser(false))
    dispatch(
      setNotification({
        message: 'Create staff user request has been rejected.',
        type: 'success',
      })
    )
    await getApprovals(dispatch)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}

// updating apis

export const updateUser = async (
  userId: string,
  data: IUpdateUser,
  dispatch: Dispatch,
  isActiveUserRoleUpdate: boolean
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    await secureapi.put(`/backoffice-auth/users/${userId}`, data)
    dispatch(setIsLoadingEditUser(false))
    if (isActiveUserRoleUpdate) {
      await refreshToken()
    }
    dispatch(
      setNotification({
        message: 'User was Successfully Updated',
        type: 'success',
      })
    )
    await getUsers(dispatch, {
      page: 1,
      size: 10,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}
export const makeUpdateUser = async (
  userId: string,
  data: IUpdateUser,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    await secureapi.put(`/backoffice-auth/users/${userId}/make`, data)
    dispatch(setIsLoadingEditUser(false))
    dispatch(
      setNotification({
        message: 'User was Successfully Updated pending approval',
        type: 'success',
      })
    )
    await getUsers(dispatch, {
      page: 1,
      size: 10,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}
export const approveUpdateUser = async (
  userId: string,
  data: ICheckUser,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    await secureapi.put(`/backoffice-auth/users/${userId}/approve`, data)
    dispatch(setIsLoadingEditUser(false))
    dispatch(
      setNotification({
        message: 'User update was successfully approved',
        type: 'success',
      })
    )
    await getUsers(dispatch, {
      page: 1,
      size: 10,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}
export const rejectUpdateUser = async (
  userId: string,
  data: ICheckUser,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    await secureapi.put(`/backoffice-auth/users/${userId}/reject`, data)
    dispatch(setIsLoadingEditUser(false))
    dispatch(
      setNotification({
        message: 'User update was successfully rejected',
        type: 'success',
      })
    )
    await getUsers(dispatch, {
      page: 1,
      size: 10,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}
//activation apis

export const activateUser = async (
  userId: string,
  action: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    const url =
      action === 'approve'
        ? `/backoffice-auth/users/activate/${userId}/approve`
        : action === 'make'
          ? `/backoffice-auth/users/activate/${userId}/make`
          : action === 'reject'
            ? `/backoffice-auth/users/activate/${userId}/reject`
            : `/backoffice-auth/users/activate/${userId}`
    await secureapi.put(url, { comments })
    dispatch(setIsLoadingEditUser(false))
    const message =
      action === 'approve'
        ? 'Approved'
        : action === 'make'
          ? 'Initiated'
          : action === 'reject'
            ? 'Rejected'
            : 'Activated'
    dispatch(
      setNotification({
        message: `User activation has been ${message} successfully`,
        type: 'success',
      })
    )
    await getUsers(dispatch, { page: 1, size: 10 })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}
//deactivation apis
export const deactivateUser = async (
  userId: string,
  action: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    const url =
      action === 'approve'
        ? `/backoffice-auth/users/deactivate/${userId}/approve`
        : action === 'make'
          ? `/backoffice-auth/users/deactivate/${userId}/make`
          : action === 'reject'
            ? `/backoffice-auth/users/deactivate/${userId}/reject`
            : `/backoffice-auth/users/deactivate/${userId}`
    await secureapi.put(url, { comments })
    dispatch(setIsLoadingEditUser(false))
    const message =
      action === 'approve'
        ? 'Approved'
        : action === 'make'
          ? 'Initiated'
          : action === 'reject'
            ? 'Rejected'
            : 'Deactivated'
    dispatch(
      setNotification({
        message: `User deactivation has been ${message} successfully`,
        type: 'success',
      })
    )
    await getUsers(dispatch, {
      page: 1,
      size: 10,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}
export const changeUserStatus = async (
  userId: string,
  action: string,
  dispatch: Dispatch,
  comments: string
) => {
  dispatch(setIsLoadingEditUser(true))
  try {
    await secureapi.put(`/backoffice-auth/users/${action}/${userId}`, {
      comments,
    })
    dispatch(setIsLoadingEditUser(false))
    const message = action === 'activate' ? 'Activated' : 'Deactivated'
    dispatch(
      setNotification({
        message: `User has been ${message} successfully`,
        type: 'success',
      })
    )
    await getUsers(dispatch, {
      page: 1,
      size: 10,
    })
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  }
}

/**
 * @returns {firstName: string, lastName: string, email: string, phoneNumber: string} as user profile
 * @param searchParams
 * @param dispatch
 */

export const getUserADProfile = async (
  searchParams: string,
  dispatch: Dispatch
) => {
  if (searchParams) {
    dispatch(setLoadingADUserDetails(true))
    dispatch(setLoadingADSuccess(false))
    dispatch(setLoadingADFailure(false))

    await secureapi
      .get(`/backoffice-auth/users/search/adusers?email=${searchParams}`)
      .then((res) => {
        dispatch(setLoadedADUserDetails(res.data))
        dispatch(setLoadingADSuccess(true))
        dispatch(setLoadingADUserDetails(false))
      })
      .catch(() => {
        dispatch(setLoadingADFailure(true))
        dispatch(setLoadingADUserDetails(false))
        dispatch(
          setNotification({
            // message: e?.message ||
            message: 'An error occurred, Please try again',
            type: 'error',
          })
        )
        // dispatch(setLoadedADUserDetails({} as any))
      })
  }
}

export const assignUserLoanProducts = async (
  data: string[],
  dispatch: Dispatch,
  userId: string
) => {
  dispatch(setIsLoadingEditUser(true))
  const payload = {
    resources: [
      {
        resourceType: 'PRODUCTS',
        resourceIds: data,
      },
    ],
  }
  try {
    await secureapi.put(`/backoffice-auth/users/${userId}/resources`, payload)
    dispatch(
      setNotification({
        message: 'User loan products assigned successfully',
        type: 'success',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsLoadingEditUser(false))
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

export const assignUserPlatforms = async (
  data: string[],
  dispatch: Dispatch,
  userId: string
) => {
  dispatch(setIsLoadingEditUser(true))
  const payload = {
    resources: [
      {
        resourceType: 'PLATFORMS',
        resourceIds: data,
      },
    ],
  }
  try {
    await secureapi.put(`/backoffice-auth/users/${userId}/resources`, payload)
    dispatch(
      setNotification({
        message: 'User platforms assigned successfully',
        type: 'success',
      })
    )
    dispatch(setIsLoadingEditUser(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsLoadingEditUser(false))
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  }
}

//User reports
interface DataItem {
  [key: string]: unknown
}

export const generateUserReports = async ({
  dispatch,
  params,
  format,
}: {
  dispatch: Dispatch
  params: {
    firstName?: string
    lastName?: string
    email?: string
    status?: string
    phoneNumber?: string
    size?: number
    page?: number
    roleIds?: string[]
    dateCreated: string
    loginStartdate: string
    loginEndDate: string
  }
  format: FileFormat
  filteredData: DataItem[]
}) => {
  const {
    firstName,
    lastName,
    email,
    status,
    phoneNumber,
    size,
    page,
    roleIds,
    dateCreated,
    loginStartdate,
    loginEndDate,
  } = params
  let url = `/reports/users/export-to-${format}?page=${page ?? 0}&size=${size ?? 7}`
  if (firstName) url += `&firstName=${firstName}`
  if (lastName) url += `&lastName=${lastName}`
  if (email) url += `&email=${email}`
  if (status) url += `&status=${status}`
  if (phoneNumber) url += `&phoneNumber=${phoneNumber}`
  if (dateCreated) url += `&dateCreated=${dateCreated}`
  if (loginStartdate) url += `&loginStartdate=${loginStartdate}`
  if (loginEndDate) url += `&loginEndDate=${loginEndDate}`
  if (roleIds && roleIds.length > 0)
    url += `&roleIds=${roleIds.join('&roleIds=')}`
  dispatch(setGeneratedUserReportLoading(true))

  try {
    const res = await secureapi2.get(url, { responseType: 'arraybuffer' })
    const dataToExport = res.data
    dispatch(setGeneratedUserReportSuccess(true))

    let mimeType: string
    let extension: string
    switch (format) {
      case 'excel':
        mimeType =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        extension = 'xlsx'
        break
      case 'csv':
        mimeType = 'text/csv'
        extension = 'csv'
        break
      case 'json':
        mimeType = 'application/json'
        extension = 'json'
        break
      case 'pdf':
        mimeType = 'application/pdf'
        extension = 'pdf'
        break
      default:
        throw new Error('Unsupported format')
    }

    const blob = new Blob([dataToExport], { type: mimeType })
    downloadBlob(blob, `User_Report.${extension}`)
  } catch (e) {
    const message = (e as Error).message
    dispatch(setGeneratedUserReportFailure(true))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setGeneratedUserReportLoading(false))
  }
}

//products apis
export const getLoanProducts = async (dispatch: Dispatch, params?: string) => {
  dispatch(setIsLoadingLoanProducts(true))
  try {
    const response = await secureapi2.get(`/lms/products?${params || ''}`)
    const { data, ...rest } = response.data
    dispatch(setLoanProducts(data))
    dispatch(setLoanProductsSummary(rest as ILoanRequestsSummary))
    const accessToken = localStorage.getItem('accessToken')
    if (accessToken) {
      const decodedToken: IDecodeToken = jwtDecode(accessToken)
      const userProducts = data.filter((product: ILoanProduct) => {
        if (decodedToken.resources && decodedToken.resources[0]) {
          return decodedToken.resources[0].resourceIds.includes(product.id)
        }
      })
      dispatch(setUserProducts(userProducts))
    }
    dispatch(setIsLoadingLoanProducts(false))
    return response.data
  } catch (e) {
    dispatch(setIsLoadingLoanProducts(false))
  }
}
//user platform apis
export const getuserPlatform = async (dispatch: Dispatch, params?: string) => {
  dispatch(setIsLoadingPlatforms(true))
  try {
    const response = await secureapi2.get(`/dbp/platforms?${params || ''}`)
    const { data, ...rest } = response.data
    dispatch(setPlatforms(data))
    dispatch(setPlatformsSummary(rest as ILoanRequestsSummary))
    dispatch(setIsLoadingPlatforms(false))
  } catch (e) {
    dispatch(setIsLoadingPlatforms(false))
    console.error('Error fetching platforms: ', e)
  }
}