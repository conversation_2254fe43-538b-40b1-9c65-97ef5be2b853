import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  ConfigurationResponse,
  IConfigurationCreate,
  IConfigurationData,
  IConfigurationLogs,
  ITariffCreate,
  ITariffData,
  TariffDataResponse,
} from '../interfaces/chargeConfiguration'

export type Status = 'Active' | 'Inactive' | 'Pending Approval'

export interface chargeConfigurationProps {
  //Tariffs
  tariffs: ITariffData[]
  selectedTariff: ITariffCreate
  tariffsSummary: TariffDataResponse
  isLoading: boolean

  //Configurations
  configurations: IConfigurationData[]
  selectedConfiguration: IConfigurationData
  configurationsSummary: ConfigurationResponse
  configuredServices: IConfiguredService[]
  isEditable: boolean
  selectedConfigurationLog: IConfigurationLogs
  openChangeLogsDrawer: boolean
}
export interface IConfiguredService {
  // serviceName: string
  serviceId: string
  name: string
}

const initialState: chargeConfigurationProps = {
  //Tariffs
  tariffs: [] as ITariffData[],
  selectedTariff: {} as ITariffCreate,
  tariffsSummary: {} as TariffDataResponse,
  isLoading: false,

  //Configurations
  configurations: [] as IConfigurationData[],
  selectedConfiguration: {} as IConfigurationCreate,
  configurationsSummary: {} as ConfigurationResponse,
  configuredServices: [] as IConfiguredService[],
  selectedConfigurationLog: {} as IConfigurationLogs,
  isEditable: false,
  openChangeLogsDrawer: false,
}

const chargeConfigurationSlice = createSlice({
  name: 'chargeConfiguration',
  initialState,
  reducers: {
    //Tariffs
    setSelectedTariff: (state, action: PayloadAction<ITariffCreate>) => {
      state.selectedTariff = action.payload
    },
    setLoadingState: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },

    //Configurations
    setSelectedConfiguration: (
      state,
      action: PayloadAction<IConfigurationCreate>
    ) => {
      state.selectedConfiguration = action.payload
    },
    resetChargeConfigurationStore: () => initialState,

    setSelectedConfigurationLog: (
      state,
      action: PayloadAction<IConfigurationLogs>
    ) => {
      state.selectedConfigurationLog = action.payload
    },

    setEditableState: (state, action: PayloadAction<boolean>) => {
      state.isEditable = action.payload
    },
    setConfiguredServices: (
      state,
      action: PayloadAction<IConfiguredService>
    ) => {
      const isAlreadyConfigured = state.configuredServices.some(
        (service) => service.serviceId === action.payload.serviceId
      )

      if (!isAlreadyConfigured) {
        state.configuredServices = [...state.configuredServices, action.payload]
      }
    },
    removeConfiguredService: (state, action: PayloadAction<string>) => {
      state.configuredServices = state.configuredServices.filter(
        (service) => service.serviceId !== action.payload[0]
      )
    },
    setOpenDrawerAction: (state, action: PayloadAction<boolean>) => {
      state.openChangeLogsDrawer = action.payload
    },
  },
})

export const {
  //Tariffs
  setSelectedTariff,
  setLoadingState,

  //Configurations
  setSelectedConfiguration,
  resetChargeConfigurationStore,
  setConfiguredServices,
  removeConfiguredService,
  setEditableState,
  setOpenDrawerAction,
  setSelectedConfigurationLog,
} = chargeConfigurationSlice.actions

export default chargeConfigurationSlice.reducer
