import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import { Button, Menu, MenuItem } from '@mui/material'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'
import { useAppDispatch } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'

import { ApprovalRequestRouting } from '../../approval-requests/RequestRouting'
import ReviewRequest from '../../approval-requests/Pending/Dialog/ReviewRequest'

export const PendingRequestMoreMenu = ({
  request,
}: {
  request: IApprovalRequest
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const dispatch = useAppDispatch()
  const router = useRouter()
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  const handleReviewRequest = async (request: IApprovalRequest) => {
    await ApprovalRequestRouting(request, dispatch, router)
    handleClose()
  }
  return (
    <>
      <Button
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        variant="outlined"
        disableElevation
        onClick={handleClick}
        sx={{
          border: '1px solid #D0D5DD',
          padding: '8px 14px',
          fontWeight: '500',
          gap: 0,
        }}
        endIcon={<KeyboardArrowDownIcon />}
      >
        Actions
      </Button>

      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
        slotProps={{
          root: {},
          paper: {
            elevation: 0,
            sx: {
              boxShadow:
                '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
            },
          },
        }}
      >
        <ReviewRequest request={request} />
        <AccessControlWrapper
          rights={[
            ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_CUSTOMERS,
            ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
            ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_ROLES,
            ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_CUSTOMERS,
            ...ACCESS_CONTROLS.REJECT_APPROVALREQUEST_ROLES,
            ...ACCESS_CONTROLS.ACCEPT_APPROVALREQUEST_USERS,
          ]}
        >
          <MenuItem onClick={() => handleReviewRequest(request)}>
            Review request
          </MenuItem>
        </AccessControlWrapper>
      </Menu>
    </>
  )
}
