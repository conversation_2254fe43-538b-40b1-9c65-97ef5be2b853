'use client'

import { CloseRounded, FilterListRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useCallback, useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import dayjs from 'dayjs'
import { useAppDispatch, useAppSelector } from '@/store'
import { IFilter, IHeadCell } from '@dtbx/store/interfaces'
import { IApprovalRequest } from '@/store/interfaces'
import { getApprovalRequestTypes, getApprovals } from '@/store/actions'
import { EmptySearchAndFilter } from '@dtbx/ui/components/EmptyPage'
import {
  DateRangePicker,
  DropDownMenu,
  DropDownMenuRadio,
} from '@dtbx/ui/components/DropDownMenus'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'

import { AllApprovalRequestsMoreMenu } from '../../approval-requests/All/MoreMenu'
import { CustomTableCell, RequestChip } from '../Pending'
import RequestSearch from '../RequestSearch'
import { CustomDateRangePicker } from '@/app/approval-requests/CustomFilterBox'

const resolvedHeader: IHeadCell[] = [
  {
    id: 'requestType',
    label: 'Request Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'module',
    label: 'Module',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker',
    label: 'Checker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'action',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]

type ExtendedKeys =
  | keyof IApprovalRequest
  | keyof IApprovalRequest['makerCheckerType']
export const requestSearchByItems: {
  label: string
  value: Array<ExtendedKeys>
}[] = [{ label: 'Module', value: ['module'] }]

const Resolved: React.FC = () => {
  const dispatch = useAppDispatch()

  const [page, setPage] = useState(1)

  const [openFilterBar, setOpenFilterBar] = useState<boolean>(false)

  const [status, setStatus] = useState<string | null>('')
  const [requestType, setRequestType] = useState<string | null>('')
  const [dateRange, setDateRange] = useState<string[] | null>(null)
  const [makerName, setMakerName] = useState<string>('')

  const {
    approvalRequests,
    isLoadingRequests,
    approvalRequestResponse,
    isRequestTypesSuccess,
    isRequestTypesLoading,
    requestTypes,
  } = useAppSelector((state) => state.approvalRequests)

  const { search } = useAppSelector((state) => state.customers)

  const makerNameFilter = () => {
    return search?.searchBy[0] === 'firstName'
      ? 'makerFirstName'
      : search?.searchBy[0] === 'lastName'
        ? 'makerLastName'
        : ''
  }

  const [paginationOptions, setPaginationOptions] = useState({
    page: approvalRequestResponse.pageNumber,
    size: 10,
    totalPages: approvalRequestResponse.totalNumberOfPages,
  })
  /*************************Query params handlers***************************/
  const buildQueryParams = (): string => {
    const params = new URLSearchParams()
    params.append('channel', 'IAM')
    params.append('size', String(paginationOptions.size))
    params.append('page', String(page))

    if (status) params.append('status', status.toUpperCase())
    if (makerName) params.append(makerNameFilter(), makerName)
    if (requestType) params.append('requestType', requestType)
    if (dateRange) {
      params.append('createDateFrom', dateRange[0])
      params.append('createDateTo', dateRange[1])
    }

    return `?${params.toString()}`
  }

  const fetchApprovals = useCallback(async () => {
    const queryParams = buildQueryParams()
    await getApprovals(dispatch, queryParams)
  }, [
    paginationOptions,
    page,
    status,
    makerName,
    requestType,
    dateRange,
    search,
  ])

  /*************************start pagination handlers***************************/
  const handlePagination = (newOptions: PaginationOptions) => {
    setPage(newOptions.page)
    setPaginationOptions({ ...paginationOptions, page: newOptions.page })
    fetchApprovals()
  }

  useEffect(() => {
    fetchApprovals()
  }, [status, page, makerName, requestType, dateRange])
  /*************************end pagination handlers**************************/

  /************************* Filter Handlers ***************************/
  const handleRequestTypeFilter = async (requestTypeId: string) => {
    setRequestType(requestTypeId)
    setPage(1)
  }

  const handleStatusFilter = (newStatus: string) => {
    setStatus(newStatus)
    setPage(1)
  }
  const handleDateRangeFilterApply = (_filter: string, date: string[]) => {
    setDateRange(date)
  }
  const filter: IFilter = {
    filterName: 'Date created',
    options: [
      {
        label: 'Date created',
        value: 'dateCreated',
        key: '',
      },
    ],
    type: 'date',
  }

  return (
    <Box
      sx={{
        padding: '2% 2% 0 2.5%',
        display: 'flex',
        flexDirection: 'column',
        gap: '22px',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-start',
          gap: '16px',
          flexDirection: 'column',
          width: '100%',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: '16px',
          }}
        >
          <RequestSearch
            searchByItems={[
              {
                label: 'Maker First Name',
                value: 'firstName',
              },
              {
                label: 'Maker Last Name',
                value: 'lastName',
              },
            ]}
            onSetSearch={(makerName: string) => {
              setMakerName(makerName)
              fetchApprovals()
            }}
          />
          <Button
            sx={{
              display: 'flex',
              width: '131px',
              padding: '8px 42px',
              justifyContent: 'center',
              alignItems: 'center',
              gap: '10px',
              height: '42px',
              borderRadius: '4px',
              border: '1.5px solid #D0D5DD',
              background: '#FFF',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            variant="outlined"
            startIcon={<FilterListRounded />}
            onClick={() => setOpenFilterBar(!openFilterBar)}
          >
            Filter
          </Button>
        </Box>
        {openFilterBar && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'flex-start',
              gap: '16px',
            }}
          >
            <Button
              onClick={() => {
                setOpenFilterBar(false)
                setPage(1)
                setStatus('')
                setMakerName('')
                setRequestType('')
                setDateRange(null)
                fetchApprovals()
              }}
              sx={{
                minWidth: '131px',
                height: '40px',
                gap: '0px',
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              }}
              endIcon={
                <Typography
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  <CloseRounded />
                </Typography>
              }
            >
              <Typography>Clear</Typography>
            </Button>
            <CustomDateRangePicker
              filter={filter}
              handleFilterChangeAction={handleDateRangeFilterApply}
              selectedDates={dateRange as string[]}
            />

            <DropDownMenuRadio
              menuItems={['Approved', 'Pending', 'Rejected']}
              onClick={handleStatusFilter}
              buttonText={'Status'}
            />

            <DropDownMenu
              menuItems={
                isRequestTypesSuccess
                  ? [
                      ...requestTypes.map((item) => {
                        return { label: item.name, id: item.id }
                      }),
                    ]
                  : []
              }
              loading={isRequestTypesLoading}
              onSelect={handleRequestTypeFilter}
              buttonText={'Request type'}
              onButtonClick={async (setOpen) => {
                setOpen((prev) => !prev)
                await getApprovalRequestTypes(dispatch, 'IAM')
              }}
            />
          </Box>
        )}
      </Box>

      {isLoadingRequests ? (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '100%',
            height: '60vh',
          }}
        />
      ) : (
        <Paper
          elevation={0}
          sx={{
            boxShadow:
              '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FFFFFF',
          }}
        >
          <TableContainer component={Paper} elevation={0}>
            <Table stickyHeader sx={{}}>
              <CustomTableHeader
                order={'desc'}
                orderBy={''}
                rowCount={0}
                headLabel={[...resolvedHeader]}
                numSelected={0}
              />
              <TableBody>
                {approvalRequests.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <EmptySearchAndFilter
                        message="No requests match your filters"
                        additionalText={`No records found. Please try again with different filters.`}
                        onClick={() => {
                          setPage(1)
                          setStatus('')
                          setMakerName('')
                          setRequestType('')
                          setDateRange(null)
                          fetchApprovals()
                        }}
                      />
                    </TableCell>
                  </TableRow>
                ) : (
                  approvalRequests.map((request, index) => (
                    <TableRow key={index || request.id}>
                      <CustomTableCell>
                        <Box>
                          <RequestChip
                            label={sentenceCase(request.makerCheckerType.type)}
                            sx={{ width: 'auto' }}
                          />
                        </Box>
                      </CustomTableCell>
                      <CustomTableCell>
                        {sentenceCase(request.makerCheckerType.module)}
                      </CustomTableCell>
                      <CustomTableCell>
                        <CustomerStatusChip label={request.status} />
                      </CustomTableCell>
                      <CustomTableCell>{request.maker}</CustomTableCell>
                      <CustomTableCell>{request.checker}</CustomTableCell>
                      <CustomTableCell sx={{ padding: '0px' }}>
                        <AllApprovalRequestsMoreMenu request={request} />
                      </CustomTableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          {approvalRequestResponse.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: approvalRequestResponse.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </Box>
  )
}

export default Resolved
