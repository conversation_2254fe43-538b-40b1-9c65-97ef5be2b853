import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import {
  activateUser,
  approveCreateRole,
  approveCreateUser,
  approveUpdateUser,
  checkDeleteRole,
  checkUpdateRole,
  deactivateUser,
  rejectCreateRole,
  rejectCreateUser,
  rejectUpdateUser,
} from '@/store/actions'

import { IApprovalRequest } from '@/store/interfaces'

//This function takes the action and approval request and makes the api call to the selected api call and routes to the final route
export const CheckerRequestsApiHandler = async (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  action: string,
  comments?: string
) => {
  const handler = handlers[request.makerCheckerType.module]
  if (handler) {
    if (typeof handler === 'function') {
      await handler(request, dispatch, router)
    } else {
      const subHandler = handler[action]
      if (subHandler) {
        await subHandler(request, dispatch, router, comments)
      }
    }
  }
}
type Handler = (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance,
  comments?: string
) => Promise<void>
type Handlers = {
  [key: string]: Handler | { [key: string]: Handler }
}
const handlers: Handlers = {
  users: {
    //accept actions
    ACCEPT_CREATE_USERS: async (request, dispatch, router, comments) => {
      await approveCreateUser(request.id, comments || '', dispatch)
      router.push('/users')
    },
    ACCEPT_UPDATE_USERS: async (request, dispatch, router, comments) => {
      await approveUpdateUser(
        request.entityId || '',
        { comments: comments || '' },
        dispatch
      )
      router.push('/users')
    },
    ACCEPT_DEACTIVATE_USERS: async (request, dispatch, router, comments) => {
      await deactivateUser(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
      router.push('/users')
    },
    ACCEPT_ACTIVATE_USERS: async (request, dispatch, router, comments) => {
      await activateUser(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
      router.push('/users')
    },

    REJECT_CREATE_USERS: async (request, dispatch, router, comments) => {
      await rejectCreateUser(request.id, comments || '', dispatch)
      router.push('/users')
    },
    REJECT_UPDATE_USERS: async (request, dispatch, router, comments) => {
      await rejectUpdateUser(
        request.entityId || '',
        { comments: comments || '' },
        dispatch
      )
      router.push('/users')
    },
    REJECT_DEACTIVATE_USERS: async (request, dispatch, router, comments) => {
      await deactivateUser(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
      router.push('/users')
    },
    REJECT_ACTIVATE_USERS: async (request, dispatch, router, comments) => {
      await activateUser(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
      router.push('/users')
    },
  },
  groups: {
    //accept actions
    ACCEPT_ACTIVATE_GROUPS: async (request, dispatch, router, comments) => {
      await approveCreateRole(request.id, comments || '', dispatch)
      router.push('/roles')
    },
    ACCEPT_UPDATE_GROUPS: async (request, dispatch, router, comments) => {
      await checkUpdateRole(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
      router.push('/roles')
    },
    ACCEPT_DELETE_GROUPS: async (request, dispatch, router, comments) => {
      await checkDeleteRole(
        request.entityId || '',
        'approve',
        dispatch,
        comments || ''
      )
      router.push('/roles')
    },

    //reject actions
    REJECT_ACTIVATE_GROUPS: async (request, dispatch, router, comments) => {
      await rejectCreateRole(request.id, comments || '', dispatch)
      router.push('/roles')
    },
    REJECT_UPDATE_GROUPS: async (request, dispatch, router, comments) => {
      await checkUpdateRole(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
      router.push('/roles')
    },
    REJECT_DELETE_GROUPS: async (request, dispatch, router, comments) => {
      await checkDeleteRole(
        request.entityId || '',
        'reject',
        dispatch,
        comments || ''
      )
      router.push('/roles')
    },
  },
}
