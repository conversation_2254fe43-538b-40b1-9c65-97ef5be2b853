import { Stack, Typography } from '@mui/material'
import React from 'react'
import { CustomersIcon, StaffUsersIcon } from '@dtbx/ui/icons'

const layout = (props: { children: string }) => {
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          padding: '8px',
        }}
      >
        <CustomersIcon width="28" height="26" />
        <Typography variant="h6">User Management</Typography>
      </Stack>
      {props.children}
    </Stack>
  )
}

export default layout
