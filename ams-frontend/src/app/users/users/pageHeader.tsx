'use client'
import { CircularProgress, Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { setNotification } from '@dtbx/store/reducers'
import { setUserFilterValue, setUserSearchValue } from '@/store/reducers'
import { generateUserReports, getRoles, getUsers } from '@/store/actions'
import { FileFormat } from '@/store/interfaces'
import { IFilter, IRole } from '@dtbx/store/interfaces'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'
import { ExportButton } from '@dtbx/ui/components/ExportButton'
import { ExportPreferences } from '@dtbx/ui/components/Dialogs'

import { CreateUser } from '@/app/users/users/CreateUser'

interface UsersPageHeaderProps {
  selectedUserCount: number
  selectedIds: string[]
  searchByField: string
  setSearchByField: React.Dispatch<React.SetStateAction<string>>
}
export const UsersPageHeader: React.FC<UsersPageHeaderProps> = ({
  selectedIds,
  searchByField,
  setSearchByField,
}) => {
  const dispatch = useAppDispatch()
  const { usersResponse } = useAppSelector((state) => state.users)
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const { searchUserValue } = useAppSelector((state) => state.users)
  const [searchValue, setSearchValue] = useState<string>(searchUserValue)
  const [exportCount, setExportCount] = useState<number>(0)

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value
    setSearchValue(search)
    dispatch(setUserSearchValue(search))

    await getUsers(dispatch, {
      page: 1,
      size: 10,
      [`${searchByField}`]: search,
    })
  }
  const roles = useAppSelector((state) => state.roles.rolesList)
  const handleFilterChange = async (
    filters: Record<string, string | string[]>
  ) => {
    dispatch(setUserFilterValue(filters))
    const status = filters['Status'] as string
    const roles = filters['Role'] as string[]
    const dateCreated = filters['Date created']
    const lastLogin = filters['Last Login']

    let dateFilters = {}
    if (dateCreated)
      dateFilters = {
        ...dateFilters,
        dateCreatedFrom: dateCreated[0],
        dateCreatedTo: dateCreated[1],
      }
    if (lastLogin)
      dateFilters = {
        ...dateFilters,
        lastLoginDateFrom: lastLogin[0],
        lastLoginDateTo: lastLogin[1],
      }
    await getUsers(dispatch, {
      page: 1,
      size: 10,
      status: status,
      roleIds: roles,
      ...dateFilters,
    })
  }
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const filters: IFilter[] = [
    {
      filterName: 'Role',
      options: roles
        ? roles.map((role: IRole) => ({
            key: role.id,
            value: role.id,
            label: role.name,
          }))
        : [],
      type: 'dropdown/checkbox',
    },
    {
      filterName: 'Status',
      options: [
        { key: 'active', value: 'ACTIVE', label: 'Active' },
        { key: 'inactive', value: 'INACTIVE', label: 'Inactive' },
        { key: 'pending', value: 'PENDING', label: 'Pending' },
        { key: 'dormant', value: 'DORMANT', label: 'Dormant' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'Date created',
      options: [
        {
          label: 'Date created',
          value: 'dateCreated',
          key: '',
        },
      ],
      type: 'date',
    },
    {
      filterName: 'Last Login',
      options: [
        {
          label: 'Last Login',
          value: 'lastLogin',
          key: '',
        },
      ],
      type: 'date',
    },
  ]
  useEffect(() => {
    setExportCount(usersResponse?.totalElements || 0)
  }, [usersResponse])

  const handleExportClick = () => {
    setOpen(true)
  }

  const handleExport = async (format: FileFormat) => {
    setOpen(false)
    setLoading(true)

    const filteredData = selectedIds.map((id) => ({ id }))

    // Export logic based on selected format
    await generateUserReports({
      dispatch,
      params: {
        page: 1,
        size: filteredData.length,
        dateCreated: '',
        loginStartdate: '',
        loginEndDate: '',
      },
      format: format,
      filteredData: filteredData,
    })

    // Simulate export process
    await new Promise((resolve) => setTimeout(resolve, 3000))
    setLoading(false)
    dispatch(
      setNotification({
        message: `${exportCount} items successfully exported.`,
        type: 'success',
      })
    )
  }

  const handleCancel = () => {
    setOpen(false)
  }
  useEffect(() => {
    if (openFilter) {
      getRoles(dispatch)
    }
  }, [openFilter])
  return (
    <Stack
      sx={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        my: '1vh',
        gap: '10px',
      }}
    >
      <CustomFilterBox
        openFilter={openFilter}
        setOpenFilter={setOpenFilter}
        searchValue={searchValue}
        searchByValues={['email', 'firstName', 'lastName', 'phoneNumber']}
        handleSearch={handleSearch}
        filters={filters}
        onFilterChange={handleFilterChange}
        setSearchByValue={setSearchByField}
      />
      <ExportButton
        onClick={handleExportClick}
        text={`Export All ${exportCount}`}
        openFilter={openFilter}
      />

      <ExportPreferences
        open={open}
        onExport={handleExport}
        onCancel={handleCancel}
        setOpen={setOpen}
        selectedIds={[]}
      />

      <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ROLES}>
        <CreateUser />
      </AccessControlWrapper>
      {loading && (
        <Stack
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.5)',
            zIndex: 10,
          }}
        >
          <CircularProgress size={40} sx={{ color: '#1976d2' }} />
        </Stack>
      )}
    </Stack>
  )
}
