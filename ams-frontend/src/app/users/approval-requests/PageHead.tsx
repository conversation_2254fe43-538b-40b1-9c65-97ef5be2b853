import {
  CloseRounded,
  FilterListOffRounded,
  FilterListRounded,
} from '@mui/icons-material'
import { Button, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import dayjs from 'dayjs'
import { getApprovalRequestTypes, getApprovals } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  DateRangePicker,
  DropDownMenu,
  DropDownMenuRadio,
} from '@dtbx/ui/components/DropDownMenus'

import RequestSearch from '../../approval-requests/RequestSearch'

const PageHead = () => {
  const dispatch = useAppDispatch()

  const [openFilterBar, setOpenFilterBar] = React.useState<boolean>(false)
  const [status, setStatus] = React.useState<string | null>('')
  const [requestType, setRequestType] = React.useState<string | null>('')
  const {
    requestTypes,
    isRequestTypesLoading,
    isRequestTypesSuccess,
    approvalRequestResponse,
  } = useAppSelector((state) => state.approvalRequests)

  const { search } = useAppSelector((state) => state.customers)

  const makerNameFilter = () => {
    return search?.searchBy[0] === 'firstName'
      ? 'makerFirstName'
      : search?.searchBy[0] === 'lastName'
        ? 'makerLastName'
        : ''
  }

  const handleDateRangeFilterApply = async (date: {
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  }) => {
    setDateRange(date)
    let params = `?channel=${'DBP'}&module=${'users'}&createDateFrom=${date.start.format('YYYY-MM-DD')}&createDateTo=${date.end.format('YYYY-MM-DD')}&page=${approvalRequestResponse.pageNumber}&size=10`
    params += makerName ? `&${makerNameFilter()}=${makerName}` : ''
    params += status ? `&status=${status.toUpperCase()}` : ''
    params += requestType ? `&requestType=${requestType}` : ''
    await getApprovals(dispatch, params)
  }

  const handleSearch = async (name: string) => {
    const params = `?channel=${'DBP'}&module=${'users'}&${makerNameFilter()}=${name}&size=10&page=${approvalRequestResponse?.pageNumber}`
    await getApprovals(dispatch, params)
  }

  // filters

  const [dateRange, setDateRange] = React.useState<{
    start: dayjs.Dayjs
    end: dayjs.Dayjs
  } | null>(null)
  const [makerName, setMakerName] = React.useState<string>('')

  useEffect(() => {
    getApprovals(
      dispatch,
      `?channel=${'DBP'}&module=users&size=10&page=${approvalRequestResponse.pageNumber}`
    )
  }, [])

  const handleStatusFilter = async (item: string) => {
    setStatus(item)
    let params = `?channel=${'DBP'}&module=users&status=${item.toUpperCase()}&size=10&page=1`
    params += dateRange
      ? `&createDateFrom=${dateRange.start.format('YYYY-MM-DD')}&createDateTo=${dateRange.end.format('YYYY-MM-DD')}`
      : ''
    params += makerName ? `&${makerNameFilter()}=${makerName}` : ''
    params += requestType ? `&requestType=${requestType}` : ''

    await getApprovals(dispatch, params)
  }

  const handleRequestTypeFilter = async (requestTypeId: string) => {
    setRequestType(requestTypeId)
    let params = `?&channel=${'IAM'}&module=${'users'}&requestType=${requestTypeId}&page=${approvalRequestResponse.pageNumber}&size=10`
    params += dateRange
      ? `&createDateFrom=${dateRange.start.format('YYYY-MM-DD')}&createDateTo=${dateRange.end.format('YYYY-MM-DD')}`
      : ''
    params += makerName ? `&${makerNameFilter()}=${makerName}` : ''
    params += status ? `&status=${status.toUpperCase()}` : ''
    await getApprovals(dispatch, params)
  }
  return (
    <Stack
      sx={{
        justifyContent: 'flex-start',
        gap: '16px',
        flexDirection: 'column',
        width: '100%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '16px',
        }}
      >
        <RequestSearch
          searchByItems={[
            {
              label: 'Maker First Name',
              value: 'firstName',
            },
            {
              label: 'Maker Last Name',
              value: 'lastName',
            },
          ]}
          onSetSearch={(makerName: string) => {
            setMakerName(makerName)
            handleSearch(makerName)
          }}
        />
        <Button
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            height: '40px',
            borderRadius: '4px',
            textWrap: 'nowrap',
            border: '1.5px solid #D0D5DD',
            background: '#FFF',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          variant="outlined"
          startIcon={
            !openFilterBar ? <FilterListOffRounded /> : <FilterListRounded />
          }
          onClick={() => setOpenFilterBar(!openFilterBar)}
        >
          {`${openFilterBar ? 'Hide' : 'Show'}`} Filters
        </Button>
      </Stack>
      {openFilterBar && (
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'flex-start',
            gap: '16px',
          }}
        >
          <Button
            onClick={() => {
              setDateRange(null)
              setMakerName('')
              getApprovals(dispatch, `?channel='DBP'&size=10&page=1`)
            }}
            sx={{
              minWidth: '131px',
              height: '40px',
              gap: '0px',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            endIcon={
              <Typography
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                }}
              >
                <CloseRounded />
              </Typography>
            }
          >
            <Typography>Clear</Typography>
          </Button>
          <DateRangePicker
            onApplyDateRange={handleDateRangeFilterApply}
            buttonText="Date Created"
          />

          <DropDownMenuRadio
            menuItems={['Approved', 'Pending', 'Rejected']}
            onClick={handleStatusFilter}
            buttonText={'Status'}
          />

          <DropDownMenu
            menuItems={
              isRequestTypesSuccess
                ? [
                    ...requestTypes.map((item) => {
                      return { label: item.name, id: item.id }
                    }),
                  ]
                : []
            }
            loading={isRequestTypesLoading}
            onSelect={handleRequestTypeFilter}
            buttonText={'Request type'}
            onButtonClick={(setOpen) => {
              setOpen((prev) => !prev)
              getApprovalRequestTypes(dispatch, 'DBP')
            }}
          />
        </Stack>
      )}
    </Stack>
  )
}

export default PageHead
