/**
 * <AUTHOR> on 29/10/2024
 */
import {
  RightsIcon,
  StaffUsersIcon,
  CustomersIcon,
  RequestsApprovalIcon,
} from '@dtbx/ui/icons'
import { ISidebarConfigItem } from '@dtbx/ui/components/Sidebar'

export const sidebarConfig: ISidebarConfigItem[] = [
  {
    id: '1',
    title: 'Users',
    path: '/users',
    module: 'users',
    icon: <CustomersIcon />,
    isProductionReady: true,
  },
  {
    id: '2',
    title: 'Roles',
    path: '/roles',
    module: 'users',
    icon: <StaffUsersIcon />,
    isProductionReady: true,
  },
  {
    id: '3',
    title: 'Rights',
    path: '/rights',
    module: 'users',
    icon: <RightsIcon />,
    isProductionReady: true,
  },
  {
    id: '4',
    title: 'Approval Requests',
    path: '/approval-requests',
    module: 'users',
    icon: <RequestsApprovalIcon />,
    isProductionReady: true,
  },
]
