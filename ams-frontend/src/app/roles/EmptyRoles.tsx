'use client'
import { But<PERSON>, Stack, Typography } from '@mui/material'
import { AddRounded } from '@mui/icons-material'
import { useAppDispatch } from '@/store'
import { ACCESS_CONTROLS, AccessControlWrapper } from '@dtbx/store/utils'
import { setDrawer } from '@dtbx/store/reducers'

export const EmptyRoles = () => {
  const dispatch = useAppDispatch()
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        alignContent: 'center',
        py: '30vh',
        backgroundImage: "url('/dashboard/background-empty.svg')",
        backgroundSize: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'top',
      }}
    >
      <Stack
        sx={{
          px: '5%',
          gap: '10px',
          justifyContent: 'center',
        }}
      >
        {/*<Stack sx={{*/}
        {/*  marginBottom: '5%',*/}
        {/*  justifyContent: 'center',*/}
        {/*  alignItems: 'center',*/}
        {/*}}>*/}
        {/*  <Stack sx={{*/}
        {/*     border: '1px solid #EAECF0',*/}
        {/*    borderRadius: '12px',*/}
        {/*    p: '2%'*/}
        {/*  }}>*/}
        {/*    <SearchOutlined sx={{*/}
        {/*      color: theme.palette.primary.main,*/}
        {/*      fontSize: '28px'*/}
        {/*    }}/>*/}
        {/*  </Stack>*/}
        {/*</Stack>*/}
        <Typography variant="h6">
          {"Seems like you haven't created any roles yet"}
        </Typography>
        <Typography
          variant="body1"
          sx={{
            px: '10%',
            textAlign: 'center',
          }}
        >
          You can do this by clicking on the button below to create a new role.
        </Typography>
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ROLES}>
          <Button
            onClick={() => {
              dispatch(
                setDrawer({
                  open: true,
                  drawerChildren: {
                    childType: 'create_role',
                  },
                  header: 'Create new role',
                })
              )
            }}
            startIcon={<AddRounded />}
            variant="contained"
          >
            Create new role
          </Button>
        </AccessControlWrapper>
      </Stack>
    </Stack>
  )
}
