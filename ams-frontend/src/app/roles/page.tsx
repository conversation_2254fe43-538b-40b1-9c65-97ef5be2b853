'use client'
import React, { useCallback, useEffect, useState } from 'react'
import { Stack } from '@mui/system'
import _ from 'lodash'
import { useAppDispatch, useAppSelector } from '@/store'
import { getPermissionsGroup, getRolesFilter } from '@/store/actions'
import { LoadingListsSkeleton } from '@dtbx/ui/components/Loading'

import { EmptyRoles } from '@/app/roles/EmptyRoles'
import { ListRoles } from '@/app/roles/ListRoles'
import { PageHeader } from '@/app/roles/PageHeader'

const RolesPage = () => {
  const dispatch = useAppDispatch()
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const { tableListRoles, isLoadingTableListRoles } = useAppSelector(
    (state) => state.roles
  )
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [filterValue, setFilterValue] = useState<
    Record<string, string | string[]>
  >({})
  const [rowsPerPage] = useState(10)
  useEffect(() => {
    // getRolesFilter(dispatch, { page: 1, size: 10 })
    getPermissionsGroup(dispatch, { page: 1, size: 100 })
  }, [])
  const payload = {
    page,
    size: rowsPerPage,
    roleName: search,
    isVisible: filterValue['Is Visible'],
    moduleName: filterValue.Module,
    rights: filterValue.Rights,
  }
  const debouncedSearch = useCallback(
    _.debounce(
      (value: string, filter: Record<string, string | string[]>) =>
        getRolesFilter(dispatch, {
          ...payload,
          page: 1,
          roleName: value,
          isVisible: filter['Is Visible'],
          moduleName: filter.Module,
          rights: filter.Rights,
        }),
      1000
    ),
    []
  )
  useEffect(() => {
    getRolesFilter(dispatch, payload)
  }, [page])
  const triggerSearch = (searchValue: string) => {
    setSearch(searchValue)
    debouncedSearch(searchValue, filterValue)
  }
  const triggerFilter = (filter: Record<string, string | string[]>) => {
    setFilterValue(filter)
    setPage(1)
    getRolesFilter(dispatch, {
      ...payload,
      page: 1,
      isVisible: filter['Is Visible'],
      moduleName: filter.Module,
      rights: filter.Rights,
    })
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        py: '0.5%',
        px: '1.5%',
      }}
    >
      <PageHeader
        search={triggerSearch}
        filter={triggerFilter}
        selectedIds={selectedIds}
      />
      {isLoadingTableListRoles ? (
        <LoadingListsSkeleton />
      ) : (
        <>
          {!tableListRoles || tableListRoles?.length === 0 ? (
            <EmptyRoles />
          ) : (
            <>
              <ListRoles
                page={page}
                setPage={setPage}
                onExport={setSelectedIds}
              />
            </>
          )}
        </>
      )}
    </Stack>
  )
}

export default RolesPage
