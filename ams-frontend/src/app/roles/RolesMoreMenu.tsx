import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Avatar,
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogTitle,
  Drawer,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  Paper,
  Popover,
  Stack,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import CloseIcon from '@mui/icons-material/Close'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import { sentenceCase } from 'tiny-case'
import {
  IPermission,
  IPermissionGroup,
  IRole,
  IUser,
} from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { setSwitchToRoleDetails } from '@dtbx/store/reducers'
import {
  ACCESS_CONTROLS,
  AccessControlWrapper,
  getInitials,
  HasAccessToRights,
} from '@dtbx/store/utils'
import { deleteRole, getRoleById, makeDeleteRole } from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { CustomDialog } from '@dtbx/ui/components/Dialogs'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { MoreVert } from '@dtbx/ui/icons'
import { CustomChip, CustomDrawerChip } from '@dtbx/ui/components/Chip'
import {
  CloseRounded,
  SearchRounded,
  KeyboardArrowDownRounded,
} from '@mui/icons-material'
import EditRole from './EditRole'

type RoleMenuProps = {
  role: IRole
  setAnchorEl?: React.Dispatch<React.SetStateAction<HTMLElement | null>>
}

export const RolesMoreMenu = (props: RoleMenuProps) => {
  const { role } = props
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  return (
    <div>
      <IconButton
        sx={{
          width: '24px',
          height: '24px',
          borderRadius: '50%',
        }}
        onClick={handleClick}
      >
        <MoreVert />
      </IconButton>

      <Menu
        id="demo-customized-menu"
        MenuListProps={{
          'aria-labelledby': 'demo-customized-button',
        }}
        anchorEl={anchorEl}
        onClose={handleClose}
        open={open}
        sx={{
          borderRadius: '4px',
        }}
      >
        <EditRole role={role} />
        <ViewRoleDetails role={role} setAnchorEl={setAnchorEl} />
        <DeleteRole role={role} setAnchorEl={setAnchorEl} />
      </Menu>
    </div>
  )
}
const DeleteRole = (props: RoleMenuProps) => {
  const { role } = props
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)
  const isLoadingDelete = useAppSelector(
    (state) => state.roles.isLoadingDeleteRole
  )
  const handleOpen = () => {
    setOpen(true)
  }
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const handleStatusChange = async () => {
    if (HasAccessToRights(['SUPER_DELETE_GROUPS'])) {
      await deleteRole(role.id, dispatch, 'Deleting a role')
    } else {
      await makeDeleteRole(role.id, dispatch, 'Initiating delete role')
    }
    handleClose(null, 'close')
  }
  return (
    <div>
      <AccessControlWrapper rights={ACCESS_CONTROLS.DELETE_ROLE}>
        <MenuItem onClick={handleOpen} disableRipple>
          <Typography
            variant="label1"
            sx={{
              fontWeight: 400,
              color: '#2A3339',
            }}
          >
            Delete
          </Typography>
        </MenuItem>
      </AccessControlWrapper>

      <CustomDialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <DialogTitle
            sx={{
              fontSize: '18px',
              fontWeight: '700',
            }}
          >
            Delete Role
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <DialogContent
          sx={{
            px: '5% !important',
          }}
        >
          <Typography
            sx={{
              textAlign: 'center',
              fontSize: '16px',
              fontWeight: '400',
            }}
          >
            {role.status === 'active'
              ? `All users assigned to ${role.name} will get access to all assigned rights. Are you sure you want to proceed?`
              : `All users assigned to ${role.name} will lose access to all assigned rights. Are you sure you want to proceed?`}
          </Typography>
        </DialogContent>
        <DialogActions
          sx={{
            px: '5% !important',
          }}
        >
          <Button
            fullWidth
            size="large"
            sx={{
              mt: 3,
              fontFamily: 'Bliss Pro',
              width: '50%',
              height: '45px',
              marginRight: '20px',
              fontWeight: '700',
              fontSize: '14px',
            }}
            type="button"
            variant="outlined"
            onClick={(e) => handleClose(e, 'close')}
          >
            Cancel
          </Button>
          {isLoadingDelete ? (
            <LoadingButton />
          ) : (
            <AccessControlWrapper rights={ACCESS_CONTROLS.DELETE_ROLE}>
              <Button
                size="large"
                sx={{
                  mt: 3,
                  fontFamily: 'Bliss Pro',
                  width: '50%',
                  height: '45px',
                  fontWeight: '700',
                  fontSize: '14px',
                }}
                color="error"
                onClick={handleStatusChange}
                type="submit"
                variant="contained"
              >
                Delete
              </Button>
            </AccessControlWrapper>
          )}
        </DialogActions>
      </CustomDialog>
    </div>
  )
}
const ViewRoleDetails = (props: RoleMenuProps) => {
  const { role, setAnchorEl } = props
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  return (
    <MenuItem
      onClick={() => {
        setAnchorEl && setAnchorEl(null)
        dispatch(setSwitchToRoleDetails({ open: true, role, type: 'view' }))
        getRoleById(dispatch, role.id)
        router.push(`/roles/details/`)
      }}
    >
      <Typography
        variant="label1"
        sx={{
          fontWeight: 400,
          color: '#2A3339',
        }}
      >
        View Details
      </Typography>
    </MenuItem>
  )
}
//View permissions dialog:
export const ViewPermissions = ({ role }: { role: IRole }) => {
  const [open, setOpen] = React.useState(false)
  const handleClick = () => {
    setOpen((prevOpen) => !prevOpen)
  }
  const data = role
  const [accordionID, SetAccordionID] = React.useState<string>('')
  const rights = data
  const [search, setSearch] = React.useState<string>('')
  const [filter, setFilter] = React.useState<IPermissionGroup[]>(
    rights.permissionsGroup && rights.permissionsGroup
  )

  const handleSearch = (search: string = '') => {
    let filteredPermissionGroups = rights.permissionsGroup.filter(
      (permissionGroup: IPermissionGroup) =>
        permissionGroup.name.toLowerCase().includes(search.toLowerCase())
    )

    // If no permission group name is found, search through permissions
    if (filteredPermissionGroups.length === 0) {
      filteredPermissionGroups = rights.permissionsGroup.filter(
        (permissionGroup: IPermissionGroup) =>
          permissionGroup.permissions.some((permission: IPermission) =>
            permission.name.toLowerCase().includes(search.toLowerCase())
          )
      )
    }

    filteredPermissionGroups = filteredPermissionGroups.map(
      (permissionGroup: IPermissionGroup) => {
        const filteredPermissions = permissionGroup.permissions.filter(
          (permission: IPermission) =>
            permission.name.toLowerCase().includes(search.toLowerCase())
        )
        return {
          ...permissionGroup,
          permissions: filteredPermissions,
        }
      }
    )

    // Update the accordion ID if the currently expanded group is not in the filtered list
    if (!filteredPermissionGroups.some((group) => group.id === accordionID)) {
      SetAccordionID('')
      setFilter(filteredPermissionGroups)
    } else {
      setFilter(filteredPermissionGroups)
    }
  }
  React.useEffect(() => {
    handleSearch(search)
  }, [search])

  return (
    <>
      <CustomChip
        label={`+${role.permissions.length - 2}`}
        onClick={handleClick}
        key={role.id}
      />

      <Drawer open={open} anchor="right">
        <Paper
          elevation={0}
          sx={{
            //minWidth: drawerMinWidth,
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            gap: '10px',
            transition: 'width 3.3s ease-in-out',
          }}
        >
          <Box
            sx={{
              width: '100%',
              maxHeight: '60px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '16px 20px 8px 24px',
              borderBottom: '1px solid lightgray',
              backgroundColor: '#F9FAFB',
            }}
          >
            {/* DrawerHeader */}
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}
            >
              <Typography
                sx={{
                  fontWeight: '700',
                }}
              >
                {role.name}
              </Typography>

              {true && <CustomDrawerChip label={`${0} roles assigned`} />}
            </Box>
            <IconButton
              sx={{
                border: '1px solid #CBD5E1',
                backgroundColor: '#F1F5F9',
              }}
              onClick={() => {
                setOpen(false)
              }}
            >
              <CloseRounded
                sx={{
                  fontSize: '20px',
                }}
              />
            </IconButton>
          </Box>
          {/* DrawerChildren */}
          <Stack
            sx={{
              flexDirection: 'column',
              padding: '19px 36px',
              gap: '10px',
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                position: 'static',
              }}
            >
              <CustomSearchInput
                startAdornment={<SearchRounded />}
                placeholder="Search"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                sx={{
                  width: '100%',
                  '&.Mui-focused': {
                    width: '100%',
                    boxShadow: '0px 0px 0px 4px #D3CFDC3D',
                  },
                }}
              />
            </Box>
            <Box
              sx={{
                overflowY: 'auto',
              }}
            >
              {filter !== null &&
                filter.map((permissionGroup) => (
                  <Accordion
                    expanded={permissionGroup.id === accordionID}
                    key={permissionGroup.id}
                    elevation={0}
                    sx={{
                      border:
                        permissionGroup.id === accordionID
                          ? '1px solid lightgray'
                          : 'hidden',
                      borderRadius: '8px',
                      '&::before': {
                        backgroundColor: 'transparent',
                      },
                      //height: permissionGroup.id === accordionID ? '500px' : 'auto',
                    }}
                    onChange={() => {
                      SetAccordionID(
                        permissionGroup.id === accordionID
                          ? ''
                          : permissionGroup.id
                      )
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<KeyboardArrowDownRounded />}
                      sx={{
                        position: 'static',
                      }}
                    >
                      <Typography marginRight={1}>
                        {sentenceCase(permissionGroup.name)}
                      </Typography>
                      <CustomDrawerChip
                        label={`${permissionGroup.permissions.length} rights`}
                        sx={{
                          backgroundColor: '#F9FAFB',
                        }}
                      />
                    </AccordionSummary>
                    <AccordionDetails
                      sx={{
                        maxHeight: '500px',
                        overflow: 'auto',
                        marginLeft: '2px',
                        '&::-webkit-scrollbar': {
                          width: '6px',
                        },
                        '&::-webkit-scrollbar-track': {
                          backgroundColor: 'lightgray transparent',
                          padding: '0px 4px',
                        },
                        '&::-webkit-scrollbar-thumb': {
                          backgroundColor: 'darkgray',
                          borderRadius: '10px',
                        },
                      }}
                    >
                      {permissionGroup.permissions.map((permission, index) => (
                        <Box
                          key={permission.id}
                          sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'flex-start',
                            alignItems: 'center',
                            gap: '4px',
                            padding: '0px 32px 20px 32px',
                          }}
                        >
                          <Typography>{index + 1}. </Typography>
                          <Typography>
                            {sentenceCase(permission.name)}
                          </Typography>
                        </Box>
                      ))}
                    </AccordionDetails>
                  </Accordion>
                ))}
            </Box>
          </Stack>
        </Paper>
      </Drawer>
    </>
  )
}

export const ViewUsers = ({ users, role }: { users: IUser[]; role: IRole }) => {
  const [open, setOpen] = useState<boolean>(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [filteredUsers, setFilteredUsers] = useState<IUser[]>(users)
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value)
    const filtered = users.filter((user) => {
      return (
        user.firstName
          .toLowerCase()
          .includes(event.target.value.toLowerCase()) ||
        user.lastName
          .toLowerCase()
          .includes(event.target.value.toLowerCase()) ||
        user.email.toLowerCase().includes(event.target.value.toLowerCase())
      )
    })
    if (!event.target.value) {
      setFilteredUsers(users)
    }
    setFilteredUsers(filtered)
  }
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget) // Toggle the anchorEl
    setOpen((prevOpen) => !prevOpen)
  }
  const handleClose = () => {
    setAnchorEl(null)
    setOpen(false)
  }
  return (
    <>
      <Button
        sx={{
          color: '#555C61',
          background: '#F8F9FC',
          fontWeight: '600',
          fontSize: '12px',
          px: '0px',
          borderRadius: '36px',
          marginLeft: '4px',
        }}
        variant="text"
        key={role.id}
        onClick={handleClick}
      >
        {`+${users.length - 3}`}
      </Button>
      {/*<Portal>*/}
      <Popover
        id={role.id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Paper
          elevation={1}
          sx={{
            padding: '5%',
          }}
        >
          <Stack>
            <Typography
              sx={{
                color: '#101828',
              }}
              variant="label1"
            >
              {sentenceCase(role.name)} Users ({users.length})
            </Typography>
            <Typography
              sx={{
                fontWeight: '400',
                fontSize: '14px',
              }}
            >
              The following users have been assigned to this role.
            </Typography>
          </Stack>
          <CustomSearchInput
            sx={{
              height: '50px',
              my: '10px',
              width: 250,
              '&.Mui-focused': {
                width: 250,
                boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
              },
            }}
            value={searchValue}
            onChange={handleSearch}
            placeholder="Search"
            endAdornment={
              <InputAdornment position="start">
                <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            }
          />
          <Stack
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              height: '30vh',
              overflowY: 'scroll',
            }}
          >
            {filteredUsers.map((user) => (
              <Stack
                key={user.id}
                flexDirection="row"
                sx={{
                  gap: '12px',
                  alignContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Avatar
                  sx={{
                    color: '#344054',
                    backgroundColor: '#E7F4FF',
                    height: 40,
                    width: 40,
                    fontWeight: 600,
                  }}
                  alt={user.firstName}
                  key={user.id}
                >
                  <Typography variant="label1">
                    {`${getInitials(user.firstName)}${getInitials(
                      user.lastName
                    )}`}
                  </Typography>
                </Avatar>
                <Stack
                  flexDirection="column"
                  sx={{
                    alignContent: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography sx={{ fontWeight: 700 }} variant="label1">
                    {user.firstName} {user.lastName}
                  </Typography>
                  <Typography
                    variant="label1"
                    sx={{
                      fontWeight: 400,
                    }}
                  >
                    {user.email}
                  </Typography>
                </Stack>
              </Stack>
            ))}
          </Stack>
        </Paper>
      </Popover>
      {/*</Portal>*/}
    </>
  )
}
