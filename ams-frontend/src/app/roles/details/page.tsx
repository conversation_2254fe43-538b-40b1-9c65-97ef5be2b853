'use client'
import React, { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getRoleById, getUserById } from '@/store/actions'

import { UserDetails } from '@/app/users/details/UserDetails/UserDetails'
import { RoleDetails } from './RolesDetails/RoleDetails'
import ChangeLogDrawer from './ChangeLogDrawer'

const DetailsPage = () => {
  const dispatch = useAppDispatch()
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { switchToUserDetails, switchToRoleDetails } = useAppSelector(
    (state) => state.navigation
  )
  useEffect(() => {
    if (selectedApprovalRequest && selectedApprovalRequest.entityId) {
      selectedApprovalRequest.makerCheckerType.module === 'users'
        ? getUserById(dispatch, selectedApprovalRequest.entityId)
        : selectedApprovalRequest.makerCheckerType.module === 'groups'
          ? getRoleById(dispatch, selectedApprovalRequest.entityId)
          : console.log('NOT GROUP OR USER')
    }
  }, [selectedApprovalRequest])
  return (
    <>
      <ChangeLogDrawer />
      {(selectedApprovalRequest &&
        selectedApprovalRequest.makerCheckerType?.module === 'users') ||
      switchToUserDetails.open ? (
        <UserDetails />
      ) : (selectedApprovalRequest &&
          selectedApprovalRequest.makerCheckerType?.module === 'groups') ||
        switchToRoleDetails.open ? (
        <RoleDetails />
      ) : null}

      {}
    </>
  )
}

export default DetailsPage
