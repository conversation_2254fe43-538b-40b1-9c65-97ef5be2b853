import { jwtDecode } from 'jwt-decode'
import { IDecodeToken } from '@/store/interfaces'

/**
 * Helper function to get user's resource IDs from J<PERSON><PERSON> token for a specific resource type
 * @param resourceType - The type of resource to filter for (e.g., 'PLATFORMS', 'PRODUCTS')
 * @returns Array of resource IDs for the specified type
 */
export const getUserResourceIds = (resourceType: string): string[] => {
  try {
    const accessToken = localStorage.getItem('accessToken')
    if (accessToken) {
      const decodedToken: IDecodeToken = jwtDecode(accessToken)
      const resource = decodedToken.resources?.find(
        (resource) => resource.resourceType === resourceType
      )
      return resource?.resourceIds || []
    }
  } catch (error) {
    console.error('Error decoding token:', error)
  }
  return []
}

/**
 * Helper function to get user's platform resource IDs from JWT token
 * @returns Array of platform IDs the user has access to
 */
export const getUserPlatformResourceIds = (): string[] => {
  return getUserResourceIds('PLATFORMS')
}

/**
 * Helper function to get user's product resource IDs from JWT token
 * @returns Array of product IDs the user has access to
 */
export const getUserProductResourceIds = (): string[] => {
  return getUserResourceIds('PRODUCTS')
}

/**
 * Helper function to check if a specific resource ID exists in user's token
 * @param resourceType - The type of resource to check for
 * @param resourceId - The specific resource ID to check
 * @returns Boolean indicating if the user has access to the resource
 */
export const hasUserAccessToResource = (resourceType: string, resourceId: string): boolean => {
  const userResourceIds = getUserResourceIds(resourceType)
  return userResourceIds.includes(resourceId)
}

/**
 * Helper function to filter a list of items based on user's token resources
 * @param items - Array of items with id property
 * @param resourceType - The type of resource to filter by
 * @returns Array of items that match user's resource IDs
 */
export const filterItemsByUserResources = <T extends { id: string }>(
  items: T[],
  resourceType: string
): T[] => {
  const userResourceIds = getUserResourceIds(resourceType)
  return items.filter(item => userResourceIds.includes(item.id))
}
