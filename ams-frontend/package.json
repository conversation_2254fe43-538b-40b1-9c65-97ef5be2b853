{"name": "ams", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack --port 3005", "build": "next build", "start": "next start --port 3005", "lint": "next lint", "lint:fix": "next lint --fix", "clean": "rimraf .turbo .next __tests__/coverage", "test": "vitest run", "test:watch": "vitest --watch", "test:vitest-ui": "vitest --ui --coverage", "test:view-report": "open __tests__/coverage/index.html"}, "dependencies": {"@dtbx/store": "0.1.1", "@dtbx/typescript-config": "^0.0.1", "@dtbx/ui": "^0.0.7", "@dtbx/vitest-config": "^0.0.2", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@mui/system": "^6.1.10", "@mui/x-date-pickers": "^7.23.1", "@reduxjs/toolkit": "^2.4.0", "@types/lodash": "^4.17.13", "axios": "^1.8.4", "dayjs": "^1.11.13", "formik": "^2.4.6", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mui-tel-input": "^8.0.1", "next": "15.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-redux": "^9.1.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tiny-case": "^1.0.3", "yup": "^1.5.0"}, "devDependencies": {"@dtbx/eslint-config": "^0.0.1", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/node": "^20.17.10", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-istanbul": "^3.0.9", "eslint": "^9.16.0", "eslint-config-next": "15.2.3", "jsdom": "^25.0.1", "rimraf": "^6.0.1", "swc-plugin-coverage-instrument": "0.0.26", "typescript": "^5.8.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9"}}