'use client'

import React from 'react'
import { Box } from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import { AuthWrapper, LocalNotification } from '@dtbx/ui/components'
import { refreshToken } from '@dtbx/store/actions'
import { Navbar } from '@dtbx/ui/components/Appbar'
import { isLoggedIn } from '@dtbx/store/utils'
import { clearNotification } from '@dtbx/store/reducers'

export default function LandingLayout(props: { children: React.ReactNode }) {
  const dispatch = useAppDispatch()
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'
  return (
    <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        <Navbar profile={profile} refreshToken={refreshToken} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            width: '100%',
            overflow: 'hidden',
            backgroundColor: '#FCFCFC',
            minHeight: '100vh',
          }}
        >
          <LocalNotification
            clearNotification={() => dispatch(clearNotification())}
            notification={notification}
            notificationType={notificationType}
          />
          {props.children}
        </Box>
      </Box>
    </AuthWrapper>
  )
}
