'use client'

import { useSearchParams } from 'next/navigation'
import { Suspense, useEffect } from 'react'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { LoadingFullScreen } from '@dtbx/ui/components/Loading'
import { useAppDispatch } from '@/store'
import { handleLogin } from '@dtbx/store/actions'
import { setIsLoginError } from '@dtbx/store/reducers'

export default function AuthenticatePage() {
  const searchParams = useSearchParams()
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const accessTokenObject = searchParams.get('accessToken') || ''

  const handleUserLogin = async () => {
    const tokenObject = JSON.parse(accessTokenObject)
    if (tokenObject.success === true) {
      const route = await handleLogin(tokenObject, dispatch)
      router.replace(route)
    } else if (tokenObject.success === false) {
      if (tokenObject.statusMessage.includes('disabled'))
        dispatch(setIsLoginError(true))
      router.push('/')
    }
  }
  useEffect(() => {
    handleUserLogin()
  }, [accessTokenObject])
  return (
    <Suspense>
      <LoadingFullScreen />
    </Suspense>
  )
}
