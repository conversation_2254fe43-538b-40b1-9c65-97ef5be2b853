{"name": "landing", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "clean": "rimraf .turbo .next __tests__/coverage", "test": "vitest run", "test:watch": "vitest --watch", "test:vitest-ui": "vitest --ui --coverage", "test:view-report": "open __tests__/coverage/index.html"}, "dependencies": {"@dtbx/store": "workspace:*", "@dtbx/ui": "workspace:*", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@reduxjs/toolkit": "^2.8.2", "next": "15.2.3", "react": "19.0.0", "react-dom": "19.0.0", "redux": "^5.0.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@dtbx/eslint-config": "workspace:*", "@dtbx/typescript-config": "workspace:*", "@dtbx/vitest-config": "workspace:*", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.1.0", "@types/node": "^20.17.27", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "eslint": "^9.23.0", "eslint-config-next": "15.2.3", "@vitest/coverage-istanbul": "^3.0.9", "jsdom": "^26.0.0", "rimraf": "^6.0.1", "swc-plugin-coverage-instrument": "0.0.26", "typescript": "^5.8.2", "vitest": "^3.0.9"}}