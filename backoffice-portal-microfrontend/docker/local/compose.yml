services:
  landing:
    container_name: landing
    build:
      context: ../..
      dockerfile: './apps/landing/Dockerfile'
      args:
        BUILD_ENV: dev
    restart: always
    ports:
      - '3000:3000'
    networks:
      - app_network

  x247:
    container_name: x247
    build:
      context: ../..
      dockerfile: './apps/x247/Dockerfile'
      args:
        BUILD_ENV: dev
    restart: always
    ports:
      - '3001:3001'
    networks:
      - app_network

  lms:
    container_name: lms
    build:
      context: ../..
      dockerfile: './apps/lms/Dockerfile'
      args:
        BUILD_ENV: dev
    restart: always
    ports:
      - '3002:3002'
    networks:
      - app_network

  ams:
    container_name: ams
    build:
      context: ../..
      dockerfile: './apps/ams/Dockerfile'
      args:
        BUILD_ENV: dev
    restart: always
    ports:
      - '3005:3005'
    networks:
      - app_network

  nms:
    container_name: nms
    build:
      context: ../..
      dockerfile: './apps/nms/Dockerfile'
      args:
        BUILD_ENV: dev
    restart: always
    ports:
      - '3006:3006'
    networks:
      - app_network

  cms:
    container_name: cms
    build:
      context: ../..
      dockerfile: './apps/cms/Dockerfile'
      args:
        BUILD_ENV: dev
    restart: always
    ports:
      - '3008:3008'
    networks:
      - app_network

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  app_network:
    external: true
