{"name": "@dtbx/eslint-config", "version": "0.0.2", "files": ["library.js", "next.js", "react-internal.js"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint-config-prettier": "^10.1.8", "eslint-config-turbo": "^2.5.5", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.5.3", "prettier": "^3.6.2", "typescript": "^5.8.3"}}