{"status": "SUCCESS", "message": "Data Loaded Successfully", "errors": null, "totalElements": 2, "size": 10, "page": 0, "totalNumberOfPages": 1, "data": [{"id": "e4182490-0b8c-4b2c-a9f2-bc2bdbdf73ad", "dateCreated": "2025-03-06T16:29:46.801343", "createdBy": null, "modifiedBy": null, "lotNo": 300017, "broker": "BROKER104", "sellingMark": "SARMA", "grade": "PF1", "invoiceNo": "099872/25", "subElevation": null, "saleCode": "Sale 36 - M2", "category": "M2", "rp": "No", "ra": "Yes", "certifications": null, "bags": 40.0, "netWeight": 68.0, "totalWeight": 2720.0, "askingPrice": 1.72, "amount": 1.5, "status": "Sold", "purchasedPrice": 1.5, "buyerName": "Buyer1002", "buyerUserName": "Buyer1002", "factoryName": "SARMA", "producerCountry": "Kenya", "warehouseCompany": "SGL", "warehouseLocation": null, "manufacturedDate": "45498.0", "sellingEndTime": "23/02/2025 12:06:06:313", "producer": "ATE", "totalValue": null, "buyerCode": "Buyer1002", "buyerCompany": "Buyer1002", "buyerUser": null, "totalPrice": 4080.0, "buyer": "Buyer1002", "factory": "SARMA", "outlotSettingType": "Other Factories", "invoiceStatus": "NEW"}, {"id": "a8bdd65d-54a1-49bc-b843-8a1396c343d8", "dateCreated": "2025-03-11T12:31:56.14925", "createdBy": null, "modifiedBy": null, "lotNo": 300022, "broker": "BROKER104", "sellingMark": "SARMA", "grade": "PF1", "invoiceNo": "099877/25", "subElevation": null, "saleCode": "Sale 36 - M2", "category": "M2", "rp": "No", "ra": "Yes", "certifications": null, "bags": 40.0, "netWeight": 68.0, "totalWeight": 2720.0, "askingPrice": 1.72, "amount": 1.5, "status": "Sold", "purchasedPrice": 1.5, "buyerName": "Buyer1002", "buyerUserName": "Buyer1002", "factoryName": "SARMA", "producerCountry": "Kenya", "warehouseCompany": "SGL", "warehouseLocation": null, "manufacturedDate": "45498.0", "sellingEndTime": "23/02/2025 12:06:06:313", "producer": "ATE", "totalValue": null, "buyerCode": "Buyer1002", "buyerCompany": "Buyer1002", "buyerUser": null, "totalPrice": 4080.0, "buyer": "Buyer1002", "factory": "SARMA", "outlotSettingType": "Other Factories", "invoiceStatus": "NEW"}]}