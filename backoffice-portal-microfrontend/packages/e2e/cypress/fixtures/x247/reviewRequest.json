{"id": "18872df9-bbd3-4139-91d6-e6ab7abb06d4", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Langat", "email": "<EMAIL>", "phoneNumber": "+254 729 294292", "enabled": true, "status": "PENDING", "dateCreated": "2024-05-22 11:53:36", "roles": [{"id": "1267d94b-4224-4c03-b6c1-0710fc5e5890", "name": "LMS user", "description": "Has all lms rights and user management", "custom": true, "permissions": [{"id": "c70802b7-32c3-47a8-8a30-5991537e2668", "name": "Read Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "1a8ab49e-3d59-4432-815b-16e650eff731", "name": "Read Insurer Limit Utilization", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "0178e232-f670-44ad-8787-8ceb6301c0c4", "name": "Download Loan Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "06d63ac0-51ef-46f5-ac8e-486aa5f8cd89", "name": "super deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "e199e531-9138-41ff-aa3e-786b599d346f", "name": "Accept Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "6ff68de6-2dfb-4d1f-aaff-3fde60cd4dbb", "name": "Make Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "f5ae8c6d-cc3f-4bcc-ad64-6552bd7e1fe0", "name": "make deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "734f4769-5309-47a6-b4b3-7ca627d05fa3", "name": "Accept Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "3f80cd3f-cdd2-4076-b79c-ec31924f4f60", "name": "Read Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "7e94c089-bb0f-493f-b449-5dfd99a26e52", "name": "make update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "a152b5d8-ad55-4d91-bbdc-5d9c62d7c05f", "name": "accept activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "1a05b8fa-fe17-4d35-ab04-edd996ba5bed", "name": "reject create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "29b132e1-dd33-444a-bc1b-cae7ac216a6a", "name": "reject deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "90011f02-f399-4e7d-872d-1801348783c6", "name": "accept deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "2350ad26-a53d-4baa-96c3-3be06f07f9f6", "name": "Accept Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "81135d6b-ef62-4db0-ab9f-503b804e411c", "name": "View Insurer Limit Consumption Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "eff48c68-2cf1-4faa-97d3-2c18c3d9e9a7", "name": "Accept Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "4d88adfe-db7e-4303-8916-5bed19b7d9df", "name": "super update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "736bf248-c78d-4e2e-bc21-0d04d6391fa1", "name": "Super Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "bb4811d1-a174-420c-9413-12c0d124e7dc", "name": "Super Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "021ddb73-3fca-4143-9637-fa0b1b048e39", "name": "Accept Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "500dd161-8fee-4999-b0b3-928f3974b0c2", "name": "Make Resets API Keys", "description": null, "visible": true, "groupName": "users"}, {"id": "9cfce28a-785a-43fd-afde-d436e15eb3c9", "name": "accept create users", "description": null, "visible": true, "groupName": "users"}, {"id": "18012a8d-93c7-4e67-959e-e9b3447<PERSON>eb", "name": "accept deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "f52847e3-b15b-4a6b-9028-6db1add013a8", "name": "super activate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "87d2fe2b-b581-40f0-916d-bf1417bc1d79", "name": "reject update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "2a95ad8a-bc0a-4056-a31c-6379fef61eb1", "name": "Super Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "c5c6af61-8b07-40da-ad88-b4b53194f270", "name": "Make Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "00590625-0b4e-44ac-abe4-af85da073387", "name": "Super Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "64c1bf97-0f15-4c62-82ec-c1f60d3a9f3a", "name": "Super Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "d3a361f3-66d1-444a-8ae9-8f42dfc8cd08", "name": "accept delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "8f7f4832-bad0-4624-9620-c2bd747b0dd1", "name": "accept update users", "description": null, "visible": true, "groupName": "users"}, {"id": "a65b43cc-5689-410d-89c3-502db2c31afc", "name": "Make Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "cd184b47-721c-49f7-890b-772c95517081", "name": "Reject Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "46f527bc-b3ea-4cc1-9ed7-d03bafb3e73e", "name": "Read Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "cb7b9bbc-0bfb-443d-8c14-1d65c0e3d3f3", "name": "Make Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "2b0cbcda-4637-495e-a288-6a6cf6ea1c58", "name": "make create notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "97faa69d-956c-42c5-8e67-91820bba40ef", "name": "Accept Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "7d9b2069-9fdb-4e53-befc-00dcdf35ee5f", "name": "Reject Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "b4119de8-5518-47ed-bb11-d06da3c23e1f", "name": "Super Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "f099805d-2857-4c4a-90ae-47bac26126a4", "name": "Accept Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "a078d16e-ceda-485a-a3f4-5b9235720a82", "name": "REJECT_RESET_PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "6f5967cd-adb3-4bc8-9477-5e26aac06c2f", "name": "Make Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "259f9103-be2d-4c73-ab03-cae8c2d49dcb", "name": "super delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "0435701c-5395-4ef0-af67-75b2146ff8d8", "name": "make create users", "description": null, "visible": true, "groupName": "users"}, {"id": "bc2bf3ce-8f01-47a6-889f-cdef031d3c3e", "name": "Make Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "43cecc72-a4cb-4953-ae11-b77d79a028e5", "name": "Make Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "f2ef2744-ca27-4780-aa04-e0dc8a576954", "name": "reject update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "7f7b81dc-25b7-4eb0-918d-52ae6d0bb591", "name": "Reject Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "f61714b4-db4d-4312-b295-f4cb94be53a0", "name": "Reject Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "d5bb788c-01c6-443a-9ecb-b5fc035fc32a", "name": "super deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "b80e6628-7af5-48f6-9812-1aaea8f28dfa", "name": "accept update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "336530bd-65ff-46ea-be6e-d04d9c929565", "name": "super activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "9fc8765b-f9d1-451c-b30c-85b3f410d72e", "name": "VIEW_ALL users", "description": null, "visible": true, "groupName": "users"}, {"id": "67c4713d-7aae-44bc-9508-edd9bb40e23c", "name": "super deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "aad92931-62cf-4db1-8c83-aebc4c77da92", "name": "Reject Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "196833be-b579-4aae-9b9b-0e42b053c154", "name": "Accept Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "9e3bd9b5-7c4e-4b8e-88ef-4bf0d51e9cbe", "name": "Super Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "47f085d7-d3a2-427a-b738-9253a0c63432", "name": "Make Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "e0fc55c0-eb23-4fba-a5a7-a4b93ad182fe", "name": "Super Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "9c7369fe-6442-4788-a8db-f300c039f054", "name": "Accept Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "42c12f26-1263-4ecb-be16-e6c1ea17d2aa", "name": "reject activate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "01c3e488-93c7-47b4-bb27-dd24f569cf90", "name": "Download Insurer Limit Consumption Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "6ad1bccd-2c9d-4358-8569-a75d6319b5a9", "name": "ACCEPT_RESET_PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "b59cbe5f-0c3b-4153-921e-af5d156ac869", "name": "SUPER RESET PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "8b3b8dfd-b8a1-4cfd-8321-729a80a29bb0", "name": "super update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "0fbe9da9-1d02-453d-8aba-4ab7f3416301", "name": "super delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "c9dd326e-fa06-4344-a196-1b71b8e193ac", "name": "Super Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "19fe6402-d486-4e72-86be-28fd637ff01e", "name": "Accept Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "7dbd5c71-a056-476f-8d95-b6de2db4f038", "name": "super update users", "description": null, "visible": true, "groupName": "users"}, {"id": "53ec54ac-b217-44e9-901c-f902d841a242", "name": "Make Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "9b0cb2ab-dab0-46d8-bf00-d2fe89c6eb8d", "name": "Accept Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "2119618a-6a10-4773-baf2-15922a0a6517", "name": "accept delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "f186ee24-9955-481d-a8b2-e8ba9e44a6f3", "name": "MAKE_RESET_PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "0a0f486c-765f-45ed-8436-f54a34dc4a93", "name": "Perform Retry - KYC Validations", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "a8c3cf97-e420-4b4d-a9f2-b21607d18e9f", "name": "Make Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "ad45920a-e61a-402b-a767-4054974b599d", "name": "Super Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "64ebaa63-ee65-4b56-854d-f010deca672c", "name": "Accept Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "a5d105e8-b864-4670-8f85-8dd296ac946f", "name": "Approve Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "d0e77bfd-93fb-4d45-8546-fe00d22856b1", "name": "Accept Reseting of API Keys", "description": null, "visible": true, "groupName": "users"}, {"id": "581ef27c-531f-45eb-84e8-e11db3c937c3", "name": "Super Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "dcfbdadc-9765-4d9b-8c85-ee5167f49965", "name": "ACCEPT_RESET_PIN", "description": null, "visible": true, "groupName": "users"}, {"id": "0d9c11a1-a17f-4603-8e9b-d20b594083b4", "name": "Accept Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "2325eb9e-70c3-41ab-aaef-def961594a28", "name": "Make Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "08c78057-258c-4e4b-9fa1-a0d9ac318d2d", "name": "accept update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "079f7e23-b768-4951-a8d2-0a5e78b6316b", "name": "reject update users", "description": null, "visible": true, "groupName": "users"}, {"id": "5c27b32d-acec-4637-b2ac-eb86183e4c0f", "name": "Make Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "50fdfe8d-fb4b-4aa6-b1a8-ebb63d7da04a", "name": "VIEW_ALL groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "98fd115a-9be1-41e7-b5ef-2a65bf3428fc", "name": "Read Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "df5fb6d3-394a-4ea2-8663-6359eef4208c", "name": "accept deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "88569a46-d15f-4ea2-8c4a-08600aa949d5", "name": "make delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "5cb9c8f5-17af-4796-8534-89dd39597f07", "name": "reject deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "249ebb67-f9bf-47ac-971a-d92ed42eb4f8", "name": "Read Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "b277d1a3-ba60-466f-b775-95a8f8c5ee91", "name": "Reject Reseting of API Keys", "description": null, "visible": true, "groupName": "users"}, {"id": "cf52a6b1-c0a6-4554-9a99-fe0b7f5e014b", "name": "reject create users", "description": null, "visible": true, "groupName": "users"}, {"id": "012e7ed2-b64d-4f08-b34d-ba532bc9a23b", "name": "Reject Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "2d676da7-c7a6-4f3c-8e5a-61c9f3dc18ac", "name": "Reject Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "655cf59d-a455-4c5b-b5b0-f1722b16cc06", "name": "Reject Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "6aa860e6-7331-44f9-966c-4ea78f5d6e11", "name": "reject delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "648466f4-6c6a-4e43-b4e3-c8a0439c655f", "name": "Read Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "28c34726-e992-4423-ad4c-fafe73934490", "name": "VIEW_ALL notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "e14f2f29-dc7a-4206-bc55-e49ad45f5548", "name": "Download Loan Request Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "711bd6ba-9198-4459-ac8f-d589a162ad00", "name": "reject deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "9f9dfb6b-90d4-49d3-84af-95bf519538cc", "name": "make deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "fe7c90e4-2c07-4d21-9f07-ca58210da706", "name": "Make Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "b2351002-f9a0-4f89-975e-e064d0274033", "name": "super create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "48d5db39-173a-470d-bcb0-71b4f39d5707", "name": "Reject Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "38e809e9-4039-4326-9b9f-47280a69e12e", "name": "Reject Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "5b93b85f-22ba-48e2-97db-ab7ba0527ad1", "name": "Super Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "66b0d603-6069-468f-a72d-738a84bffd61", "name": "make deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "a6e2f9f1-6051-4391-b687-68ab2a1e74b2", "name": "accept create notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "22565166-6dfa-421e-9cf3-6bddb88ac051", "name": "View Loan Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "5c675526-78ae-4309-8e90-952b538da5a0", "name": "super create notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "6d55dc59-91d0-44bd-b237-fd9ce603aabe", "name": "make delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "ff30da21-1272-4e25-8ec7-6e7217abbfea", "name": "Super Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "3378b289-b021-4f8e-aa5b-561d40d8d09c", "name": "Reject Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "eff90ba9-913b-4b6f-841c-8dd3f39177b0", "name": "accept delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "03701dac-5f1b-4741-98fe-377fc12b77b8", "name": "Read Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "a3f88d92-8898-4be0-a153-a68d47e139e1", "name": "Reject Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "6ab69225-dd45-4d8a-9620-f6ea434834d4", "name": "Super Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "4fcf80bf-a0bd-4b31-88de-6cf3e8ee86fb", "name": "Maker Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "2870325b-c6de-493f-82d2-aabe1755b3ee", "name": "super delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "5f18054d-6d06-4c03-bae4-b8cdf47e9f30", "name": "reject delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "d88731f2-240a-4eb2-860a-85ea24add5ef", "name": "Super Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "0e66f916-2022-4086-9bf9-2048089866dd", "name": "make update users", "description": null, "visible": true, "groupName": "users"}, {"id": "f7af8c68-cba2-4948-b25f-452bbda259dc", "name": "accept activate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "d026613a-d24d-4b2c-8e4f-67aedc72f9f6", "name": "MakeUpdate Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "3fea3d6d-d083-40a7-a9dd-bd9879c680f9", "name": "Reject Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "94c03f23-2d8e-437a-9ebf-eeb72e88e059", "name": "reject activate users", "description": null, "visible": true, "groupName": "users"}, {"id": "8ab63f00-0ce1-4550-aaa9-9a00d7fa3ac9", "name": "make create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "983811db-f9dc-4398-af22-bc24e1a99659", "name": "reject activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "4b0971c6-cc32-4442-a03a-eac9ff0230a8", "name": "reject delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "71fdd329-3517-4e10-8f09-81241a170ade", "name": "accept activate users", "description": null, "visible": true, "groupName": "users"}, {"id": "5f1f3585-b33b-408f-8923-9632cd62a956", "name": "make activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "3c35dcb2-cec0-4f98-a434-ea520122dcf8", "name": "Reject Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "f803d668-2baf-44f3-8c62-77dee83ad3de", "name": "make update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "c8df98bd-a75f-4421-98ce-6a5a4b21fc67", "name": "accept create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "fe87c069-bad6-4717-87c2-7de53aeb9641", "name": "Reject Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "728eec13-90e3-4290-b995-44938fd93abf", "name": "View Loan Request Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "d83de888-451d-47a9-b21f-b1c4a3ffb642", "name": "Perform Retry - Credit Validations", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "a2a3011b-70e9-40ff-a9f6-a2b25bc5e653", "name": "super activate users", "description": null, "visible": true, "groupName": "users"}, {"id": "538d922d-50a5-4efb-b28f-2909de7ea30e", "name": "make delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "65f1cd7f-4d0b-498c-8b67-6e5bb0b80c66", "name": "super create users", "description": null, "visible": true, "groupName": "users"}, {"id": "a5738dc3-afac-4c34-b48d-32edb22fe2d9", "name": "make activate users", "description": null, "visible": true, "groupName": "users"}, {"id": "c0e91165-5241-4179-a67d-8e444b4b16c7", "name": "make activate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "2b20f2aa-7b0f-44d2-8476-0ef57c5b72c9", "name": "Accept Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "18caaa3a-aace-49e4-bba4-0b8abf84b759", "name": "VIEW_ALL modules", "description": null, "visible": true, "groupName": "modules"}, {"id": "1afa5ade-1def-4b54-a306-fd50edbbdfe2", "name": "reject create notifications", "description": null, "visible": true, "groupName": "notifications"}], "permissionsGroup": [{"id": "653a60f9-f810-40e1-b2cb-cb484c6a3829", "name": "Loans Credit Validation", "permissions": [{"id": "eff48c68-2cf1-4faa-97d3-2c18c3d9e9a7", "name": "Accept Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "736bf248-c78d-4e2e-bc21-0d04d6391fa1", "name": "Super Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "bb4811d1-a174-420c-9413-12c0d124e7dc", "name": "Super Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "46f527bc-b3ea-4cc1-9ed7-d03bafb3e73e", "name": "Read Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "cb7b9bbc-0bfb-443d-8c14-1d65c0e3d3f3", "name": "Make Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "b4119de8-5518-47ed-bb11-d06da3c23e1f", "name": "Super Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "6f5967cd-adb3-4bc8-9477-5e26aac06c2f", "name": "Make Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "7f7b81dc-25b7-4eb0-918d-52ae6d0bb591", "name": "Reject Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "196833be-b579-4aae-9b9b-0e42b053c154", "name": "Accept Update Credit Validation Record - Insurer Limit Validation Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "a5d105e8-b864-4670-8f85-8dd296ac946f", "name": "Approve Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "5c27b32d-acec-4637-b2ac-eb86183e4c0f", "name": "Make Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "655cf59d-a455-4c5b-b5b0-f1722b16cc06", "name": "Reject Create Credit Validation Record", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "48d5db39-173a-470d-bcb0-71b4f39d5707", "name": "Reject Update Credit Validation Record - CRB, Credit Approval Status", "description": null, "visible": true, "groupName": "Loans Credit Validation"}, {"id": "d83de888-451d-47a9-b21f-b1c4a3ffb642", "name": "Perform Retry - Credit Validations", "description": null, "visible": true, "groupName": "Loans Credit Validation"}]}, {"id": "72fcbb33-8f99-49b8-8198-a5f6fd9670d1", "name": "modules", "permissions": [{"id": "18caaa3a-aace-49e4-bba4-0b8abf84b759", "name": "VIEW_ALL modules", "description": null, "visible": true, "groupName": "modules"}]}, {"id": "8c0f4f37-525a-4ac0-adc2-2026c6c0b860", "name": "users", "permissions": [{"id": "f5ae8c6d-cc3f-4bcc-ad64-6552bd7e1fe0", "name": "make deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "29b132e1-dd33-444a-bc1b-cae7ac216a6a", "name": "reject deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "500dd161-8fee-4999-b0b3-928f3974b0c2", "name": "Make Resets API Keys", "description": null, "visible": true, "groupName": "users"}, {"id": "9cfce28a-785a-43fd-afde-d436e15eb3c9", "name": "accept create users", "description": null, "visible": true, "groupName": "users"}, {"id": "d3a361f3-66d1-444a-8ae9-8f42dfc8cd08", "name": "accept delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "8f7f4832-bad0-4624-9620-c2bd747b0dd1", "name": "accept update users", "description": null, "visible": true, "groupName": "users"}, {"id": "a078d16e-ceda-485a-a3f4-5b9235720a82", "name": "REJECT_RESET_PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "259f9103-be2d-4c73-ab03-cae8c2d49dcb", "name": "super delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "0435701c-5395-4ef0-af67-75b2146ff8d8", "name": "make create users", "description": null, "visible": true, "groupName": "users"}, {"id": "d5bb788c-01c6-443a-9ecb-b5fc035fc32a", "name": "super deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "9fc8765b-f9d1-451c-b30c-85b3f410d72e", "name": "VIEW_ALL users", "description": null, "visible": true, "groupName": "users"}, {"id": "6ad1bccd-2c9d-4358-8569-a75d6319b5a9", "name": "ACCEPT_RESET_PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "b59cbe5f-0c3b-4153-921e-af5d156ac869", "name": "SUPER RESET PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "7dbd5c71-a056-476f-8d95-b6de2db4f038", "name": "super update users", "description": null, "visible": true, "groupName": "users"}, {"id": "f186ee24-9955-481d-a8b2-e8ba9e44a6f3", "name": "MAKE_RESET_PASSWORD", "description": null, "visible": true, "groupName": "users"}, {"id": "d0e77bfd-93fb-4d45-8546-fe00d22856b1", "name": "Accept Reseting of API Keys", "description": null, "visible": true, "groupName": "users"}, {"id": "dcfbdadc-9765-4d9b-8c85-ee5167f49965", "name": "ACCEPT_RESET_PIN", "description": null, "visible": true, "groupName": "users"}, {"id": "079f7e23-b768-4951-a8d2-0a5e78b6316b", "name": "reject update users", "description": null, "visible": true, "groupName": "users"}, {"id": "df5fb6d3-394a-4ea2-8663-6359eef4208c", "name": "accept deactivate users", "description": null, "visible": true, "groupName": "users"}, {"id": "b277d1a3-ba60-466f-b775-95a8f8c5ee91", "name": "Reject Reseting of API Keys", "description": null, "visible": true, "groupName": "users"}, {"id": "cf52a6b1-c0a6-4554-9a99-fe0b7f5e014b", "name": "reject create users", "description": null, "visible": true, "groupName": "users"}, {"id": "6aa860e6-7331-44f9-966c-4ea78f5d6e11", "name": "reject delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "0e66f916-2022-4086-9bf9-2048089866dd", "name": "make update users", "description": null, "visible": true, "groupName": "users"}, {"id": "94c03f23-2d8e-437a-9ebf-eeb72e88e059", "name": "reject activate users", "description": null, "visible": true, "groupName": "users"}, {"id": "71fdd329-3517-4e10-8f09-81241a170ade", "name": "accept activate users", "description": null, "visible": true, "groupName": "users"}, {"id": "a2a3011b-70e9-40ff-a9f6-a2b25bc5e653", "name": "super activate users", "description": null, "visible": true, "groupName": "users"}, {"id": "538d922d-50a5-4efb-b28f-2909de7ea30e", "name": "make delete users", "description": null, "visible": true, "groupName": "users"}, {"id": "65f1cd7f-4d0b-498c-8b67-6e5bb0b80c66", "name": "super create users", "description": null, "visible": true, "groupName": "users"}, {"id": "a5738dc3-afac-4c34-b48d-32edb22fe2d9", "name": "make activate users", "description": null, "visible": true, "groupName": "users"}]}, {"id": "4663f725-da20-42c4-9bb7-3a5cdf919260", "name": "groups", "permissions": [{"id": "7e94c089-bb0f-493f-b449-5dfd99a26e52", "name": "make update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "1a05b8fa-fe17-4d35-ab04-edd996ba5bed", "name": "reject create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "90011f02-f399-4e7d-872d-1801348783c6", "name": "accept deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "f52847e3-b15b-4a6b-9028-6db1add013a8", "name": "super activate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "87d2fe2b-b581-40f0-916d-bf1417bc1d79", "name": "reject update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "b80e6628-7af5-48f6-9812-1aaea8f28dfa", "name": "accept update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "67c4713d-7aae-44bc-9508-edd9bb40e23c", "name": "super deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "42c12f26-1263-4ecb-be16-e6c1ea17d2aa", "name": "reject activate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "8b3b8dfd-b8a1-4cfd-8321-729a80a29bb0", "name": "super update groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "0fbe9da9-1d02-453d-8aba-4ab7f3416301", "name": "super delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "2119618a-6a10-4773-baf2-15922a0a6517", "name": "accept delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "50fdfe8d-fb4b-4aa6-b1a8-ebb63d7da04a", "name": "VIEW_ALL groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "88569a46-d15f-4ea2-8c4a-08600aa949d5", "name": "make delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "5cb9c8f5-17af-4796-8534-89dd39597f07", "name": "reject deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "9f9dfb6b-90d4-49d3-84af-95bf519538cc", "name": "make deactivate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "b2351002-f9a0-4f89-975e-e064d0274033", "name": "super create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "5f18054d-6d06-4c03-bae4-b8cdf47e9f30", "name": "reject delete groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "f7af8c68-cba2-4948-b25f-452bbda259dc", "name": "accept activate groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "8ab63f00-0ce1-4550-aaa9-9a00d7fa3ac9", "name": "make create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "c8df98bd-a75f-4421-98ce-6a5a4b21fc67", "name": "accept create groups", "description": null, "visible": true, "groupName": "groups"}, {"id": "c0e91165-5241-4179-a67d-8e444b4b16c7", "name": "make activate groups", "description": null, "visible": true, "groupName": "groups"}]}, {"id": "3c82010c-b913-4133-b5cf-8730aec4e5fe", "name": "Loan Request", "permissions": [{"id": "e199e531-9138-41ff-aa3e-786b599d346f", "name": "Accept Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "43cecc72-a4cb-4953-ae11-b77d79a028e5", "name": "Make Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "f61714b4-db4d-4312-b295-f4cb94be53a0", "name": "Reject Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "581ef27c-531f-45eb-84e8-e11db3c937c3", "name": "Super Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "98fd115a-9be1-41e7-b5ef-2a65bf3428fc", "name": "Read Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "ff30da21-1272-4e25-8ec7-6e7217abbfea", "name": "Super Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "4fcf80bf-a0bd-4b31-88de-6cf3e8ee86fb", "name": "Maker Create Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "fe87c069-bad6-4717-87c2-7de53aeb9641", "name": "Reject Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}, {"id": "2b20f2aa-7b0f-44d2-8476-0ef57c5b72c9", "name": "Accept Update Request Record", "description": null, "visible": true, "groupName": "Loan Request"}]}, {"id": "16300db7-3db0-49c8-82d7-e589baef07e2", "name": "Reports", "permissions": [{"id": "0178e232-f670-44ad-8787-8ceb6301c0c4", "name": "Download Loan Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "81135d6b-ef62-4db0-ab9f-503b804e411c", "name": "View Insurer Limit Consumption Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "01c3e488-93c7-47b4-bb27-dd24f569cf90", "name": "Download Insurer Limit Consumption Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "e14f2f29-dc7a-4206-bc55-e49ad45f5548", "name": "Download Loan Request Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "22565166-6dfa-421e-9cf3-6bddb88ac051", "name": "View Loan Report", "description": null, "visible": true, "groupName": "Reports"}, {"id": "728eec13-90e3-4290-b995-44938fd93abf", "name": "View Loan Request Report", "description": null, "visible": true, "groupName": "Reports"}]}, {"id": "7a98a710-c20d-4f43-9a0b-f20c2090a05a", "name": "Loan Disbursement", "permissions": [{"id": "734f4769-5309-47a6-b4b3-7ca627d05fa3", "name": "Accept Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "2a95ad8a-bc0a-4056-a31c-6379fef61eb1", "name": "Super Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "a65b43cc-5689-410d-89c3-502db2c31afc", "name": "Make Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "aad92931-62cf-4db1-8c83-aebc4c77da92", "name": "Reject Update Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "9e3bd9b5-7c4e-4b8e-88ef-4bf0d51e9cbe", "name": "Super Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "47f085d7-d3a2-427a-b738-9253a0c63432", "name": "Make Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "64ebaa63-ee65-4b56-854d-f010deca672c", "name": "Accept Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "38e809e9-4039-4326-9b9f-47280a69e12e", "name": "Reject Create Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}, {"id": "03701dac-5f1b-4741-98fe-377fc12b77b8", "name": "Read Disbursement Record", "description": null, "visible": true, "groupName": "Loan Disbursement"}]}, {"id": "006956de-5f9f-4632-b44a-82a81f65ab43", "name": "notifications", "permissions": [{"id": "06d63ac0-51ef-46f5-ac8e-486aa5f8cd89", "name": "super deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "a152b5d8-ad55-4d91-bbdc-5d9c62d7c05f", "name": "accept activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "4d88adfe-db7e-4303-8916-5bed19b7d9df", "name": "super update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "18012a8d-93c7-4e67-959e-e9b3447<PERSON>eb", "name": "accept deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "2b0cbcda-4637-495e-a288-6a6cf6ea1c58", "name": "make create notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "f2ef2744-ca27-4780-aa04-e0dc8a576954", "name": "reject update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "336530bd-65ff-46ea-be6e-d04d9c929565", "name": "super activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "08c78057-258c-4e4b-9fa1-a0d9ac318d2d", "name": "accept update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "28c34726-e992-4423-ad4c-fafe73934490", "name": "VIEW_ALL notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "711bd6ba-9198-4459-ac8f-d589a162ad00", "name": "reject deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "66b0d603-6069-468f-a72d-738a84bffd61", "name": "make deactivate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "a6e2f9f1-6051-4391-b687-68ab2a1e74b2", "name": "accept create notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "5c675526-78ae-4309-8e90-952b538da5a0", "name": "super create notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "6d55dc59-91d0-44bd-b237-fd9ce603aabe", "name": "make delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "eff90ba9-913b-4b6f-841c-8dd3f39177b0", "name": "accept delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "2870325b-c6de-493f-82d2-aabe1755b3ee", "name": "super delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "983811db-f9dc-4398-af22-bc24e1a99659", "name": "reject activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "4b0971c6-cc32-4442-a03a-eac9ff0230a8", "name": "reject delete notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "5f1f3585-b33b-408f-8923-9632cd62a956", "name": "make activate notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "f803d668-2baf-44f3-8c62-77dee83ad3de", "name": "make update notifications", "description": null, "visible": true, "groupName": "notifications"}, {"id": "1afa5ade-1def-4b54-a306-fd50edbbdfe2", "name": "reject create notifications", "description": null, "visible": true, "groupName": "notifications"}]}, {"id": "57b9ba20-97ba-4614-8903-829b20684728", "name": "Product Configs", "permissions": [{"id": "c70802b7-32c3-47a8-8a30-5991537e2668", "name": "Read Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "1a8ab49e-3d59-4432-815b-16e650eff731", "name": "Read Insurer Limit Utilization", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "6ff68de6-2dfb-4d1f-aaff-3fde60cd4dbb", "name": "Make Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "3f80cd3f-cdd2-4076-b79c-ec31924f4f60", "name": "Read Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "2350ad26-a53d-4baa-96c3-3be06f07f9f6", "name": "Accept Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "021ddb73-3fca-4143-9637-fa0b1b048e39", "name": "Accept Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "c5c6af61-8b07-40da-ad88-b4b53194f270", "name": "Make Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "64c1bf97-0f15-4c62-82ec-c1f60d3a9f3a", "name": "Super Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "cd184b47-721c-49f7-890b-772c95517081", "name": "Reject Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "7d9b2069-9fdb-4e53-befc-00dcdf35ee5f", "name": "Reject Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "f099805d-2857-4c4a-90ae-47bac26126a4", "name": "Accept Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "bc2bf3ce-8f01-47a6-889f-cdef031d3c3e", "name": "Make Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "e0fc55c0-eb23-4fba-a5a7-a4b93ad182fe", "name": "Super Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "9c7369fe-6442-4788-a8db-f300c039f054", "name": "Accept Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "c9dd326e-fa06-4344-a196-1b71b8e193ac", "name": "Super Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "19fe6402-d486-4e72-86be-28fd637ff01e", "name": "Accept Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "53ec54ac-b217-44e9-901c-f902d841a242", "name": "Make Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "9b0cb2ab-dab0-46d8-bf00-d2fe89c6eb8d", "name": "Accept Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "2325eb9e-70c3-41ab-aaef-def961594a28", "name": "Make Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "249ebb67-f9bf-47ac-971a-d92ed42eb4f8", "name": "Read Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "012e7ed2-b64d-4f08-b34d-ba532bc9a23b", "name": "Reject Update Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "fe7c90e4-2c07-4d21-9f07-ca58210da706", "name": "Make Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "5b93b85f-22ba-48e2-97db-ab7ba0527ad1", "name": "Super Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "3378b289-b021-4f8e-aa5b-561d40d8d09c", "name": "Reject Update Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "a3f88d92-8898-4be0-a153-a68d47e139e1", "name": "Reject Create Insurer Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "6ab69225-dd45-4d8a-9620-f6ea434834d4", "name": "Super Update Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "d88731f2-240a-4eb2-860a-85ea24add5ef", "name": "Super Create Product Record", "description": null, "visible": true, "groupName": "Product Configs"}, {"id": "3c35dcb2-cec0-4f98-a434-ea520122dcf8", "name": "Reject Create Broker Record", "description": null, "visible": true, "groupName": "Product Configs"}]}, {"id": "a4770878-0d6a-4b16-ae17-edb435369bfd", "name": "Loans KYC", "permissions": [{"id": "00590625-0b4e-44ac-abe4-af85da073387", "name": "Super Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "97faa69d-956c-42c5-8e67-91820bba40ef", "name": "Accept Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "0a0f486c-765f-45ed-8436-f54a34dc4a93", "name": "Perform Retry - KYC Validations", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "a8c3cf97-e420-4b4d-a9f2-b21607d18e9f", "name": "Make Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "ad45920a-e61a-402b-a767-4054974b599d", "name": "Super Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "0d9c11a1-a17f-4603-8e9b-d20b594083b4", "name": "Accept Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "2d676da7-c7a6-4f3c-8e5a-61c9f3dc18ac", "name": "Reject Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "648466f4-6c6a-4e43-b4e3-c8a0439c655f", "name": "Read Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "d026613a-d24d-4b2c-8e4f-67aedc72f9f6", "name": "MakeUpdate Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "3fea3d6d-d083-40a7-a9dd-bd9879c680f9", "name": "Reject Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}]}], "creationDate": "2024-08-02 10:09:13", "createdBy": "2d80252c-ee81-4182-8b8f-bccb4c5b2cc8"}]}