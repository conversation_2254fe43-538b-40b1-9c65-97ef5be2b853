{"pageNumber": 1, "pageSize": 15, "totalNumberOfPages": 3, "totalElements": 38, "data": [{"id": "d5f54314-3fc2-42e7-babb-121afc002624", "checker": "<PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Deactivate Users", "type": "DEACTIVATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_DEACTIVATE_USERS", "ACCEPT_DEACTIVATE_USERS"], "makerPermissions": ["MAKE_DEACTIVATE_USERS"], "overridePermissions": ["SUPER_DEACTIVATE_USERS"]}, "diff": [{"field": "status", "oldValue": "ACTIVE", "newValue": "INACTIVE"}], "status": "REJECTED", "makerComments": "", "checkerComments": "User Deactivation Rejected", "dateCreated": "2024-07-19 16:06:45", "dateModified": "2024-07-22 08:44:34", "entityId": "fd10e52b-95fc-44f9-8aca-619d8be3e357", "entity": "INACTIVE"}, {"id": "3115c8ec-591b-4604-bfa8-0fa550dee86b", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Update Users", "type": "UPDATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_UPDATE_USERS", "ACCEPT_UPDATE_USERS"], "makerPermissions": ["MAKE_UPDATE_USERS"], "overridePermissions": ["SUPER_UPDATE_USERS"]}, "diff": [{"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": "IdAM Maker", "newValue": "Id<PERSON> Checker"}, {"field": "description", "oldValue": "DTBK ITRM Users", "newValue": "DTBK COPs Authorisers"}]}], "status": "APPROVED", "makerComments": "Updating a user", "checkerComments": "User Update approved", "dateCreated": "2024-07-17 12:05:54", "dateModified": "2024-07-17 16:01:41", "entityId": "1eadb874-8922-4f2e-b7e1-c4bd064275b1", "entity": "{\"roleId\":\"73c89a70-dce5-403e-854b-1f8ca9f8e31b\",\"comments\":\"Updating a user\"}"}, {"id": "66eb64e6-2280-4fd9-9577-bcad1c17767b", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Deactivate Users", "type": "DEACTIVATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_DEACTIVATE_USERS", "ACCEPT_DEACTIVATE_USERS"], "makerPermissions": ["MAKE_DEACTIVATE_USERS"], "overridePermissions": ["SUPER_DEACTIVATE_USERS"]}, "diff": [{"field": "status", "oldValue": "ACTIVE", "newValue": "INACTIVE"}], "status": "REJECTED", "makerComments": "", "checkerComments": "User Deactivation Rejected", "dateCreated": "2024-07-17 11:01:14", "dateModified": "2024-07-19 12:01:18", "entityId": "023ff66d-c087-40c4-8637-194467b643a5", "entity": "INACTIVE"}, {"id": "105767a8-27fa-4d90-a1f9-ad482c68772f", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Create Users", "type": "CREATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_USERS", "REJECT_CREATE_USERS"], "makerPermissions": ["MAKE_CREATE_USERS"], "overridePermissions": ["SUPER_CREATE_USERS"]}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "<PERSON>"}, {"field": "lastName", "oldValue": null, "newValue": "<PERSON><PERSON><PERSON>"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "status", "oldValue": null, "newValue": "ACTIVE"}, {"field": "phoneNumber", "oldValue": null, "newValue": "+254 720607989"}, {"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "User Admin Maker"}, {"field": "description", "oldValue": null, "newValue": "Admin Users Maker Group"}, {"field": "custom", "oldValue": null, "newValue": false}, {"field": "visible", "oldValue": null, "newValue": true}]}], "status": "APPROVED", "makerComments": null, "checkerComments": "User Approved", "dateCreated": "2024-07-15 12:44:02", "dateModified": "2024-07-15 12:45:15", "entityId": "cae16b94-a8d2-4c71-bd3a-bb1afe9e4074", "entity": "{\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON>hu<PERSON>\",\"email\":\"<EMAIL>\",\"phoneNumber\":\"+254 720607989\",\"roleId\":\"934a45e4-6103-4196-9bd3-ed1582447bdf\",\"comments\":null}"}, {"id": "2132d320-5e80-44d8-92b1-1ff3477dde2a", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Create Users", "type": "CREATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_USERS", "REJECT_CREATE_USERS"], "makerPermissions": ["MAKE_CREATE_USERS"], "overridePermissions": ["SUPER_CREATE_USERS"]}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "<PERSON>"}, {"field": "lastName", "oldValue": null, "newValue": "<PERSON><PERSON><PERSON>"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "status", "oldValue": null, "newValue": "ACTIVE"}, {"field": "phoneNumber", "oldValue": null, "newValue": "+254 726797815"}, {"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "User Admin Maker"}, {"field": "description", "oldValue": null, "newValue": "Admin Users Maker Group"}, {"field": "custom", "oldValue": null, "newValue": false}, {"field": "visible", "oldValue": null, "newValue": true}]}], "status": "APPROVED", "makerComments": null, "checkerComments": "User Approved", "dateCreated": "2024-07-15 12:42:29", "dateModified": "2024-07-15 12:45:04", "entityId": "1a3dd7d4-1bfe-46ed-8499-86de27dd4267", "entity": "{\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\",\"phoneNumber\":\"+254 726797815\",\"roleId\":\"934a45e4-6103-4196-9bd3-ed1582447bdf\",\"comments\":null}"}, {"id": "65614b08-b95e-48cd-858e-8d757c81a2b2", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Deactivate Users", "type": "DEACTIVATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_DEACTIVATE_USERS", "ACCEPT_DEACTIVATE_USERS"], "makerPermissions": ["MAKE_DEACTIVATE_USERS"], "overridePermissions": ["SUPER_DEACTIVATE_USERS"]}, "diff": [{"field": "status", "oldValue": "ACTIVE", "newValue": "INACTIVE"}], "status": "REJECTED", "makerComments": "", "checkerComments": "User Deactivation Rejected", "dateCreated": "2024-07-12 11:40:45", "dateModified": "2024-07-12 15:18:36", "entityId": "beb89d6a-e0e2-421b-9a98-3f29a957a2bd", "entity": "INACTIVE"}, {"id": "f3ea4619-f49a-4449-8b92-0a67faa26e09", "checker": "Patshe<PERSON> Gikunda", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Activate Users", "type": "ACTIVATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_ACTIVATE_USERS", "REJECT_ACTIVATE_USERS"], "makerPermissions": ["MAKE_ACTIVATE_USERS"], "overridePermissions": ["SUPER_ACTIVATE_USERS"]}, "diff": [{"field": "status", "oldValue": "INACTIVE", "newValue": "ACTIVE"}], "status": "APPROVED", "makerComments": "", "checkerComments": "This is approved", "dateCreated": "2024-07-12 11:37:25", "dateModified": "2024-07-12 15:41:03", "entityId": "99d41457-6bb7-428c-b344-04242ff5f310", "entity": "ACTIVE"}, {"id": "2450c8cf-bde8-4a52-bee1-44ef9bfbe8d3", "checker": "<PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Update Users", "type": "UPDATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_UPDATE_USERS", "ACCEPT_UPDATE_USERS"], "makerPermissions": ["MAKE_UPDATE_USERS"], "overridePermissions": ["SUPER_UPDATE_USERS"]}, "diff": [{"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": "IdAM Maker", "newValue": "Business Operations Checker"}, {"field": "description", "oldValue": "DTBK ITRM Users", "newValue": "Business Operations Checker"}]}], "status": "APPROVED", "makerComments": "Updating a user", "checkerComments": "User Update approved", "dateCreated": "2024-07-11 16:41:57", "dateModified": "2024-07-11 16:43:19", "entityId": "18c74dbb-3bb9-41de-b852-b334f405eeaa", "entity": "{\"roleId\":\"bf0c3913-ca79-4843-aabb-f646dac2bd0b\",\"comments\":\"Updating a user\"}"}, {"id": "a79b5f85-9d60-4a01-a9cd-025c50dcd60c", "checker": "Patshe<PERSON> Gikunda", "maker": "Patshe<PERSON> Gikunda", "makerCheckerType": {"name": "Update Users", "type": "UPDATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_UPDATE_USERS", "ACCEPT_UPDATE_USERS"], "makerPermissions": ["MAKE_UPDATE_USERS"], "overridePermissions": ["SUPER_UPDATE_USERS"]}, "diff": [{"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": "testing 4", "newValue": "Testing Maker"}, {"field": "description", "oldValue": "testing", "newValue": "Rrxc"}]}], "status": "APPROVED", "makerComments": "Updating a user", "checkerComments": "User Update approved", "dateCreated": "2024-07-09 16:17:19", "dateModified": "2024-07-09 16:47:01", "entityId": "fb7da77d-ad99-4ea4-af33-26df6614e19f", "entity": "{\"roleId\":\"45041a46-4888-45e9-9082-89c259c93256\",\"comments\":\"Updating a user\"}"}, {"id": "acb29b10-a9b8-42c0-a58b-0cfa9f05542c", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Activate Users", "type": "ACTIVATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_ACTIVATE_USERS", "REJECT_ACTIVATE_USERS"], "makerPermissions": ["MAKE_ACTIVATE_USERS"], "overridePermissions": ["SUPER_ACTIVATE_USERS"]}, "diff": [{"field": "status", "oldValue": "INACTIVE", "newValue": "ACTIVE"}], "status": "APPROVED", "makerComments": "", "checkerComments": "User Activated", "dateCreated": "2024-07-09 15:46:40", "dateModified": "2024-07-09 15:48:14", "entityId": "999bf820-6226-4381-9992-8f3730f1bb6d", "entity": "ACTIVE"}, {"id": "d0ffe5a6-6e06-42c2-9205-fcf87a2bfda5", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Create Users", "type": "CREATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_USERS", "REJECT_CREATE_USERS"], "makerPermissions": ["MAKE_CREATE_USERS"], "overridePermissions": ["SUPER_CREATE_USERS"]}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "<PERSON>"}, {"field": "lastName", "oldValue": null, "newValue": "Ochwoto"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "status", "oldValue": null, "newValue": "ACTIVE"}, {"field": "phoneNumber", "oldValue": null, "newValue": "+254 714849666"}, {"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "IT Maintenance"}, {"field": "description", "oldValue": null, "newValue": "DTBK IT Business Systems Users"}, {"field": "custom", "oldValue": null, "newValue": false}, {"field": "visible", "oldValue": null, "newValue": true}]}], "status": "APPROVED", "makerComments": null, "checkerComments": "User Approved", "dateCreated": "2024-07-09 15:38:58", "dateModified": "2024-07-09 15:42:14", "entityId": "8ce35364-cdb6-4dd5-bfd8-542dfbc20695", "entity": "{\"firstName\":\"<PERSON>\",\"lastName\":\"Ochwo<PERSON>\",\"email\":\"<EMAIL>\",\"phoneNumber\":\"+254 714849666\",\"roleId\":\"89cf7320-5895-4e27-a341-a3afb501b4f9\",\"comments\":null}"}, {"id": "543acfad-aa28-4fbe-be1c-632f333f61fc", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Deactivate Users", "type": "DEACTIVATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_DEACTIVATE_USERS", "ACCEPT_DEACTIVATE_USERS"], "makerPermissions": ["MAKE_DEACTIVATE_USERS"], "overridePermissions": ["SUPER_DEACTIVATE_USERS"]}, "diff": [{"field": "status", "oldValue": "ACTIVE", "newValue": "INACTIVE"}], "status": "REJECTED", "makerComments": "", "checkerComments": "Cannot deactivate this user", "dateCreated": "2024-07-09 15:31:54", "dateModified": "2024-07-09 15:42:55", "entityId": "fcf642cc-b1a2-43cc-96dd-dfbef0b7f4b6", "entity": "INACTIVE"}, {"id": "022c4b7f-dd03-41df-ab9c-1866906bbaa6", "checker": "Patshe<PERSON> Gikunda", "maker": "<PERSON>", "makerCheckerType": {"name": "Create Users", "type": "CREATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_USERS", "REJECT_CREATE_USERS"], "makerPermissions": ["MAKE_CREATE_USERS"], "overridePermissions": ["SUPER_CREATE_USERS"]}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "<PERSON>"}, {"field": "lastName", "oldValue": null, "newValue": "<PERSON><PERSON>"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "status", "oldValue": null, "newValue": "ACTIVE"}, {"field": "phoneNumber", "oldValue": null, "newValue": "+254 *********"}, {"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Testing Maker"}, {"field": "description", "oldValue": null, "newValue": "Rrxc"}, {"field": "custom", "oldValue": null, "newValue": true}, {"field": "visible", "oldValue": null, "newValue": true}]}, {"field": "tokens", "oldValue": [], "newValue": null}], "status": "APPROVED", "makerComments": null, "checkerComments": "User Approved", "dateCreated": "2024-07-04 10:45:26", "dateModified": "2024-07-04 11:44:35", "entityId": "1416bec9-14ed-4400-b031-2a96748f29fe", "entity": "{\"id\":null,\"dateCreated\":null,\"dateModified\":null,\"createdBy\":null,\"updatedBy\":null,\"approvalRequest\":null,\"firstName\":\"<PERSON>\",\"lastName\":\"Mpate\",\"email\":\"<EMAIL>\",\"externalId\":null,\"status\":\"ACTIVE\",\"phoneNumber\":\"+254 *********\",\"enabled\":true,\"password\":null,\"username\":\"<EMAIL>\",\"accountNonExpired\":true,\"accountNonLocked\":true,\"credentialsNonExpired\":true}"}, {"id": "96a89241-8194-4e5b-8c32-8bd0cbb3818c", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Create Users", "type": "CREATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_USERS", "REJECT_CREATE_USERS"], "makerPermissions": ["MAKE_CREATE_USERS"], "overridePermissions": ["SUPER_CREATE_USERS"]}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "<PERSON>"}, {"field": "lastName", "oldValue": null, "newValue": "<PERSON><PERSON>"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "status", "oldValue": null, "newValue": "ACTIVE"}, {"field": "phoneNumber", "oldValue": null, "newValue": "+254 701-285351"}, {"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "IdAM Maker"}, {"field": "description", "oldValue": null, "newValue": "DTBK ITRM Users"}, {"field": "custom", "oldValue": null, "newValue": false}, {"field": "visible", "oldValue": null, "newValue": true}]}, {"field": "tokens", "oldValue": [], "newValue": null}], "status": "APPROVED", "makerComments": null, "checkerComments": "<string>", "dateCreated": "2024-07-04 10:42:36", "dateModified": "2024-07-04 16:21:34", "entityId": "7d45a5c6-a31f-4893-a06d-905d337715e7", "entity": "{\"id\":null,\"dateCreated\":null,\"dateModified\":null,\"createdBy\":null,\"updatedBy\":null,\"approvalRequest\":null,\"firstName\":\"<PERSON>\",\"lastName\":\"Chula\",\"email\":\"<EMAIL>\",\"externalId\":null,\"status\":\"ACTIVE\",\"phoneNumber\":\"+254 701-285351\",\"enabled\":true,\"password\":null,\"username\":\"<EMAIL>\",\"accountNonExpired\":true,\"accountNonLocked\":true,\"credentialsNonExpired\":true}"}, {"id": "899f9e6f-f874-48ce-9a1f-027f178822f6", "checker": "<PERSON><PERSON><PERSON>", "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Create Users", "type": "CREATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_USERS", "REJECT_CREATE_USERS"], "makerPermissions": ["MAKE_CREATE_USERS"], "overridePermissions": ["SUPER_CREATE_USERS"]}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "<PERSON>"}, {"field": "lastName", "oldValue": null, "newValue": "<PERSON><PERSON><PERSON>"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "status", "oldValue": null, "newValue": "ACTIVE"}, {"field": "phoneNumber", "oldValue": null, "newValue": "+254 729 286544"}, {"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Id<PERSON> Checker"}, {"field": "description", "oldValue": null, "newValue": "DTBK COPs Authorisers"}, {"field": "custom", "oldValue": null, "newValue": false}, {"field": "visible", "oldValue": null, "newValue": true}]}, {"field": "tokens", "oldValue": [], "newValue": null}], "status": "APPROVED", "makerComments": null, "checkerComments": "Ok", "dateCreated": "2024-07-03 15:58:59", "dateModified": "2024-07-03 16:10:56", "entityId": "b830eee2-86fe-4c3f-9a0c-39a22dbc7e5b", "entity": "{\"id\":null,\"dateCreated\":null,\"dateModified\":null,\"createdBy\":null,\"updatedBy\":null,\"approvalRequest\":null,\"firstName\":\"<PERSON>\",\"lastName\":\"<PERSON><PERSON><PERSON>\",\"email\":\"<EMAIL>\",\"externalId\":null,\"status\":\"ACTIVE\",\"phoneNumber\":\"+254 729 286544\",\"enabled\":true,\"password\":null,\"username\":\"<EMAIL>\",\"accountNonExpired\":true,\"accountNonLocked\":true,\"credentialsNonExpired\":true}"}]}