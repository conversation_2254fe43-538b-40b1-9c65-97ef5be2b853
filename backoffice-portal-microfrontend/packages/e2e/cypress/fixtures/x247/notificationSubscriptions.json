[{"accountSource": "CBS", "accountId": "**********", "thresholdAmount": 650.0, "status": "ACTIVE", "numberOfSends": 0, "event": {"id": "a82c0c2a-a0f2-4ff2-82ad-f3576d24e88b", "eventType": "ACCOUNT_DEBIT", "eventName": "Account debit event", "platform": null}, "subscribers": [{"id": "e28b6936-9960-4e0d-adc5-6797ab33a5c2", "recipient": "<EMAIL>", "deliveryMode": "EMAIL", "name": null}], "optedInDate": "2024-11-28T15:32:48.405798+03:00", "optedOutDate": "2024-11-25T16:25:16.2436+03:00"}, {"accountSource": "CBS", "accountId": "**********", "thresholdAmount": 550.0, "status": "ACTIVE", "numberOfSends": 0, "event": {"id": "4b9dae93-b926-46a8-8fe6-f3b9545253a6", "eventType": "ACCOUNT_CREDIT", "eventName": "Account credit event", "platform": null}, "subscribers": [{"id": "e28b6936-9960-4e0d-adc5-6797ab33a5c2", "recipient": "<EMAIL>", "deliveryMode": "EMAIL", "name": null}, {"id": "2af702d9-81e1-4697-937b-e4d52ea88205", "recipient": "************", "deliveryMode": "SMS", "name": null}], "optedInDate": "2024-11-27T11:20:44.240266+03:00", "optedOutDate": "2024-11-25T16:27:52.924555+03:00"}, {"accountSource": "CBS", "accountId": "**********", "alertFrequency": {"id": "8b052ffa-ebe8-4875-b049-4549de1dc86d", "name": "Daily Frequency", "interval": 1, "frequencyType": "DAY", "description": "A frequency that occurs every 1 day"}, "status": "ACTIVE", "numberOfSends": 0, "event": {"id": "0d48819f-4933-403c-a4c4-db0cf8d6a6d0", "eventType": "ACCOUNT_STATEMENT", "eventName": "Account statement event", "platform": null}, "subscribers": [{"id": "150538e8-13f9-40bb-a62c-0ea50c2dff56", "recipient": "<EMAIL>", "deliveryMode": "EMAIL", "name": null}], "optedInDate": "2024-11-29T10:50:09.74216+03:00", "optedOutDate": "2024-11-28T16:24:44.050786+03:00"}, {"accountSource": "CBS", "accountId": "**********", "alertFrequency": {"id": "8b052ffa-ebe8-4875-b049-4549de1dc86d", "name": "Daily Frequency", "interval": 1, "frequencyType": "DAY", "description": "A frequency that occurs every 1 day"}, "thresholdAmount": 500.0, "status": "ACTIVE", "numberOfSends": 0, "nextSendTime": "2024-12-04T12:04:00+03:00", "firstSendTime": "2024-11-29T16:05:00+03:00", "event": {"id": "db5795a8-41d8-45dc-b216-1cfb3662c269", "eventType": "BALANCE_ALERT", "eventName": "Account balance event", "platform": null}, "subscribers": [{"id": "b322a6b0-b271-434b-abdf-7fc521db370f", "recipient": "<EMAIL>", "deliveryMode": "EMAIL", "name": null}], "optedInDate": "2024-11-28T16:05:51.042814+03:00"}, {"accountSource": "CBS", "accountId": "**********", "thresholdAmount": 500.0, "status": "ACTIVE", "numberOfSends": 0, "event": {"id": "1f14b3c4-56c3-4534-a4cc-794517b2598e", "eventType": "OVERDRAFT_ALERT", "eventName": "Account overdraft event", "platform": null}, "subscribers": [{"id": "b322a6b0-b271-434b-abdf-7fc521db370f", "recipient": "<EMAIL>", "deliveryMode": "EMAIL", "name": null}], "optedInDate": "2024-12-06T10:45:03.517953+03:00"}]