{"pageNumber": 1, "pageSize": 30, "totalNumberOfPages": 1, "totalElements": 20, "data": [{"id": "8d9dc2a2-1dc4-4e85-9732-fa17ade9a8b6", "dateCreated": "2024-08-22T10:41:29", "dateModified": "2024-08-22T10:41:29", "createdBy": null, "modifiedBy": null, "code": "PRO627079531", "name": "Test Product 5", "country": "KE", "currency": "KES", "organization": {"id": "d9517eac-1149-40a0-8a2b-fd6e39b28064", "dateCreated": "2024-07-30T07:47:47", "dateModified": "2024-08-06T08:19:12", "createdBy": null, "modifiedBy": null, "name": "Kraken Company", "cbsIdentifier": "657657", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": null, "exposureLimit": null, "status": "Active", "customerType": "Individual", "minimumAmount": 2000.0, "maximumAmount": 12000000.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 10, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 0.0, "facilityFee": 0.0, "exciseDuty": 0.0, "facilityFeeRecoveryType": null, "interestRecoveryType": "Spread", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": null, "prepaymentValue": 0.0, "penalty": null, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 0, "earlyPaymentsAllowed": false, "periodInArrears": 1, "interestGl": "************", "facilityFeeGl": "", "exciseDutyGl": "", "rollOverGl": "", "penaltyGl": "", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "N/A", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO632827834", "disbursementCreditAccount": "7768868", "disbursementCreditAccountBranch": "006", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 0.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": false, "externalProductName": "IPFA", "loanCreationBranch": "Default Branch", "loanCreationBranchValue": "118", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "74abb55a-453e-4edf-a776-eb1a73b221ee", "dateCreated": "2024-08-14T13:48:21", "dateModified": "2024-08-14T13:48:21", "createdBy": null, "modifiedBy": null, "code": "PRO559539209", "name": "J-CARE IPF 2", "country": "KE", "currency": "KES", "organization": {"id": "6f45d2f9-ddf5-4da4-b063-7d556fa88dcb", "dateCreated": "2024-04-17T12:52:53", "dateModified": "2024-05-15T09:01:15", "createdBy": null, "modifiedBy": null, "name": "Jubilee", "cbsIdentifier": "JICL,JHIL", "limit": "*********.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": null, "exposureLimit": null, "status": "Active", "customerType": "Individual", "minimumAmount": 1.0, "maximumAmount": 10000000.0, "measureOfTenure": "Months", "minimumTenure": 2, "maximumTenure": 10, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 10.5, "facilityFee": 0.0, "exciseDuty": 0.0, "facilityFeeRecoveryType": null, "interestRecoveryType": "Spread", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": null, "prepaymentValue": 0.0, "penalty": null, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 0, "earlyPaymentsAllowed": false, "periodInArrears": 1, "interestGl": "IN134008", "facilityFeeGl": "", "exciseDutyGl": "", "rollOverGl": "", "penaltyGl": "", "prepaymentGl": "**********:052", "disbursementGl": "AS171001", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "N/A", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "IPFA", "disbursementCreditAccount": "**********", "disbursementCreditAccountBranch": "052", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": true, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 5000.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": false, "externalProductName": "JICL", "loanCreationBranch": "Default Branch", "loanCreationBranchValue": "118", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": true, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "137804d6-91de-4280-821d-1f907b33b75d", "dateCreated": "2024-08-14T07:15:38", "dateModified": "2024-08-14T07:15:38", "createdBy": null, "modifiedBy": null, "code": "PRO842623297", "name": "JTest 2", "country": "KE", "currency": "KES", "organization": {"id": "d9517eac-1149-40a0-8a2b-fd6e39b28064", "dateCreated": "2024-07-30T07:47:47", "dateModified": "2024-08-06T08:19:12", "createdBy": null, "modifiedBy": null, "name": "Kraken Company", "cbsIdentifier": "657657", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": null, "exposureLimit": null, "status": "Active", "customerType": "Individual", "minimumAmount": 1.0, "maximumAmount": 1.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 1, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 0.0, "facilityFee": 0.0, "exciseDuty": 0.0, "facilityFeeRecoveryType": null, "interestRecoveryType": "Spread", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": null, "prepaymentValue": 0.0, "penalty": null, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 0, "earlyPaymentsAllowed": false, "periodInArrears": 1, "interestGl": "************", "facilityFeeGl": "", "exciseDutyGl": "", "rollOverGl": "", "penaltyGl": "", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "N/A", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO632827834", "disbursementCreditAccount": "7768868", "disbursementCreditAccountBranch": "", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 0.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": false, "externalProductName": "IPF3", "loanCreationBranch": "Default Branch", "loanCreationBranchValue": "118", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "589c2209-176e-405c-baaf-74c1154e776a", "dateCreated": "2024-08-14T07:14:06", "dateModified": "2024-08-14T07:14:06", "createdBy": null, "modifiedBy": null, "code": "PRO181582060", "name": "JTest Copy", "country": "KE", "currency": "KES", "organization": {"id": "d9517eac-1149-40a0-8a2b-fd6e39b28064", "dateCreated": "2024-07-30T07:47:47", "dateModified": "2024-08-06T08:19:12", "createdBy": null, "modifiedBy": null, "name": "Kraken Company", "cbsIdentifier": "657657", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": null, "exposureLimit": null, "status": "Active", "customerType": "Individual", "minimumAmount": 1.0, "maximumAmount": 1.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 1, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 0.0, "facilityFee": 0.0, "exciseDuty": 0.0, "facilityFeeRecoveryType": null, "interestRecoveryType": "Spread", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": null, "prepaymentValue": 0.0, "penalty": null, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 0, "earlyPaymentsAllowed": false, "periodInArrears": 1, "interestGl": "************", "facilityFeeGl": "", "exciseDutyGl": "", "rollOverGl": "", "penaltyGl": "", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "N/A", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO011459053", "disbursementCreditAccount": "7768868", "disbursementCreditAccountBranch": "", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 0.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": false, "externalProductName": "IPFB2", "loanCreationBranch": "Default Branch", "loanCreationBranchValue": "118", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "94fe0433-ca92-40dc-976e-ce1a36a59de9", "dateCreated": "2024-08-14T07:05:52", "dateModified": "2024-08-14T07:05:52", "createdBy": null, "modifiedBy": null, "code": "PRO988165910", "name": "JTest", "country": "KE", "currency": "KES", "organization": {"id": "6f45d2f9-ddf5-4da4-b063-7d556fa88dcb", "dateCreated": "2024-04-17T12:52:53", "dateModified": "2024-05-15T09:01:15", "createdBy": null, "modifiedBy": null, "name": "Jubilee", "cbsIdentifier": "JICL,JHIL", "limit": "*********.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": null, "exposureLimit": null, "status": "Active", "customerType": "Individual", "minimumAmount": 1.0, "maximumAmount": 1.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 1, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 0.0, "facilityFee": 0.0, "exciseDuty": 0.0, "facilityFeeRecoveryType": null, "interestRecoveryType": "Spread", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": null, "prepaymentValue": 0.0, "penalty": null, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 0, "earlyPaymentsAllowed": false, "periodInArrears": 1, "interestGl": "************", "facilityFeeGl": "", "exciseDutyGl": "", "rollOverGl": "", "penaltyGl": "", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "N/A", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO675145339", "disbursementCreditAccount": "7768868", "disbursementCreditAccountBranch": "", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 0.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": false, "externalProductName": "IPFB", "loanCreationBranch": "Default Branch", "loanCreationBranchValue": "118", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "3e08003f-24d7-4928-bdeb-51d6a372e545", "dateCreated": "2024-08-01T11:59:01", "dateModified": "2024-08-07T09:09:14", "createdBy": null, "modifiedBy": null, "code": "PRO543823535", "name": "Somaks", "country": "KE", "currency": "KES", "organization": {"id": "6782a8ba-900a-481d-8faa-6a8c9f3365e2", "dateCreated": "2024-07-22T11:46:08", "dateModified": "2024-07-22T11:46:08", "createdBy": null, "modifiedBy": null, "name": "Urbann Vacations", "cbsIdentifier": "1234", "limit": "1000000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2025-08-01", "exposureLimit": 10000.0, "status": "Active", "customerType": "Individual", "minimumAmount": 1000.0, "maximumAmount": 1000000.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 12, "interestRateType": "Dynamic", "dynamicInterestFixedComponent": 0.0, "interestRate": 10.5, "facilityFee": 100.0, "exciseDuty": 100.0, "facilityFeeRecoveryType": "Spread", "interestRecoveryType": "Spread", "rollOverFee": 100.0, "rollOverPeriod": 2, "maxRollOverCount": 10, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 20000.0, "penalty": 10.0, "interestCalculation": "Accrued", "amortizationMode": "BulletPayment", "multipleDrawDown": true, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 10, "earlyPaymentsAllowed": false, "periodInArrears": 12, "interestGl": "********", "facilityFeeGl": "********", "exciseDutyGl": "********", "rollOverGl": "********", "penaltyGl": "********", "prepaymentGl": "********", "disbursementGl": "********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": "PRO343850376", "disbursementCreditAccount": "********", "disbursementCreditAccountBranch": "052", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": true, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 5000.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": true, "externalProductName": "Somak", "loanCreationBranch": "Nominated Customer Account", "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "1f2f1de2-1670-483a-9707-b732d89c8418", "dateCreated": "2024-08-01T11:48:28", "dateModified": "2024-08-01T11:48:28", "createdBy": null, "modifiedBy": null, "code": "PRO349922537", "name": "Red 3 Loan", "country": "KE", "currency": "KES", "organization": {"id": "241fdb12-37a9-4e0c-982e-42132955b188", "dateCreated": "2024-04-17T12:29:26", "dateModified": "2024-04-17T12:29:26", "createdBy": null, "modifiedBy": null, "name": "Red Company", "cbsIdentifier": "********", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 10000.0, "status": "Active", "customerType": "Both", "minimumAmount": 10.0, "maximumAmount": 10000.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 12, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 10.0, "rollOverPeriod": 10, "maxRollOverCount": 10, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 10.0, "penalty": 10.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 10, "earlyPaymentsAllowed": false, "periodInArrears": 10, "interestGl": "************", "facilityFeeGl": "************", "exciseDutyGl": "************", "rollOverGl": "************", "penaltyGl": "************", "prepaymentGl": "************", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "********", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO137652034", "disbursementCreditAccount": "7768868", "disbursementCreditAccountBranch": "Head Office", "isManaged": false, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": 0.0, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": "Red3", "loanCreationBranch": "Nominated Customer Account", "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "383f68ee-1c7e-4697-ba60-d4c366305d05", "dateCreated": "2024-08-01T09:25:45", "dateModified": "2024-08-05T12:33:17", "createdBy": null, "modifiedBy": null, "code": "PRO113868858", "name": "Red 2 Loan", "country": "KE", "currency": "KES", "organization": {"id": "241fdb12-37a9-4e0c-982e-42132955b188", "dateCreated": "2024-04-17T12:29:26", "dateModified": "2024-04-17T12:29:26", "createdBy": null, "modifiedBy": null, "name": "Red Company", "cbsIdentifier": "********", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 1100000.0, "status": "Active", "customerType": "Both", "minimumAmount": 10.0, "maximumAmount": 100000.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 12, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 10.0, "rollOverPeriod": 10, "maxRollOverCount": 10, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 10.0, "penalty": 10.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 10, "earlyPaymentsAllowed": true, "periodInArrears": 10, "interestGl": "************", "facilityFeeGl": "********", "exciseDutyGl": "8674566", "rollOverGl": "********", "penaltyGl": "********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "********", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO487579791", "disbursementCreditAccount": "7768868", "disbursementCreditAccountBranch": "", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": "Red2", "loanCreationBranch": "Nominated Customer Account", "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "6d9c7aea-08e0-4e92-848a-ac8b0abfff11", "dateCreated": "2024-08-01T08:33:03", "dateModified": "2024-08-01T08:33:03", "createdBy": null, "modifiedBy": null, "code": "PRO553182683", "name": "Red1 Loan", "country": "KE", "currency": "KES", "organization": {"id": "241fdb12-37a9-4e0c-982e-42132955b188", "dateCreated": "2024-04-17T12:29:26", "dateModified": "2024-04-17T12:29:26", "createdBy": null, "modifiedBy": null, "name": "Red Company", "cbsIdentifier": "********", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 1100000.0, "status": "Active", "customerType": "Both", "minimumAmount": 10.0, "maximumAmount": 100000.0, "measureOfTenure": "Months", "minimumTenure": 1, "maximumTenure": 12, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 10.0, "rollOverPeriod": 10, "maxRollOverCount": 10, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 10.0, "penalty": 10.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 10, "earlyPaymentsAllowed": true, "periodInArrears": 10, "interestGl": "************", "facilityFeeGl": "********", "exciseDutyGl": "8674566", "rollOverGl": "********", "penaltyGl": "********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "********", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO487579791", "disbursementCreditAccount": "7768868", "disbursementCreditAccountBranch": "", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": 0.0, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": "RED1", "loanCreationBranch": "Nominated Customer Account", "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "8d19a225-3840-45a8-ad45-f0d0740003c9", "dateCreated": "2024-08-01T08:09:38", "dateModified": "2024-08-01T08:09:38", "createdBy": null, "modifiedBy": null, "code": "PRO650031849", "name": "Jubilee4 Copy", "country": "KE", "currency": "KES", "organization": {"id": "4b56b770-0580-4dc2-a543-caa457e0df5d", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-17T09:25:23", "createdBy": null, "modifiedBy": null, "name": "Jubilee2", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 400000.0, "status": "Active", "customerType": "Both", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 2, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": true, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": "*********", "disbursementCreditAccountBranch": "HO", "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": null, "loanCreationBranch": null, "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "fa74bb7d-b075-42eb-951b-be133e017ce4", "dateCreated": "2024-07-31T12:34:29", "dateModified": "2024-07-31T12:34:29", "createdBy": null, "modifiedBy": null, "code": "PRO433627613", "name": "Jubilee3 Copy", "country": "KE", "currency": "KES", "organization": {"id": "4b56b770-0580-4dc2-a543-caa457e0df5d", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-17T09:25:23", "createdBy": null, "modifiedBy": null, "name": "Jubilee2", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 400000.0, "status": "Active", "customerType": "Both", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 2, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": true, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": "*********", "disbursementCreditAccountBranch": "HO", "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": "J32HOK", "loanCreationBranch": "Nominated Customer Account", "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "2e541aeb-9933-4d66-9ad9-080673a50f9a", "dateCreated": "2024-07-30T11:38:29", "dateModified": "2024-08-06T07:18:08", "createdBy": null, "modifiedBy": null, "code": "PRO651155723", "name": "Testing 2", "country": "KE", "currency": "KES", "organization": {"id": "4b56b770-0580-4dc2-a543-caa457e0df5d", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-17T09:25:23", "createdBy": null, "modifiedBy": null, "name": "Jubilee2", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 400000.0, "status": "Active", "customerType": "Individual", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 2, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": true, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": "*********", "disbursementCreditAccountBranch": "HO", "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": "TESTING testing", "loanCreationBranch": null, "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "07729b4b-48f2-47a5-90f7-fe03dc2271a7", "dateCreated": "2024-07-05T09:30:53", "dateModified": "2024-07-05T09:30:53", "createdBy": null, "modifiedBy": null, "code": "PRO729829084", "name": "Jubilee2 Copy", "country": "KE", "currency": "KES", "organization": {"id": "4b56b770-0580-4dc2-a543-caa457e0df5d", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-17T09:25:23", "createdBy": null, "modifiedBy": null, "name": "Jubilee2", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 400000.0, "status": "Active", "customerType": "Both", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 2, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": true, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": "*********", "disbursementCreditAccountBranch": "HO", "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": null, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": true, "externalProductName": "J2Copy", "loanCreationBranch": "Nominated Customer Account", "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "bef5ace8-a655-4417-8759-5322027a5dcd", "dateCreated": "2024-06-25T07:03:25", "dateModified": "2024-08-05T12:33:35", "createdBy": null, "modifiedBy": null, "code": "PRO38143313", "name": "Red Care Updat", "country": "KE", "currency": "KES", "organization": {"id": "241fdb12-37a9-4e0c-982e-42132955b188", "dateCreated": "2024-04-17T12:29:26", "dateModified": "2024-04-17T12:29:26", "createdBy": null, "modifiedBy": null, "name": "Red Company", "cbsIdentifier": "********", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-11-30", "exposureLimit": 10000.0, "status": "Active", "customerType": "Individual", "minimumAmount": 1000.0, "maximumAmount": 100000.0, "measureOfTenure": "Weeks", "minimumTenure": 1, "maximumTenure": 10, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 10.0, "rollOverPeriod": 10, "maxRollOverCount": 10, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 10.0, "penalty": 10.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Weeks", "numberOfInstallments": 10, "earlyPaymentsAllowed": true, "periodInArrears": 10, "interestGl": "************", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "********", "penaltyGl": "********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "********", "gracePeriodType": "None", "hasRecoveryTracking": false, "externalProductCode": "PRO58909466", "disbursementCreditAccount": "*********", "disbursementCreditAccountBranch": "HO", "isManaged": true, "manualApprovalAmount": 0.0, "hasMinimumInterest": false, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 0.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": true, "externalProductName": "RCare", "loanCreationBranch": "Nominated Customer Account", "loanCreationBranchValue": "", "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "f696a262-ced8-4001-9513-1063903cca11", "dateCreated": "2024-05-09T13:22:15", "dateModified": "2024-05-09T13:22:15", "createdBy": null, "modifiedBy": null, "code": "PR0000002", "name": "Merchant Finance", "country": "KE", "currency": "KES", "organization": {"id": "979cba1d-e557-4c92-b53c-71a82251dfc5", "dateCreated": "2024-05-09T13:00:03", "dateModified": "2024-05-09T13:00:03", "createdBy": null, "modifiedBy": null, "name": "Safaricom", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "57af591b-3d43-4c78-8b61-69a4b7af3e2f", "dateCreated": "2024-05-09T13:15:14", "dateModified": "2024-05-09T13:15:14", "code": "PT00002", "name": "SME Lending", "parentCategory": {"id": "43e49722-6177-48af-a270-264f849f8486", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00002", "name": "SME Lending"}}, "expiryDate": "2030-10-31", "exposureLimit": 40000000.0, "status": "Active", "customerType": "Both", "minimumAmount": 1000.0, "maximumAmount": 250000.0, "measureOfTenure": "Days", "minimumTenure": 1, "maximumTenure": 14, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 5.0, "facilityFee": 0.0, "exciseDuty": 20.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": false, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": "string", "disbursementCreditAccount": "string", "disbursementCreditAccountBranch": "string", "isManaged": false, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": null, "loanCreationBranch": null, "loanCreationBranchValue": null, "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "368031ca-15be-4c9c-8553-790894e0be35", "dateCreated": "2024-04-18T11:43:20", "dateModified": "2024-04-18T11:43:20", "createdBy": null, "modifiedBy": null, "code": "PR09193612", "name": "JTlee IPF", "country": "KE", "currency": "KES", "organization": {"id": "241fdb12-37a9-4e0c-982e-42132955b188", "dateCreated": "2024-04-17T12:29:26", "dateModified": "2024-04-17T12:29:26", "createdBy": null, "modifiedBy": null, "name": "Red Company", "cbsIdentifier": "********", "limit": "10000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 700000.0, "status": "Active", "customerType": "Both", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": false, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": null, "disbursementCreditAccountBranch": null, "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": null, "loanCreationBranch": null, "loanCreationBranchValue": null, "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "2a46c328-327d-461a-9f55-87d416c06493", "dateCreated": "2024-04-16T07:54:40", "dateModified": "2024-04-16T07:54:40", "createdBy": null, "modifiedBy": null, "code": "PR09121212", "name": "Jubilee2 IPF", "country": "KE", "currency": "KES", "organization": {"id": "4b56b770-0580-4dc2-a543-caa457e0df5d", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-17T09:25:23", "createdBy": null, "modifiedBy": null, "name": "Jubilee2", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "d139527c-4cd4-4e8b-bf2d-3f406c6c93af", "dateCreated": "2024-04-15T13:58:34", "dateModified": "2024-04-15T13:58:34", "code": "PC000026", "name": "Personal Lending", "parentCategory": {"id": "d6d9e86a-f150-44f5-8082-f9e2454172f7", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-07-15T09:36:22", "code": "PC000021", "name": "Personal Lending Test"}}, "expiryDate": "2024-10-31", "exposureLimit": 400000.0, "status": "Active", "customerType": "Both", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": false, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": null, "disbursementCreditAccountBranch": null, "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": null, "loanCreationBranch": null, "loanCreationBranchValue": null, "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "13e991e9-504c-4ecc-82b7-18298ebf76ec", "dateCreated": "2024-04-11T00:00:00", "dateModified": "2024-04-11T00:00:00", "createdBy": null, "modifiedBy": null, "code": "PR09121214", "name": "Jubilee IPFD", "country": "KE", "currency": "KES", "organization": {"id": "4b56b770-0580-4dc2-a543-caa457e0df5d", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-17T09:25:23", "createdBy": null, "modifiedBy": null, "name": "Jubilee2", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 400000.0, "status": "Active", "customerType": "Both", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 2, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": false, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": null, "disbursementCreditAccountBranch": null, "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": null, "loanCreationBranch": null, "loanCreationBranchValue": null, "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "006a3f34-29f2-479c-a48f-a25c3752796a", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-09T00:00:00", "createdBy": null, "modifiedBy": null, "code": "PR09121213", "name": "Jubilee IPFD", "country": "KE", "currency": "KES", "organization": {"id": "4b56b770-0580-4dc2-a543-caa457e0df5d", "dateCreated": "2024-04-09T00:00:00", "dateModified": "2024-04-17T09:25:23", "createdBy": null, "modifiedBy": null, "name": "Jubilee2", "cbsIdentifier": "********", "limit": "100000.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2024-10-31", "exposureLimit": 400000.0, "status": "Active", "customerType": "Both", "minimumAmount": 100.0, "maximumAmount": 100.0, "measureOfTenure": "Days", "minimumTenure": 2, "maximumTenure": 2, "interestRateType": "Static", "dynamicInterestFixedComponent": 100.0, "interestRate": 10.0, "facilityFee": 10.0, "exciseDuty": 10.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 2, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "FlatAmount", "prepaymentValue": 100.0, "penalty": 100.0, "interestCalculation": "Simple", "amortizationMode": "FlatRate", "multipleDrawDown": false, "tranches": false, "trancheInterval": null, "repaymentCycle": "Days", "numberOfInstallments": 1, "earlyPaymentsAllowed": false, "periodInArrears": 2, "interestGl": "**********", "facilityFeeGl": "**********", "exciseDutyGl": "**********", "rollOverGl": "**********", "penaltyGl": "**********", "prepaymentGl": "**********", "disbursementGl": "**********", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "**********", "gracePeriodType": "Principal", "hasRecoveryTracking": false, "externalProductCode": null, "disbursementCreditAccount": null, "disbursementCreditAccountBranch": null, "isManaged": true, "manualApprovalAmount": null, "hasMinimumInterest": false, "minimumInterestRecoveryType": null, "minimumInterestValue": null, "minimumInterestCalculationMode": null, "hasPrepayment": true, "externalProductName": null, "loanCreationBranch": null, "loanCreationBranchValue": null, "upfrontInterestLiabilityGL": null, "upfrontInterestRecognitionType": null, "autoSubscribe": false, "existingArrearsOverdueDays": 0, "defaultBranchCode": null}, {"id": "7d8dbd81-4b7e-4418-a354-0b4c387da5b4", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-04-30T06:41:44", "createdBy": "system", "modifiedBy": null, "code": "PR0000001", "name": "Jubilee IPF", "country": "KE", "currency": "KES", "organization": {"id": "6f45d2f9-ddf5-4da4-b063-7d556fa88dcb", "dateCreated": "2024-04-17T12:52:53", "dateModified": "2024-05-15T09:01:15", "createdBy": null, "modifiedBy": null, "name": "Jubilee", "cbsIdentifier": "JICL,JHIL", "limit": "*********.00"}, "type": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb5", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PT00001", "name": "Retail Insurance Finance", "parentCategory": {"id": "cf7d9176-2ace-4ee3-9cb6-82c0bdd30fb9", "dateCreated": "2024-03-26T00:00:00", "dateModified": "2024-03-26T00:00:00", "createdBy": "system", "code": "PC00001", "name": "Personal Lending"}}, "expiryDate": "2050-05-05", "exposureLimit": null, "status": "Active", "customerType": "Individual", "minimumAmount": 3000.0, "maximumAmount": 2000000.0, "measureOfTenure": "Months", "minimumTenure": 2, "maximumTenure": 10, "interestRateType": "Static", "dynamicInterestFixedComponent": 0.0, "interestRate": 21.0, "facilityFee": 0.0, "exciseDuty": 0.0, "facilityFeeRecoveryType": "Upfront", "interestRecoveryType": "Upfront", "rollOverFee": 0.0, "rollOverPeriod": 0, "maxRollOverCount": 0, "prepaymentType": "Inclusive", "prepaymentCalculation": "Instalment Value", "prepaymentValue": 1.0, "penalty": null, "interestCalculation": "Simple", "amortizationMode": "ReducingBalance", "multipleDrawDown": true, "tranches": false, "trancheInterval": null, "repaymentCycle": "Months", "numberOfInstallments": 1, "earlyPaymentsAllowed": true, "periodInArrears": 14, "interestGl": "IN134008", "facilityFeeGl": null, "exciseDutyGl": "e", "rollOverGl": null, "penaltyGl": "0", "prepaymentGl": "**********:052", "disbursementGl": "disbursementGl", "disbursementCreditAccountType": "TenantCbsAccount", "repaymentGl": "repaymentGL", "gracePeriodType": "Pure", "hasRecoveryTracking": false, "externalProductCode": "IPFA", "disbursementCreditAccount": "**********", "disbursementCreditAccountBranch": "052", "isManaged": true, "manualApprovalAmount": 1000000.0, "hasMinimumInterest": true, "minimumInterestRecoveryType": "Upfront", "minimumInterestValue": 5000.0, "minimumInterestCalculationMode": "Flat Amount", "hasPrepayment": true, "externalProductName": "JICL", "loanCreationBranch": "Default Branch", "loanCreationBranchValue": "118", "upfrontInterestLiabilityGL": "LI444006", "upfrontInterestRecognitionType": "Amortized", "autoSubscribe": true, "existingArrearsOverdueDays": 2, "defaultBranchCode": null}]}