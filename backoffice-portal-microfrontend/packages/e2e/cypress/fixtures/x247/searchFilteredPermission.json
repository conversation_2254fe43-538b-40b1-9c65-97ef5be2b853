{"pageNumber": 1, "pageSize": 10, "totalNumberOfPages": 4, "totalElements": 32, "data": [{"id": "525b95cf-656f-43f7-b6aa-4400450d4dbc", "name": "View Customers Reports", "description": null, "visible": true, "groupName": "X247 Reports"}, {"id": "0d9c11a1-a17f-4603-8e9b-d20b594083b4", "name": "Accept Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "97faa69d-956c-42c5-8e67-91820bba40ef", "name": "Accept Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "648466f4-6c6a-4e43-b4e3-c8a0439c655f", "name": "Read Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "ad45920a-e61a-402b-a767-4054974b599d", "name": "Super Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "d026613a-d24d-4b2c-8e4f-67aedc72f9f6", "name": "MakeUpdate Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "3fea3d6d-d083-40a7-a9dd-bd9879c680f9", "name": "Reject Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "2d676da7-c7a6-4f3c-8e5a-61c9f3dc18ac", "name": "Reject Update Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}, {"id": "1fcfeb84-a457-43a4-97b3-6e922d5624d5", "name": "VIEW_ALL Customers", "description": null, "visible": true, "groupName": "Customers"}, {"id": "a8c3cf97-e420-4b4d-a9f2-b21607d18e9f", "name": "Make Create Customer KYC Validation Record", "description": null, "visible": true, "groupName": "Loans KYC"}]}