{"pageNumber": 1, "pageSize": 15, "totalNumberOfPages": 2, "totalElements": 17, "data": [{"id": "9de451b9-68c8-4917-a567-916461609efd", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "eventId", "oldValue": null, "newValue": "1f14b3c4-56c3-4534-a4cc-794517b2598e"}, {"field": "accountSource", "oldValue": null, "newValue": "CBS"}, {"field": "accountId", "oldValue": null, "newValue": "**********"}, {"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "thresholdAmount", "oldValue": null, "newValue": 500.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "recipients", "oldValue": null, "newValue": ["<EMAIL>"]}, {"field": "deliveryMode", "oldValue": null, "newValue": "EMAIL"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "recipientType", "oldValue": null, "newValue": "END_USER"}]}, {"field": "comments", "oldValue": null, "newValue": "Create notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-12-06 10:44:40", "dateModified": "2024-12-06 10:45:03", "entityId": "CBS_**********", "entity": "{\"eventId\":\"1f14b3c4-56c3-4534-a4cc-794517b2598e\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":null,\"thresholdAmount\":500.0,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "27da72ef-57de-434d-9359-5e9bc5a2690d", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "frequencyId", "oldValue": "350f2d25-37fc-4dbf-937f-3a46965de6a8", "newValue": "8b052ffa-ebe8-4875-b049-4549de1dc86d"}, {"field": "thresholdAmount", "oldValue": 300.0, "newValue": 500.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "deliveryMode", "oldValue": "SMS", "newValue": null}, {"field": "name", "oldValue": null, "newValue": null}, {"field": "recipientType", "oldValue": "END_USER", "newValue": null}]}, {"field": "comments", "oldValue": null, "newValue": "Create notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-12-02 13:03:33", "dateModified": "2024-12-02 13:04:37", "entityId": "CBS_**********", "entity": "{\"eventId\":\"db5795a8-41d8-45dc-b216-1cfb3662c269\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"8b052ffa-ebe8-4875-b049-4549de1dc86d\",\"thresholdAmount\":500.0,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "71da62e5-9993-4ea3-9a9f-52e9d7ebd22a", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}]}, {"field": "comments", "oldValue": null, "newValue": "Edit subscription alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Edit subscription alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-29 10:54:09", "dateModified": "2024-11-29 10:54:40", "entityId": "CBS_**********", "entity": "{\"eventId\":\"0d48819f-4933-403c-a4c4-db0cf8d6a6d0\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"8b052ffa-ebe8-4875-b049-4549de1dc86d\",\"thresholdAmount\":null,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Edit subscription alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "f0254ea3-39f3-4953-99ef-9b2efee91189", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "eventId", "oldValue": null, "newValue": "0d48819f-4933-403c-a4c4-db0cf8d6a6d0"}, {"field": "accountSource", "oldValue": null, "newValue": "CBS"}, {"field": "accountId", "oldValue": null, "newValue": "**********"}, {"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "frequencyId", "oldValue": null, "newValue": "8b052ffa-ebe8-4875-b049-4549de1dc86d"}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "recipients", "oldValue": null, "newValue": ["<EMAIL>"]}, {"field": "deliveryMode", "oldValue": null, "newValue": "EMAIL"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "recipientType", "oldValue": null, "newValue": "END_USER"}]}, {"field": "comments", "oldValue": null, "newValue": "Create subscription alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create subscription alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-29 10:49:45", "dateModified": "2024-11-29 10:50:09", "entityId": "CBS_**********", "entity": "{\"eventId\":\"0d48819f-4933-403c-a4c4-db0cf8d6a6d0\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"8b052ffa-ebe8-4875-b049-4549de1dc86d\",\"thresholdAmount\":null,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create subscription alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "8be16e0f-44c8-40fb-9d10-ca8cedc0be6e", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "thresholdAmount", "oldValue": 510.0, "newValue": 550.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}]}, {"field": "comments", "oldValue": null, "newValue": "Edit notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Edit notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 16:41:37", "dateModified": "2024-11-29 10:49:38", "entityId": "CBS_**********", "entity": "{\"eventId\":\"4b9dae93-b926-46a8-8fe6-f3b9545253a6\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":null,\"thresholdAmount\":550.0,\"subscribers\":[{\"recipients\":[\"<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"},{\"recipients\":[\"************\"],\"deliveryMode\":\"SMS\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Edit notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "c181829e-13ff-4022-ab5c-f6e41b632804", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "UnSubscribe Account <PERSON><PERSON>s", "type": "UNSUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "status", "oldValue": "Active", "newValue": "Inactive"}], "status": "APPROVED", "makerComments": "Other reason", "checkerComments": "Approve delete customer nofitification", "dateCreated": "2024-11-28 16:13:20", "dateModified": "2024-11-28 16:24:44", "entityId": "CBS_**********", "entity": "{\"comments\":\"Other reason\",\"accountId\":\"**********\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\",\"eventId\":\"0d48819f-4933-403c-a4c4-db0cf8d6a6d0\",\"accountSource\":\"CBS\"}"}, {"id": "82b324c0-dafa-426b-a61d-d31c3fbcf680", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}]}, {"field": "comments", "oldValue": null, "newValue": "Edit subscription alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Edit subscription alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 16:10:39", "dateModified": "2024-11-28 16:11:22", "entityId": "CBS_**********", "entity": "{\"eventId\":\"0d48819f-4933-403c-a4c4-db0cf8d6a6d0\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"0c0a132d-62eb-4e41-8f10-b02907328774\",\"thresholdAmount\":null,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Edit subscription alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "cf4df424-c28f-4e0d-b85b-0e84701b43ed", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "thresholdAmount", "oldValue": 600.0, "newValue": 650.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}]}, {"field": "comments", "oldValue": null, "newValue": "Edit notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Edit notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 16:09:40", "dateModified": "2024-11-28 16:10:10", "entityId": "CBS_**********", "entity": "{\"eventId\":\"a82c0c2a-a0f2-4ff2-82ad-f3576d24e88b\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":null,\"thresholdAmount\":650.0,\"subscribers\":[{\"recipients\":[\"<PERSON><PERSON><PERSON><EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Edit notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "c360d1ed-872c-4613-8ef3-c9ddeb654ff6", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "eventId", "oldValue": null, "newValue": "0d48819f-4933-403c-a4c4-db0cf8d6a6d0"}, {"field": "accountSource", "oldValue": null, "newValue": "CBS"}, {"field": "accountId", "oldValue": null, "newValue": "**********"}, {"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "frequencyId", "oldValue": null, "newValue": "0c0a132d-62eb-4e41-8f10-b02907328774"}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "recipients", "oldValue": null, "newValue": ["<EMAIL>"]}, {"field": "deliveryMode", "oldValue": null, "newValue": "EMAIL"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "recipientType", "oldValue": null, "newValue": "END_USER"}]}, {"field": "comments", "oldValue": null, "newValue": "Create subscription alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create subscription alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 16:08:47", "dateModified": "2024-11-28 16:09:16", "entityId": "CBS_**********", "entity": "{\"eventId\":\"0d48819f-4933-403c-a4c4-db0cf8d6a6d0\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"0c0a132d-62eb-4e41-8f10-b02907328774\",\"thresholdAmount\":null,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create subscription alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "2cbf3e8d-47c0-41c8-a358-b4b162f215f0", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "frequencyId", "oldValue": "0c0a132d-62eb-4e41-8f10-b02907328774", "newValue": "350f2d25-37fc-4dbf-937f-3a46965de6a8"}, {"field": "thresholdAmount", "oldValue": 200.0, "newValue": 300.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}]}, {"field": "comments", "oldValue": null, "newValue": "Create notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 16:08:09", "dateModified": "2024-11-28 16:08:28", "entityId": "CBS_**********", "entity": "{\"eventId\":\"db5795a8-41d8-45dc-b216-1cfb3662c269\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"350f2d25-37fc-4dbf-937f-3a46965de6a8\",\"thresholdAmount\":300.0,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"},{\"recipients\":[\"************\"],\"deliveryMode\":\"SMS\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "6ad259fd-1a69-4f0c-b4b3-f5c44ee20de6", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "frequencyId", "oldValue": "8b052ffa-ebe8-4875-b049-4549de1dc86d", "newValue": "0c0a132d-62eb-4e41-8f10-b02907328774"}, {"field": "thresholdAmount", "oldValue": 100.0, "newValue": 200.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}]}, {"field": "comments", "oldValue": null, "newValue": "Create notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 16:07:09", "dateModified": "2024-11-28 16:07:35", "entityId": "CBS_**********", "entity": "{\"eventId\":\"db5795a8-41d8-45dc-b216-1cfb3662c269\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"0c0a132d-62eb-4e41-8f10-b02907328774\",\"thresholdAmount\":200.0,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"},{\"recipients\":[\"************\"],\"deliveryMode\":\"SMS\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "b2085e1c-79fb-4410-9fd7-66c9592f5822", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "eventId", "oldValue": null, "newValue": "db5795a8-41d8-45dc-b216-1cfb3662c269"}, {"field": "accountSource", "oldValue": null, "newValue": "CBS"}, {"field": "accountId", "oldValue": null, "newValue": "**********"}, {"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "frequencyId", "oldValue": null, "newValue": "8b052ffa-ebe8-4875-b049-4549de1dc86d"}, {"field": "thresholdAmount", "oldValue": null, "newValue": 100.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "recipients", "oldValue": null, "newValue": ["<EMAIL>"]}, {"field": "deliveryMode", "oldValue": null, "newValue": "EMAIL"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "recipientType", "oldValue": null, "newValue": "END_USER"}, {"field": "recipients", "oldValue": null, "newValue": ["************"]}, {"field": "deliveryMode", "oldValue": null, "newValue": "SMS"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "recipientType", "oldValue": null, "newValue": "END_USER"}]}, {"field": "comments", "oldValue": null, "newValue": "Create notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 16:05:22", "dateModified": "2024-11-28 16:05:51", "entityId": "CBS_**********", "entity": "{\"eventId\":\"db5795a8-41d8-45dc-b216-1cfb3662c269\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":\"8b052ffa-ebe8-4875-b049-4549de1dc86d\",\"thresholdAmount\":100.0,\"subscribers\":[{\"recipients\":[\"<EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"},{\"recipients\":[\"************\"],\"deliveryMode\":\"SMS\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "c081f29f-741b-4f70-b236-7e7ac488ba74", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "UnSubscribe Account <PERSON><PERSON>s", "type": "UNSUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "status", "oldValue": "Active", "newValue": "Inactive"}], "status": "REJECTED", "makerComments": "Irrelevant content", "checkerComments": "Reject", "dateCreated": "2024-11-28 15:38:58", "dateModified": "2024-11-28 15:55:49", "entityId": "CBS_**********", "entity": "{\"comments\":\"Irrelevant content\",\"accountId\":\"**********\",\"profileId\":null,\"eventId\":\"a82c0c2a-a0f2-4ff2-82ad-f3576d24e88b\",\"accountSource\":\"CBS\"}"}, {"id": "3745e374-ee01-4e63-9036-d4be7b3e4632", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "eventId", "oldValue": null, "newValue": "a82c0c2a-a0f2-4ff2-82ad-f3576d24e88b"}, {"field": "accountSource", "oldValue": null, "newValue": "CBS"}, {"field": "accountId", "oldValue": null, "newValue": "**********"}, {"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "thresholdAmount", "oldValue": null, "newValue": 600.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "recipients", "oldValue": null, "newValue": ["<EMAIL>"]}, {"field": "deliveryMode", "oldValue": null, "newValue": "EMAIL"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "recipientType", "oldValue": null, "newValue": "END_USER"}]}, {"field": "comments", "oldValue": null, "newValue": "Create notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Create notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 15:31:35", "dateModified": "2024-11-28 15:32:48", "entityId": "CBS_**********", "entity": "{\"eventId\":\"a82c0c2a-a0f2-4ff2-82ad-f3576d24e88b\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":null,\"thresholdAmount\":600.0,\"subscribers\":[{\"recipients\":[\"<PERSON><PERSON><PERSON><EMAIL>\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Create notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}, {"id": "848e5f04-d4f0-4d85-a149-94e0519e0e41", "checker": "<PERSON>", "maker": "<PERSON>", "makerCheckerType": {"name": "Subscribe Account <PERSON><PERSON>s", "type": "SUBSCRIBE_ACCOUNT_ALERTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "branchCode", "oldValue": null, "newValue": "011"}, {"field": "thresholdAmount", "oldValue": 500.0, "newValue": 510.0}, {"field": "subscribers", "oldValue": null, "newValue": [{"field": "name", "oldValue": null, "newValue": "Fire Chimera"}, {"field": "name", "oldValue": null, "newValue": "Fire Chimera"}]}, {"field": "comments", "oldValue": null, "newValue": "Edit notification alert"}, {"field": "profileId", "oldValue": null, "newValue": "83e07b30-f20f-4696-bb2d-844f6a785ba7"}], "status": "APPROVED", "makerComments": "Edit notification alert", "checkerComments": "Approve create customer nofitification", "dateCreated": "2024-11-28 15:28:48", "dateModified": "2024-11-28 15:30:59", "entityId": "CBS_**********", "entity": "{\"eventId\":\"4b9dae93-b926-46a8-8fe6-f3b9545253a6\",\"accountSource\":\"CBS\",\"accountId\":\"**********\",\"branchCode\":\"011\",\"frequencyId\":null,\"thresholdAmount\":510.0,\"subscribers\":[{\"recipients\":[\"<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com\"],\"deliveryMode\":\"EMAIL\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"},{\"recipients\":[\"************\"],\"deliveryMode\":\"SMS\",\"name\":\"Fire Chimera\",\"recipientType\":\"END_USER\"}],\"comments\":\"Edit notification alert\",\"profileId\":\"83e07b30-f20f-4696-bb2d-844f6a785ba7\"}"}]}