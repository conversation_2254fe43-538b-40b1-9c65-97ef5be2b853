{"pageNumber": 1, "pageSize": 10, "totalNumberOfPages": 4, "totalElements": 38, "data": [{"id": "022c4b7f-dd03-41df-ab9c-1866906bbaa6", "checker": "Patshe<PERSON> Gikunda", "maker": "<PERSON>", "makerCheckerType": {"name": "Create Users", "type": "CREATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_USERS", "REJECT_CREATE_USERS"], "makerPermissions": ["MAKE_CREATE_USERS"], "overridePermissions": ["SUPER_CREATE_USERS"]}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "<PERSON><PERSON><PERSON>"}, {"field": "lastName", "oldValue": null, "newValue": "Langat"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "status", "oldValue": null, "newValue": "ACTIVE"}, {"field": "phoneNumber", "oldValue": null, "newValue": "+254 729 294292"}, {"field": "tokens", "oldValue": [], "newValue": null}], "status": "PENDING", "makerComments": "string", "checkerComments": null, "dateCreated": "2024-05-23 09:06:18", "dateModified": "2024-05-23 09:06:19", "entityId": null, "entity": "{\"id\":null,\"dateCreated\":null,\"dateModified\":null,\"createdBy\":null,\"updatedBy\":null,\"firstName\":\"string\",\"lastName\":\"string\",\"email\":\"<EMAIL>\",\"externalId\":null,\"status\":\"ACTIVE\",\"phoneNumber\":\"string\",\"approvalRequest\":null,\"password\":null,\"enabled\":true,\"username\":\"<EMAIL>\",\"accountNonLocked\":true,\"accountNonExpired\":true,\"credentialsNonExpired\":true}"}, {"id": "f518ee16-9533-492e-bb0e-4a4b7ff1c37e", "checker": null, "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Activate Customers", "type": "ACTIVATE_CUSTOMERS", "description": null, "module": "Customers", "channel": "DBP", "checkerPermissions": ["REJECT_ACTIVATE_CUSTOMERS", "ACCEPT_ACTIVATE_CUSTOMERS"], "makerPermissions": ["MAKE_ACTIVATE_CUSTOMERS"], "overridePermissions": ["SUPER_ACTIVATE_CUSTOMERS"]}, "diff": [{"field": "isBlocked", "oldValue": true, "newValue": false}], "status": "PENDING", "makerComments": "Reinstatement", "checkerComments": null, "dateCreated": "2024-10-17 09:06:03", "dateModified": "2024-10-17 09:06:03", "entityId": "c0fe6c37-e8fe-4c22-be39-fd37a21d7858", "entity": "{\"comments\":\"Reinstatement\"}"}, {"id": "5d479e46-001e-414a-9a2a-d29259e31085", "checker": null, "maker": "<PERSON>", "makerCheckerType": {"name": "Activate Customers", "type": "ACTIVATE_CUSTOMERS", "description": null, "module": "Customers", "channel": "DBP", "checkerPermissions": ["REJECT_ACTIVATE_CUSTOMERS", "ACCEPT_ACTIVATE_CUSTOMERS"], "makerPermissions": ["MAKE_ACTIVATE_CUSTOMERS"], "overridePermissions": ["SUPER_ACTIVATE_CUSTOMERS"]}, "diff": [{"field": "isBlocked", "oldValue": true, "newValue": false}], "status": "PENDING", "makerComments": "Resolved Issues", "checkerComments": null, "dateCreated": "2024-10-16 12:50:24", "dateModified": "2024-10-16 12:50:24", "entityId": "365093bb-24fb-4aae-a6f8-05e34946197e", "entity": "{\"comments\":\"Resolved Issues\"}"}, {"id": "8d135a64-7647-440d-a3f6-0c04d012aece", "checker": null, "maker": "<PERSON>", "makerCheckerType": {"name": "Create Customers", "type": "CREATE_CUSTOMERS", "description": null, "module": "Customers", "channel": "DBP", "checkerPermissions": [], "makerPermissions": [], "overridePermissions": []}, "diff": [{"field": "firstName", "oldValue": null, "newValue": "SAUL"}, {"field": "lastName", "oldValue": null, "newValue": "WAFULA"}, {"field": "email", "oldValue": null, "newValue": "<EMAIL>"}, {"field": "phoneNumber", "oldValue": null, "newValue": "*********pho"}, {"field": "postalAddress", "oldValue": null, "newValue": "Sitawi House Lamu Rd Box 47-80200"}, {"field": "gender", "oldValue": null, "newValue": "M"}, {"field": "idType", "oldValue": null, "newValue": "NationalId"}, {"field": "idValue", "oldValue": null, "newValue": "********"}, {"field": "cif", "oldValue": null, "newValue": "*********"}, {"field": "<PERSON><PERSON><PERSON>ress", "oldValue": null, "newValue": "Baraka House East Rd Box 16478-20100"}, {"field": "country", "oldValue": null, "newValue": "KE"}, {"field": "customerPrefix", "oldValue": null, "newValue": "MR"}, {"field": "dateOfBirth", "oldValue": null, "newValue": "1981-02-14"}, {"field": "nationality", "oldValue": null, "newValue": "KE"}, {"field": "customerCategory", "oldValue": null, "newValue": "001"}, {"field": "customerType", "oldValue": null, "newValue": "I"}, {"field": "entityId", "oldValue": null, "newValue": "8d135a64-7647-440d-a3f6"}, {"field": "customerAccounts", "oldValue": null, "newValue": [{"accNumber": "**********", "accOpenDate": "2008-03-25", "customerType": "I", "customerCategory": "001", "accBranchCode": "001", "accClass": "SVLSST", "accClassDesc": "DTB Staff Savings Account", "accCurrency": "KES", "accDormant": "N", "accStatus": "NORM", "accRecordStatus": "O", "accStatBlock": "N", "accFrozen": "N", "accNoDebit": "N", "accNoCredit": "N", "accStopPay": "N", "jointAccIndicator": "S", "customerRecordStatus": "O", "accountClass": {"id": "SVLSST", "description": "DTB Staff Savings Account", "accountClassType": "S", "mobileBankingLinkingAllowed": true, "deleted": false}, "isMobileLinkable": true}, {"accNumber": "**********", "accOpenDate": "2008-04-17", "customerType": "I", "customerCategory": "001", "accBranchCode": "001", "accClass": "LDRPAC", "accClassDesc": "LOAN REPAYMENT ACCOUNT", "accCurrency": "KES", "accDormant": "N", "accStatus": "NORM", "accRecordStatus": "O", "accStatBlock": "N", "accFrozen": "N", "accNoDebit": "N", "accNoCredit": "N", "accStopPay": "N", "jointAccIndicator": "S", "customerRecordStatus": "O", "accountClass": {"id": "LDRPAC", "description": "LOAN REPAYMENT ACCOUNT", "accountClassType": "U", "mobileBankingLinkingAllowed": false, "deleted": false}, "isMobileLinkable": false}, {"accNumber": "**********", "accOpenDate": "2013-01-25", "customerType": "I", "customerCategory": "001", "accBranchCode": "001", "accClass": "SVLSLC", "accClassDesc": "DTB Savings Account", "accCurrency": "KES", "accDormant": "N", "accStatus": "NORM", "accRecordStatus": "O", "accStatBlock": "N", "accFrozen": "N", "accNoDebit": "N", "accNoCredit": "N", "accStopPay": "N", "jointAccIndicator": "S", "customerRecordStatus": "O", "accountClass": {"id": "SVLSLC", "description": "DTB Savings Account", "accountClassType": "S", "mobileBankingLinkingAllowed": true, "deleted": false}, "isMobileLinkable": true}, {"accNumber": "**********", "accOpenDate": "2021-03-25", "customerType": "I", "customerCategory": "001", "accBranchCode": "001", "accClass": "CURMOB", "accClassDesc": "Digital Current Account", "accCurrency": "KES", "accDormant": "N", "accStatus": "NORM", "accRecordStatus": "C", "accStatBlock": "N", "accFrozen": "N", "accNoDebit": "N", "accNoCredit": "N", "accStopPay": "N", "jointAccIndicator": "S", "customerRecordStatus": "O", "accountClass": {"id": "CURMOB", "description": "Digital Current Account", "accountClassType": "U", "mobileBankingLinkingAllowed": true, "deleted": false}, "isMobileLinkable": true}, {"accNumber": "**********", "accOpenDate": "2005-10-26", "customerType": "I", "customerCategory": "001", "accBranchCode": "008", "accClass": "ATMAIR", "accClassDesc": "DTB Super Value Account", "accCurrency": "KES", "accDormant": "N", "accStatus": "NORM", "accRecordStatus": "O", "accStatBlock": "N", "accFrozen": "N", "accNoDebit": "N", "accNoCredit": "N", "accStopPay": "N", "jointAccIndicator": "S", "customerRecordStatus": "O", "accountClass": {"id": "ATMAIR", "description": "DTB Super Value Account", "accountClassType": "U", "mobileBankingLinkingAllowed": true, "deleted": false}, "isMobileLinkable": true}]}], "status": "PENDING", "makerComments": null, "checkerComments": null, "dateCreated": "2024-09-24 16:28:00", "dateModified": "2024-09-24 16:28:00", "entityId": "8d135a64-7647-440d-a3f6", "entity": "{\"profileId\":\"8d135a64-7647-440d-a3f6\",\"firstName\":\"SAUL\",\"lastName\":\"WAFULA\",\"email\":\"<EMAIL>\",\"phoneNumber\":\"*********pho\",\"postalAddress\":\"Sitawi House Lamu Rd Box 47-80200\",\"gender\":\"M\",\"idType\":\"NationalId\",\"idValue\":\"********\",\"cif\":\"*********\",\"physicalAddress\":\"Baraka House East Rd Box 16478-20100\",\"country\":\"KE\",\"customerPrefix\":\"MR\",\"dateOfBirth\":\"1981-02-14\",\"nationality\":\"KE\",\"customerCategory\":\"001\",\"customerType\":\"I\",\"customerAccounts\":[{\"accNumber\":\"**********\",\"accOpenDate\":\"2008-03-25\",\"customerType\":\"I\",\"customerCategory\":\"001\",\"accBranchCode\":\"001\",\"accClass\":\"SVLSST\",\"accClassDesc\":\"DTB Staff Savings Account\",\"accCurrency\":\"KES\",\"accDormant\":\"N\",\"accStatus\":\"NORM\",\"accRecordStatus\":\"O\",\"accStatBlock\":\"N\",\"accFrozen\":\"N\",\"accNoDebit\":\"N\",\"accNoCredit\":\"N\",\"accStopPay\":\"N\",\"jointAccIndicator\":\"S\",\"customerRecordStatus\":\"O\",\"accountClass\":{\"id\":\"SVLSST\",\"description\":\"DTB Staff Savings Account\",\"accountClassType\":\"S\",\"mobileBankingLinkingAllowed\":true,\"deleted\":false},\"isMobileLinkable\":true},{\"accNumber\":\"**********\",\"accOpenDate\":\"2008-04-17\",\"customerType\":\"I\",\"customerCategory\":\"001\",\"accBranchCode\":\"001\",\"accClass\":\"LDRPAC\",\"accClassDesc\":\"LOAN REPAYMENT ACCOUNT\",\"accCurrency\":\"KES\",\"accDormant\":\"N\",\"accStatus\":\"NORM\",\"accRecordStatus\":\"O\",\"accStatBlock\":\"N\",\"accFrozen\":\"N\",\"accNoDebit\":\"N\",\"accNoCredit\":\"N\",\"accStopPay\":\"N\",\"jointAccIndicator\":\"S\",\"customerRecordStatus\":\"O\",\"accountClass\":{\"id\":\"LDRPAC\",\"description\":\"LOAN REPAYMENT ACCOUNT\",\"accountClassType\":\"U\",\"mobileBankingLinkingAllowed\":false,\"deleted\":false},\"isMobileLinkable\":false},{\"accNumber\":\"**********\",\"accOpenDate\":\"2013-01-25\",\"customerType\":\"I\",\"customerCategory\":\"001\",\"accBranchCode\":\"001\",\"accClass\":\"SVLSLC\",\"accClassDesc\":\"DTB Savings Account\",\"accCurrency\":\"KES\",\"accDormant\":\"N\",\"accStatus\":\"NORM\",\"accRecordStatus\":\"O\",\"accStatBlock\":\"N\",\"accFrozen\":\"N\",\"accNoDebit\":\"N\",\"accNoCredit\":\"N\",\"accStopPay\":\"N\",\"jointAccIndicator\":\"S\",\"customerRecordStatus\":\"O\",\"accountClass\":{\"id\":\"SVLSLC\",\"description\":\"DTB Savings Account\",\"accountClassType\":\"S\",\"mobileBankingLinkingAllowed\":true,\"deleted\":false},\"isMobileLinkable\":true},{\"accNumber\":\"**********\",\"accOpenDate\":\"2021-03-25\",\"customerType\":\"I\",\"customerCategory\":\"001\",\"accBranchCode\":\"001\",\"accClass\":\"CURMOB\",\"accClassDesc\":\"Digital Current Account\",\"accCurrency\":\"KES\",\"accDormant\":\"N\",\"accStatus\":\"NORM\",\"accRecordStatus\":\"C\",\"accStatBlock\":\"N\",\"accFrozen\":\"N\",\"accNoDebit\":\"N\",\"accNoCredit\":\"N\",\"accStopPay\":\"N\",\"jointAccIndicator\":\"S\",\"customerRecordStatus\":\"O\",\"accountClass\":{\"id\":\"CURMOB\",\"description\":\"Digital Current Account\",\"accountClassType\":\"U\",\"mobileBankingLinkingAllowed\":true,\"deleted\":false},\"isMobileLinkable\":true},{\"accNumber\":\"**********\",\"accOpenDate\":\"2005-10-26\",\"customerType\":\"I\",\"customerCategory\":\"001\",\"accBranchCode\":\"008\",\"accClass\":\"ATMAIR\",\"accClassDesc\":\"DTB Super Value Account\",\"accCurrency\":\"KES\",\"accDormant\":\"N\",\"accStatus\":\"NORM\",\"accRecordStatus\":\"O\",\"accStatBlock\":\"N\",\"accFrozen\":\"N\",\"accNoDebit\":\"N\",\"accNoCredit\":\"N\",\"accStopPay\":\"N\",\"jointAccIndicator\":\"S\",\"customerRecordStatus\":\"O\",\"accountClass\":{\"id\":\"ATMAIR\",\"description\":\"DTB Super Value Account\",\"accountClassType\":\"U\",\"mobileBankingLinkingAllowed\":true,\"deleted\":false},\"isMobileLinkable\":true}],\"delinkedAccounts\":null,\"inActiveAccounts\":null,\"comments\":null}"}, {"id": "c68d2d35-6820-45db-9ae2-9881f4204eb7", "checker": null, "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Delete Accounts", "type": "DELETE_ACCOUNTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": ["ACCEPT_DELETE_ACCOUNTS", "REJECT_DELETE_ACCOUNTS"], "makerPermissions": ["MAKE_DELETE_ACCOUNTS"], "overridePermissions": ["SUPER_DELETE_ACCOUNTS"]}, "diff": [{"field": "status", "oldValue": "ACTIVE", "newValue": "REMOVED"}], "status": "PENDING", "makerComments": "Unlinking Account", "checkerComments": null, "dateCreated": "2024-09-21 10:53:07", "dateModified": "2024-09-21 10:53:07", "entityId": "**********", "entity": "{\"comments\":\"Unlinking Account\",\"profileId\":\"a1b4d5ae-1404-49ac-bd7f-81a925be6fdf\",\"accountNo\":\"**********\",\"status\":\"REMOVED\"}"}, {"id": "b15c9432-d437-4ffa-bfd1-53cad9b3636b", "checker": null, "maker": "Patshe<PERSON> Gikunda", "makerCheckerType": {"name": "Deactivate Customers", "type": "DEACTIVATE_CUSTOMERS", "description": null, "module": "Customers", "channel": "DBP", "checkerPermissions": ["REJECT_DEACTIVATE_CUSTOMERS", "ACCEPT_DEACTIVATE_CUSTOMERS"], "makerPermissions": ["MAKE_DEACTIVATE_CUSTOMERS"], "overridePermissions": ["SUPER_DEACTIVATE_CUSTOMERS"]}, "diff": [{"field": "isBlocked", "oldValue": false, "newValue": true}], "status": "PENDING", "makerComments": "User Request", "checkerComments": null, "dateCreated": "2024-10-09 10:30:22", "dateModified": "2024-10-09 10:30:22", "entityId": "0a8b882c-06f6-49d7-b913-a892b267851b", "entity": "{\"blockReason\":\"DataBreach\",\"comments\":\"User Request\"}"}, {"id": "c433d986-ecd7-4b6d-83a7-542b2f73de55", "checker": null, "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Create Accounts", "type": "CREATE_ACCOUNTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_ACCOUNTS", "REJECT_CREATE_ACCOUNTS"], "makerPermissions": ["MAKE_CREATE_ACCOUNTS"], "overridePermissions": ["SUPER_CREATE_ACCOUNTS"]}, "diff": [{"field": "accounts", "oldValue": "", "newValue": "**********"}], "status": "PENDING", "makerComments": "", "checkerComments": null, "dateCreated": "2024-09-30 14:54:44", "dateModified": "2024-09-30 14:54:44", "entityId": "0cce9b88-fd0d-48c0-8643-a072c91b48fc", "entity": "{\"accounts\":[{\"accNumber\":\"**********\",\"accOpenDate\":\"2022-10-12\",\"customerType\":\"I\",\"customerCategory\":\"001\",\"accBranchCode\":\"052\",\"accClass\":\"SVLSST\",\"accClassDesc\":\"DTB Staff Savings Account\",\"accCurrency\":\"KES\",\"accDormant\":\"N\",\"accStatus\":\"NORM\",\"accRecordStatus\":\"O\",\"accStatBlock\":\"N\",\"accFrozen\":\"N\",\"accNoDebit\":\"N\",\"accNoCredit\":\"N\",\"accStopPay\":\"N\",\"jointAccIndicator\":\"S\",\"customerRecordStatus\":\"O\",\"accountClass\":{\"id\":\"SVLSST\",\"description\":\"DTB Staff Savings Account\",\"accountClassType\":\"S\",\"mobileBankingLinkingAllowed\":true,\"deleted\":false},\"isMobileLinkable\":true}],\"comments\":\"make linking accounts\"}"}, {"id": "697cd367-b260-4c75-801f-e22ee5707f9a", "checker": null, "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Activate Customers", "type": "ACTIVATE_CUSTOMERS", "description": null, "module": "Customers", "channel": "DBP", "checkerPermissions": ["REJECT_ACTIVATE_CUSTOMERS", "ACCEPT_ACTIVATE_CUSTOMERS"], "makerPermissions": ["MAKE_ACTIVATE_CUSTOMERS"], "overridePermissions": ["SUPER_ACTIVATE_CUSTOMERS"]}, "diff": [{"field": "isBlocked", "oldValue": true, "newValue": false}], "status": "PENDING", "makerComments": "Approve customer profile activation", "checkerComments": null, "dateCreated": "2024-09-25 11:32:03", "dateModified": "2024-09-25 11:32:03", "entityId": "48cef321-ddfd-47d5-9467-db513c2e74a1", "entity": "{\"comments\":\"Approve customer profile activation\"}"}, {"id": "af0b7597-0dff-42f0-b77d-3a9e33b3a219", "checker": null, "maker": "<PERSON>", "makerCheckerType": {"name": "Create Accounts", "type": "CREATE_ACCOUNTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": ["ACCEPT_CREATE_ACCOUNTS", "REJECT_CREATE_ACCOUNTS"], "makerPermissions": ["MAKE_CREATE_ACCOUNTS"], "overridePermissions": ["SUPER_CREATE_ACCOUNTS"]}, "diff": [{"field": "accounts", "oldValue": "", "newValue": "**********"}], "status": "PENDING", "makerComments": "", "checkerComments": null, "dateCreated": "2024-09-23 10:24:37", "dateModified": "2024-09-23 10:24:37", "entityId": "83e07b30-f20f-4696-bb2d-844f6a785ba7", "entity": "{\"accounts\":[{\"accNumber\":\"**********\",\"accOpenDate\":\"2010-07-19\",\"customerType\":\"I\",\"customerCategory\":\"001\",\"accBranchCode\":\"011\",\"accClass\":\"CURLCY\",\"accClassDesc\":\"DTB Current Account\",\"accCurrency\":\"KES\",\"accDormant\":\"N\",\"accStatus\":\"NORM\",\"accRecordStatus\":\"O\",\"accStatBlock\":\"N\",\"accFrozen\":\"N\",\"accNoDebit\":\"N\",\"accNoCredit\":\"N\",\"accStopPay\":\"N\",\"jointAccIndicator\":\"S\",\"customerRecordStatus\":\"O\",\"accountClass\":{\"id\":\"CURLCY\",\"description\":\"DTB Current Account\",\"accountClassType\":\"U\",\"mobileBankingLinkingAllowed\":true,\"deleted\":false},\"isMobileLinkable\":true}],\"comments\":\"make linking accounts\"}"}, {"id": "c68d2d35-6820-45db-9ae2-9881f4204eb7", "checker": null, "maker": "<PERSON><PERSON><PERSON>", "makerCheckerType": {"name": "Delete Accounts", "type": "DELETE_ACCOUNTS", "description": null, "module": "accounts", "channel": "DBP", "checkerPermissions": ["ACCEPT_DELETE_ACCOUNTS", "REJECT_DELETE_ACCOUNTS"], "makerPermissions": ["MAKE_DELETE_ACCOUNTS"], "overridePermissions": ["SUPER_DELETE_ACCOUNTS"]}, "diff": [{"field": "status", "oldValue": "ACTIVE", "newValue": "REMOVED"}], "status": "PENDING", "makerComments": "Unlinking Account", "checkerComments": null, "dateCreated": "2024-09-21 10:53:07", "dateModified": "2024-09-21 10:53:07", "entityId": "**********", "entity": "{\"comments\":\"Unlinking Account\",\"profileId\":\"a1b4d5ae-1404-49ac-bd7f-81a925be6fdf\",\"accountNo\":\"**********\",\"status\":\"REMOVED\"}"}, {"id": "074c9186-7dfd-480e-82dc-98d769b93ca1", "checker": null, "maker": "<PERSON>", "makerCheckerType": {"name": "Update Users", "type": "UPDATE_USERS", "description": null, "module": "users", "channel": "DBP", "checkerPermissions": ["REJECT_UPDATE_USERS", "ACCEPT_UPDATE_USERS"], "makerPermissions": ["MAKE_UPDATE_USERS"], "overridePermissions": ["SUPER_UPDATE_USERS"]}, "diff": [{"field": "roles", "oldValue": null, "newValue": [{"field": "name", "oldValue": "testing 4", "newValue": "Compliance View"}, {"field": "description", "oldValue": "testing", "newValue": "Audit"}, {"field": "custom", "oldValue": true, "newValue": false}]}], "status": "PENDING", "makerComments": "Updating a user", "checkerComments": null, "dateCreated": "2024-11-07 11:34:09", "dateModified": "2024-11-07 11:34:09", "entityId": "142d9a31-b4bb-430b-bd86-f3aae0834a24", "entity": "{\"roleId\":\"1adac73e-bd4b-428a-acdc-2268a570b6ea\",\"comments\":\"Updating a user\"}"}]}