[{"id": "76b6f262-e5a2-4fc4-98de-ea9861bb7b00", "eventType": "KYC_VALIDATION_EXCEPTION", "eventName": "Failed Customer KYC Validation", "settings": [{"id": "7406447c-21ec-4717-92f4-6f364d1a636c", "notificationType": "BACKOFFICE_USER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${failedValidations}", "${failedMessages}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}"], "templates": [{"id": "5c27a613-fe2e-4fa1-a0b3-b41742646541", "templateName": "kyc_loan_exception_email", "templateSubject": "${productName} Exception – ${customerName} – ${loanId}", "templateContent": "<html><body> <p>Dear User,</p> <p> Please note that an exception has occurred that needs your attention. Please find below the details of the exceptions noted:</p><p> <strong>Product Name:</strong> ${productName}</p><p> <strong>Loan Request ID:</strong> ${loanId}</p><p> <strong>Customer Name:</strong> ${customerName}</p><p><strong>Loan Amount:</strong> ${loanAmount}</p><p><strong>Loan Tenor:</strong> ${loanTenure}</p><p><strong>Error(s):</strong> ${failedValidations}</p><p> </p><p> Kindly click this link to log in and review this case: <a href=\"${backofficeLink}\">here</a></p>", "templateDescription": "Loan request exceptions", "htmlContent": false}]}, {"id": "c5edb9ed-b217-4a84-9362-1003185aa58d", "eventType": "CUSTOMER_PHONE_VERIFICATION", "eventName": "Customer phone verification", "settings": [{"id": "bed00958-747a-4107-81f0-a0fc88d54e1e", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${otp}", "${hash_key}"], "templates": [{"id": "f749824e-152a-4625-a162-fb7ead402b43", "templateName": "customer-phone-verification-otp", "templateSubject": "One time PIN", "templateContent": "Dear customer, this is your One Time PIN ${otp}, please verify it to continue.\\n${hash_key}", "templateDescription": "Customer One time PIN", "htmlContent": false}]}, {"id": "c26efacc-6249-4877-bcef-520c1a729ae6", "eventType": "CREDIT_VALIDATION_EXCEPTION", "eventName": "Failed Customer Credit Validation", "settings": [{"id": "7406447c-21ec-4717-92f4-6f364d1a636c", "notificationType": "BACKOFFICE_USER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${failedValidations}", "${failedMessages}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}"], "templates": [{"id": "5c27a613-fe2e-4fa1-a0b3-b41742646541", "templateName": "kyc_loan_exception_email", "templateSubject": "${productName} Exception – ${customerName} – ${loanId}", "templateContent": "<html><body> <p>Dear User,</p> <p> Please note that an exception has occurred that needs your attention. Please find below the details of the exceptions noted:</p><p> <strong>Product Name:</strong> ${productName}</p><p> <strong>Loan Request ID:</strong> ${loanId}</p><p> <strong>Customer Name:</strong> ${customerName}</p><p><strong>Loan Amount:</strong> ${loanAmount}</p><p><strong>Loan Tenor:</strong> ${loanTenure}</p><p><strong>Error(s):</strong> ${failedValidations}</p><p> </p><p> Kindly click this link to log in and review this case: <a href=\"${backofficeLink}\">here</a></p>", "templateDescription": "Loan request exceptions", "htmlContent": false}]}, {"id": "9c3aff78-68f8-452f-9faf-17aa5a2b0b71", "eventType": "LOAN_OVERDUE_DAY_1", "eventName": "Day 1 Overdue", "settings": [{"id": "********-8679-4d6f-8533-046a0aeaf3e1", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "90afcca1-a9a9-4ab0-b4fe-17bbb53b187d", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}", "${settlementAccount}", "${externalLoanId}", "${issueDate}", "${maturityDate}", "${principalAmount}", "${prepaymentAmount}", "${paidPrepaymentAmount}", "${interestAmount}", "${outstandingPrincipalAmount}", "${outstandingInterestAmount}", "${principalOverDue}", "${interestOverDue}", "${penaltyInterestOverDue}", "${penaltyPrincipalOverDue}", "${loanStatus}", "${cbsDisbursementRef}", "${upfrontInterestAmount}", "${interestRate}", "${daysOverDue}", "${overDueDays}", "${loanID}", "${totalOverdueAmount}"], "templates": [{"id": "a3610635-52d6-40b9-baa7-0ff11ad95b61", "templateName": "overdue_loan_customer_sms", "templateSubject": "Overdue Loan Customer SMS", "templateContent": "Dear ${customerFirstName} ${customerLastName},\\n Kindly note that your ${productName} loan, ref ${externalLoanId}, is overdue by ${overDueDays} day(s). Please make your outstanding payment of ${totalOverdueAmount} by crediting your DTB Loan Account ${settlementAccount}. Failure to do so will result in additional penalty fees accruing on the facility.\\n DTB.", "templateDescription": "Overdue Loan Customer SMS", "htmlContent": false}, {"id": "4e5dd5d2-57cc-4a04-85c6-2131d652600e", "templateName": "overdue_loan_customer_email", "templateSubject": "Overdue Loan Alert - ${customerFirstName} ${customerLastName} – Loan Ref ${externalLoanId}", "templateContent": "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Loan Overdue Notification</title></head><body><p>Dear ${customerFirstName} ${customerLastName},</p><p>Kindly note that the below ${productName} Loan is overdue by ${overDueDays} Days:</p><ul><li><strong>Loan Request Date:</strong> ${issueDate}</li><li><strong>Insurer Name:</strong> ${productOrganizationName}</li><li><strong>Customer Name:</strong> ${customerName}</li><li><strong>Loan Amount:</strong> ${principalAmount}</li><li><strong>Loan Tenor:</strong> ${loanTenure}</li><li><strong>Loan Overdue Amount:</strong> ${totalOverdueAmount}</li></ul><p>Please make your outstanding payment of ${totalOverdueAmount} by crediting your DTB Loan Account ${settlementAccount}. Failure to do so will result in additional penalty fees accruing on the facility.</p><p>DTB</p></body></html>", "templateDescription": "Overdue Loan Customer Email", "htmlContent": false}]}, {"id": "7ee73555-96b0-4d0f-8467-db2cd202524f", "eventType": "LOAN_DISBURSEMENT", "eventName": "Loan Disbursement", "settings": [{"id": "7f6192bf-15eb-4040-8cc0-96aa4cd53882", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "3ca974cf-c786-4a95-bacb-3b8c079f7f3e", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${firstName}", "${lastName}", "${idNumber}", "${loanID}", "${prePaymentAmount}", "${loanInstallmentSchedule}", "${nextRepaymentDate}", "${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}", "${settlementAccount}", "${externalLoanId}", "${issueDate}", "${maturityDate}", "${principalAmount}", "${prepaymentAmount}", "${paidPrepaymentAmount}", "${interestAmount}", "${loanStatus}", "${cbsDisbursementRef}", "${upfrontInterestAmount}", "${interestRate}", "${overDueDays}", "${totalOverdueAmount}", "${nextInstallmentAmount}", "${nextLoanInstallmentDate}"], "templates": [{"id": "de908a0e-b4ae-469d-a102-d076a5f0b8b3", "templateName": "loan_disbursement_customer_sms", "templateSubject": "Loan Disbursement Customer SMS", "templateContent": "Congratulations, ${customerFirstName}! Your ${productName} loan has been disbursed and is now active. Loan Ref: ${externalLoanId} Your Loan Account Number is ${settlementAccount}. Please credit your loan account with your next installment amount ${nextInstallmentAmount} by ${nextLoanInstallmentDate}. DTB.", "templateDescription": "Loan Disbursement Customer SMS", "htmlContent": false}, {"id": "c6713c38-9d84-454e-8c51-667c25f74b8f", "templateName": "loan_disbursement_customer_email", "templateSubject": "${customerFirstName} ${customerLastName} – ${productName} Loan Disbursement", "templateContent": "<!DOCTYPE html><html><head><title>Loan Disbursement Notification</title></head><body><p>Dear ${customerFirstName} ${customerLastName},</p><p>Congratulations! Your ${productName} Loan has been disbursed and is now active. Please find below details:</p><ul><li>Customer Name: ${customerName}</li><li>Loan Request Date: ${issueDate}</li><li>Disbursement Date: ${issueDate}</li><li>Maturity Date: ${maturityDate}</li><li>Loan Amount: ${principalAmount}</li><li>Deposit Amount: ${prepaymentAmount}</li><li>Loan Tenor: ${loanTenure}</li><li>Loan Account Number: ${settlementAccount}</li><li>DTB Loan Reference: ${externalLoanId}</li></ul><p>Below is your Loan Repayment Schedule:</p>${loanInstallmentSchedule}<p>Make your repayments to the Loan Account Number through M-PESA or Pesalink by following the below instructions:</p><h3>M-PESA</h3><ol><li>Go to 'Lipa na M-PESA'</li><li>Select Paybill Option</li><li>Input 516600 as the Business Number.</li><li>Input your Loan Account Number as the Account Number.</li><li>Input the Amount to be repaid.</li><li>Complete the transaction and provide M-PESA PIN.</li></ol><h3>PESALINK</h3><ol><li>Go to your Bank App</li><li>Select Pesalink</li><li>Select Pay by Account</li><li>Select DTB as the Recipient Bank</li><li>Input your Loan Account Number as the Account Number.</li><li>Input the Amount to be repaid.</li><li>Complete the transaction and provide your Mobile Banking or Internet Banking PIN.</li></ol><p>Sincerely,</p><p>DTB</p></body></html>", "templateDescription": "Loan Disbursement Customer Email", "htmlContent": false}]}, {"id": "44677b3b-f7a9-4c30-86a0-590233b38151", "eventType": "LOAN_OVERDUE_DAY_7", "eventName": "Day 7 Overdue", "settings": [{"id": "********-8679-4d6f-8533-046a0aeaf3e1", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "90afcca1-a9a9-4ab0-b4fe-17bbb53b187d", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}", "${settlementAccount}", "${externalLoanId}", "${issueDate}", "${maturityDate}", "${principalAmount}", "${prepaymentAmount}", "${paidPrepaymentAmount}", "${interestAmount}", "${outstandingPrincipalAmount}", "${outstandingInterestAmount}", "${principalOverDue}", "${interestOverDue}", "${penaltyInterestOverDue}", "${penaltyPrincipalOverDue}", "${loanStatus}", "${cbsDisbursementRef}", "${upfrontInterestAmount}", "${interestRate}", "${daysOverDue}", "${overDueDays}", "${loanID}", "${totalOverdueAmount}"], "templates": [{"id": "a3610635-52d6-40b9-baa7-0ff11ad95b61", "templateName": "overdue_loan_customer_sms", "templateSubject": "Overdue Loan Customer SMS", "templateContent": "Dear ${customerFirstName} ${customerLastName},\\n Kindly note that your ${productName} loan, ref ${externalLoanId}, is overdue by ${overDueDays} day(s). Please make your outstanding payment of ${totalOverdueAmount} by crediting your DTB Loan Account ${settlementAccount}. Failure to do so will result in additional penalty fees accruing on the facility.\\n DTB.", "templateDescription": "Overdue Loan Customer SMS", "htmlContent": false}, {"id": "4e5dd5d2-57cc-4a04-85c6-2131d652600e", "templateName": "overdue_loan_customer_email", "templateSubject": "Overdue Loan Alert - ${customerFirstName} ${customerLastName} – Loan Ref ${externalLoanId}", "templateContent": "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Loan Overdue Notification</title></head><body><p>Dear ${customerFirstName} ${customerLastName},</p><p>Kindly note that the below ${productName} Loan is overdue by ${overDueDays} Days:</p><ul><li><strong>Loan Request Date:</strong> ${issueDate}</li><li><strong>Insurer Name:</strong> ${productOrganizationName}</li><li><strong>Customer Name:</strong> ${customerName}</li><li><strong>Loan Amount:</strong> ${principalAmount}</li><li><strong>Loan Tenor:</strong> ${loanTenure}</li><li><strong>Loan Overdue Amount:</strong> ${totalOverdueAmount}</li></ul><p>Please make your outstanding payment of ${totalOverdueAmount} by crediting your DTB Loan Account ${settlementAccount}. Failure to do so will result in additional penalty fees accruing on the facility.</p><p>DTB</p></body></html>", "templateDescription": "Overdue Loan Customer Email", "htmlContent": false}]}, {"id": "b24c7e10-3544-4b65-afcc-58358220051e", "eventType": "LOAN_OVERDUE_DAY_14", "eventName": "Day 14 Overdue", "settings": [{"id": "375aa341-735e-45a0-a300-e22ede08e64b", "notificationType": "ORGANIZATION", "deliveryMode": "EMAIL", "templateName": null}, {"id": "********-8679-4d6f-8533-046a0aeaf3e1", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "90afcca1-a9a9-4ab0-b4fe-17bbb53b187d", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}", "${settlementAccount}", "${externalLoanId}", "${issueDate}", "${maturityDate}", "${principalAmount}", "${prepaymentAmount}", "${paidPrepaymentAmount}", "${interestAmount}", "${outstandingPrincipalAmount}", "${outstandingInterestAmount}", "${principalOverDue}", "${interestOverDue}", "${penaltyInterestOverDue}", "${penaltyPrincipalOverDue}", "${loanStatus}", "${cbsDisbursementRef}", "${upfrontInterestAmount}", "${interestRate}", "${daysOverDue}", "${overDueDays}", "${loanID}", "${totalOverdueAmount}"], "templates": [{"id": "97a2c625-2105-4091-b186-6741d1ced160", "templateName": "organization_notice_overdue_loan_listing", "templateSubject": "Overdue Customer Loan(s) Notice - ${productOrganizationName}", "templateContent": "<!DOCTYPE html><body><p>Dear ${productOrganizationName},</p><p>Kindly note that the attached list of ${productName} Loans are overdue by ${overDueDays} Days.</p><p>Please make arrangements to engage the customer(s) above to make their repayments. Failure to do so will result in request for cancellation.</p><p>DTB</p></body></html>", "templateDescription": "Organization Notice - Overdue Loan Listing", "htmlContent": false}, {"id": "a3610635-52d6-40b9-baa7-0ff11ad95b61", "templateName": "overdue_loan_customer_sms", "templateSubject": "Overdue Loan Customer SMS", "templateContent": "Dear ${customerFirstName} ${customerLastName},\\n Kindly note that your ${productName} loan, ref ${externalLoanId}, is overdue by ${overDueDays} day(s). Please make your outstanding payment of ${totalOverdueAmount} by crediting your DTB Loan Account ${settlementAccount}. Failure to do so will result in additional penalty fees accruing on the facility.\\n DTB.", "templateDescription": "Overdue Loan Customer SMS", "htmlContent": false}, {"id": "4e5dd5d2-57cc-4a04-85c6-2131d652600e", "templateName": "overdue_loan_customer_email", "templateSubject": "Overdue Loan Alert - ${customerFirstName} ${customerLastName} – Loan Ref ${externalLoanId}", "templateContent": "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Loan Overdue Notification</title></head><body><p>Dear ${customerFirstName} ${customerLastName},</p><p>Kindly note that the below ${productName} Loan is overdue by ${overDueDays} Days:</p><ul><li><strong>Loan Request Date:</strong> ${issueDate}</li><li><strong>Insurer Name:</strong> ${productOrganizationName}</li><li><strong>Customer Name:</strong> ${customerName}</li><li><strong>Loan Amount:</strong> ${principalAmount}</li><li><strong>Loan Tenor:</strong> ${loanTenure}</li><li><strong>Loan Overdue Amount:</strong> ${totalOverdueAmount}</li></ul><p>Please make your outstanding payment of ${totalOverdueAmount} by crediting your DTB Loan Account ${settlementAccount}. Failure to do so will result in additional penalty fees accruing on the facility.</p><p>DTB</p></body></html>", "templateDescription": "Overdue Loan Customer Email", "htmlContent": false}]}, {"id": "263e041e-15aa-4972-969e-28d2500466f1", "eventType": "SUCCESSFUL_PRE_PAYMENT", "eventName": "Successful Pre-Payment", "settings": [], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}", "${amount}", "${prepaymentAmount}", "${paidPrepaymentAmount}"], "templates": []}, {"id": "54b37109-745d-4aed-9cde-7d9ed7e7ff43", "eventType": "LOAN_REPAYMENT", "eventName": "Loan Repayment", "settings": [{"id": "72e1dad9-6acd-4ad6-9ae5-980b6c2e7a4e", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${customerLastName}", "${productName}", "${installmentDueDate}", "${installmentAmount}", "${flexCubeLoanRef}"], "templates": [{"id": "ae149236-88d3-4d32-a447-4986c2390226", "templateName": "successful_loan_repayment_customer_sms", "templateSubject": "Loan Repayment", "templateContent": "Dear ${customerFirstName} ${customerLastName},Your ${productName} loan repayment for Loan Ref: ${flexLoanRef} has been received. Next installment Amount: ${installmentAmount} Next Installment Date: ${installmentDueDate}.DTB", "templateDescription": "Loan Repayment", "htmlContent": false}]}, {"id": "50496e98-6849-47f9-beab-300bd378b776", "eventType": "LOAN_APPLICATION_APPROVAL", "eventName": "Loan Application Approval", "settings": [], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${loanId}", "${customerName}", "${customerFirstName}", "${customerLastName}", "${loanAmount}", "${loanTenure}", "${loanPurpose}", "${providerItemReference}", "${backofficeLink}", "${requestStatus}", "${nominatedCustomerAccount}", "${loanBranch}", "${requestReference}"], "templates": []}, {"id": "2fadfdd4-be37-43f8-8fef-f0f9a6749535", "eventType": "APPROVAL_TASK_CREATION", "eventName": "Approval Task Creation", "settings": [{"id": "51a008c5-783f-46e6-b168-9f76a69e8795", "notificationType": "BACKOFFICE_USER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${taskName}", "${taskCreationDate}", "${taskMaker}", "${loginUrl}"], "templates": [{"id": "cea3f2c5-4b51-4b92-b2c1-004a2500ad32", "templateName": "approval_task", "templateSubject": "Approval Task Pending-${taskName}", "templateContent": "<html><body> <p>Dear User,</p> <p> There is a task that requires your action.Find the details:</p><p> <strong>Task:</strong> ${taskName}</p><p> <strong>Date Created:</strong> ${taskCreationDate}</p><p> <strong>Maker :</strong> ${taskMaker}</p><p>Kindly click  <a href=${loginUrl}>here </a>to log in and review this case</p><p>DTB</p>", "templateDescription": "approval task", "htmlContent": false}]}, {"id": "0b8fe26d-645e-48bc-a4de-1e003e081cab", "eventType": "CUSTOMER_CHANGE_EMAIL", "eventName": "Customer change email", "settings": [{"id": "d6b8f4f0-909e-4d92-af51-f24ef6c28dd2", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${otp}", "${hyper_link}"], "templates": [{"id": "3a0edbbe-44a1-49a7-9443-51cf683a1adb", "templateName": "change-email-otp-message-html-email", "templateSubject": "Change email OTP", "templateContent": "<!DOCTYPE html> <html lang=\\\"en\\\"> <head> <meta charset=\\\"UTF-8\\\"> <meta http-equiv=\\\"X-UA-Compatible\\\" content=\\\"IE=edge\\\"> <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\"> </head> <body> <div> <p>Hi ${first_name},</p> <p>Verify your Email:</p> <p>Below is your one-time password that is valid for 10 minutes:</p> <p><strong>${otp}</strong></p> <p>Alternatively, use the link below</p> <a href=\"${hyper_link}\"><p><strong>Verify</strong></p></a> <p>If you did not request this, please ignore this email.</p> </div> <div> <p>Got any questions or need support? Email us at <a href=\"mailto:<EMAIL>\"><EMAIL></a>, we're always happy to help!</p> </div> <div> <p>Thanks,</p> <p>DTB</p> </div> </body> </html>", "templateDescription": "Change Email OTP", "htmlContent": false}]}, {"id": "031371c6-fbfe-4052-a47e-dcbeb320a06a", "eventType": "SUCCESSFUL_LOAN_PREPAYMENT", "eventName": "Successful Loan Prepayment", "settings": [{"id": "d9a07985-e012-431c-a6b2-cea65fd5fd7b", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${customerMiddleName}", "${customerLastName}", "${productName}"], "templates": [{"id": "ba74e94c-5912-420e-a5bc-d0b79f45b655", "templateName": "loan_successful_prepayment_customer_sms", "templateSubject": "Loan Successful Prepayment", "templateContent": "Dear ${customerFirstName} ${customerLastName}. Your ${productName} Loan Deposit has been received. Expect a follow up communication about your loan disbursement details. DTB.", "templateDescription": "sms template for customer on loan successful prepayment", "htmlContent": false}]}, {"id": "f4589985-7f41-481d-bfbe-4d4f3916b800", "eventType": "login-failed-invalid-pin", "eventName": "When the user enters invalid login PIN credentials", "settings": [{"id": "8aba222d-0769-4a02-badc-90dac90dbb13", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "f92ca8a4-aa71-44dc-85aa-bf2a98808d3a", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "df1efe3b-2986-4170-97f6-c7ccb37be370", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "00111a29-509f-4040-a35a-301f5831ca3d", "templateName": "login-failed-invalid-pin", "templateSubject": "<PERSON><PERSON><PERSON>", "templateContent": "Log in failed - Please provide the correct PIN to successfully access the service.", "templateDescription": "When the user enters invalid login PIN credentials", "htmlContent": false}]}, {"id": "5d9168d4-5d87-47e3-809c-da771e7e1585", "eventType": "sim-card-validation-fail", "eventName": "Failed SIM card validation", "settings": [{"id": "6bee203f-d73f-456c-ac0d-72c16caa8018", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "b4bbbddd-9707-4345-9b33-3ffcbd466ff8", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "a30654c5-d2af-4d93-bd4a-a28140d908fa", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${contact_center_number}"], "templates": [{"id": "3e3340da-a73f-4d4e-b3b8-5dca52c93f44", "templateName": "sim-card-validation-fail", "templateSubject": "Sim Verification", "templateContent": "Failed Login. Please contact us on ${contact_center_number}.", "templateDescription": "Failed SIM card validation", "htmlContent": false}]}, {"id": "55a9e6d4-161a-43e6-aedc-d53b2c13d7de", "eventType": "change-pin-success", "eventName": "Change PIN: Successful change of PIN: Message to be displayed", "settings": [{"id": "bcabc08f-efa4-4004-8744-9ca575fe7c41", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "03e713ac-f0d8-4894-9eee-ac05127fd897", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "71e2e07d-4e74-4210-98c4-3ff02760a8a3", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "b99b9710-1b22-4d05-bb19-feb7839d3a25", "templateName": "change-pin-success", "templateSubject": "change-pin-success", "templateContent": "You have successfully changed your PIN.Kindly log in.", "templateDescription": "Change PIN: Successful change of PIN: Message to be displayed", "htmlContent": false}]}, {"id": "1d122580-719c-4c92-8448-c1ddcccec14f", "eventType": "reset-pin-failed-wrong-number", "eventName": "Erroneous ID document number: Message to be displayed", "settings": [{"id": "bba6ae3b-617c-4c14-a7e8-36a7bbeff7eb", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "e898026b-18f7-4552-a042-c6a7a1ed2462", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "ff265302-89ad-4edc-b8a0-498bf8108f59", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${ID_Number}"], "templates": [{"id": "0934a781-f748-46bd-b1db-9f8a187d91df", "templateName": "reset-pin-failed-wrong-number", "templateSubject": "Reset pin", "templateContent": "Dear Customer, the ${ID_Number} supplied does not exist in our database. Please check and retry .", "templateDescription": "Erroneous ID document number: Message to be displayed", "htmlContent": false}]}, {"id": "3ac0b4e8-b6b2-467e-9fbc-f8e00a3ea00d", "eventType": "create-wallet-success", "eventName": "Erroneous ID document number: Message to be displayed", "settings": [{"id": "ef0ce07e-4c62-4e9c-9d4f-b3b53088f53e", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "1dd69e57-a365-4bec-ad75-0cb6e2332f25", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "3338a89c-4ae3-408b-a672-a7d56184d7a6", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${wallet_tier}", "${wallet_limit}"], "templates": [{"id": "ac1989f7-73f6-4f41-8036-4e2f41c23158", "templateName": "create-wallet-success", "templateSubject": "Reset pin", "templateContent": "Dear ${first_name}, you have been issued with a wallet a Tier ${wallet_tier} & ${wallet_limit}.", "templateDescription": "Erroneous ID document number: Message to be displayed", "htmlContent": false}]}, {"id": "bef02298-7a00-44c4-b6ad-0a811c344b41", "eventType": "customer-registration-success", "eventName": "After customer record creation", "settings": [{"id": "518776f0-b337-41e9-b67c-8589ad42e99b", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "e4682f89-916f-4817-98d8-8287a52f4b71", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "13c794be-a206-46a7-990c-017feb7e8030", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${mobile_product_name}"], "templates": [{"id": "a8f1059c-6c21-4db3-841e-3ddcaeab7812", "templateName": "customer-registration-success", "templateSubject": "Self onboarding sms", "templateContent": "Dear ${first_name}, you have been successfully registered on ${mobile_product_name}.", "templateDescription": "After customer record creation", "htmlContent": false}]}, {"id": "18f7b452-ef26-44f3-96c6-fa4359709aa6", "eventType": "customer-welcome-and-verify-email", "eventName": "Before customer record creation at verification of email address", "settings": [{"id": "6b8e4d11-97eb-4019-9826-d9b94d03d2e0", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "69326f55-52b2-4fbc-b3eb-a8a54a89cb22", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "ca13d403-ea87-4d1d-bf5e-c6d92259e814", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "158afaae-976a-4a57-8305-30483c18530c", "templateName": "customer-welcome-and-verify-email", "templateSubject": "Self onboarding sms", "templateContent": "Welcome to DTB Digital Banking App. Your registration is being processed. You shall receive an email and SMS confirmation once completed. In the meantime, login to your registered email address and verify your email address via the link sent to you.", "templateDescription": "Before customer record creation at verification of email address", "htmlContent": false}]}, {"id": "717a4165-63e3-419a-8bca-e2c929d9e7a8", "eventType": "airtime-purchase-success", "eventName": "Successful airtime purchase", "settings": [{"id": "1896e26d-b0a3-4813-9173-3082b1dd2294", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "b5647472-f8a8-46d3-b262-866ee8ebb63b", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "ec817879-03de-4618-ae1c-22847a0b2a2c", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${currency}", "${amount}", "${mobile number}", "${reference}"], "templates": [{"id": "bd001315-0abe-494d-a172-d5912e9516a5", "templateName": "airtime-purchase-success", "templateSubject": "Buy airtime in-app, sms", "templateContent": "Dear ${first_name}, you have successfully bought airtime of ${currency} ${amount} for ${mobile number}. Ref. ${reference}.", "templateDescription": "Successful airtime purchase", "htmlContent": false}]}, {"id": "2ef72d85-b63c-46ab-b791-0652580c5c1f", "eventType": "pay-bill-invalid-account-details", "eventName": "Invalid account details", "settings": [{"id": "54650fdb-bd2f-4f0d-af3d-e5c9622fcf11", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "daf0f25e-5eaa-41cc-9052-4433d544f914", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "64f546f9-dcae-47ec-8190-ce4f54fac0c2", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "35b870d8-8497-4d5f-90d7-4ec822b38d3c", "templateName": "pay-bill-invalid-account-details", "templateSubject": "Bill payments in-app, sms", "templateContent": "Invalid account details. Kindly confirm your account_number.", "templateDescription": "Invalid account details", "htmlContent": false}]}, {"id": "ef5de69a-8c77-46d8-851d-4c0ef024288f", "eventType": "pay-bill-success", "eventName": "Successful bill payments", "settings": [{"id": "05f2d745-9402-4643-b464-5110e139eaa8", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "c3d62556-783e-403c-bfb7-d2ae4a50bae3", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "ecfa522e-26af-45c8-9ba5-1c4ced0ecf27", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${currency}", "${amount}", "${biller_name}", "${reference_name}", "${biller_reference}", "${reference}"], "templates": [{"id": "2eb67528-842d-49ee-b653-91f1fd4d7921", "templateName": "pay-bill-success", "templateSubject": "Bill payments in-app, sms", "templateContent": "Payment Success ${first_name}, you have successfully paid ${currency} ${amount} to ${biller_name} with ${reference_name} ${biller_reference}. Ref ${reference}.", "templateDescription": "Successful bill payments", "htmlContent": false}]}, {"id": "4fe6ad0e-c714-47df-8724-7ab8bd79f7bd", "eventType": "beneficiary-edit-success", "eventName": "Beneficiary editing", "settings": [{"id": "aaf50197-d697-4e25-93a3-c717fe3a461f", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "adacabcf-9160-416e-8f1c-fca827a752d3", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "1ce66cc1-1094-40c5-902e-b2a41c42eaf0", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "9286ec2e-371d-40e0-9dc6-972e5756788b", "templateName": "beneficiary-edit-success", "templateSubject": "Beneficiary management in-app", "templateContent": "Your beneficiary details have been successfully amended.", "templateDescription": "Beneficiary editing", "htmlContent": false}]}, {"id": "bd948997-e500-4060-8a11-5ef1f42a6ff5", "eventType": "beneficiary-add-success", "eventName": "Beneficiary addition", "settings": [{"id": "b7e4239d-f235-4591-b8d3-b0d62d151688", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "afe5bc69-7acd-48b4-b8f6-17faff30c657", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "8689b90b-33dc-4685-9095-ced60e371f06", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "cfc99648-5133-41ee-a60e-15bb2315e60f", "templateName": "beneficiary-add-success", "templateSubject": "Beneficiary management in-app", "templateContent": "Your Beneficiary has been created successfully.", "templateDescription": "Beneficiary addition", "htmlContent": false}]}, {"id": "8e1cf0a4-585e-4736-aafa-23543da7d89b", "eventType": "send-to-pesalink-success", "eventName": "Successful payment", "settings": [{"id": "acf906e8-1eba-4586-993f-237453561972", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "8a87f156-49b9-442c-9b39-c7ef76f7d0b5", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "6cd0659a-4253-4348-9700-13bcf5c5b6d9", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${currency}", "${amount}", "${phone_number}", "${4_digit_card_number}", "${reference}"], "templates": [{"id": "1a115bee-9d65-46dc-a375-cf455fc14e45", "templateName": "send-to-pesalink-success", "templateSubject": "Credit card payment in-app", "templateContent": "Payment Success ${first_name}, you have successfully made a credit card payment of ${currency} ${amount} to ${phone_number} and ${4_digit_card_number}. Ref ${reference}.", "templateDescription": "Successful payment", "htmlContent": false}]}, {"id": "1fce7d60-5cfb-4acb-bc7d-105f5de5707e", "eventType": "pesalink-wallet-setup-success", "eventName": "Successful wallet/account set-up", "settings": [{"id": "5020b7ee-f50b-4251-b70b-6e1db86563d7", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "181042e7-6e0f-4d86-be15-dba182b73675", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "4b1c38bc-a141-4e34-a6a0-dfc663eae21d", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${value_store_type}", "${account_number}"], "templates": [{"id": "6364b6f1-2e19-4fcd-adc8-ababf2bf123f", "templateName": "pesalink-wallet-setup-success", "templateSubject": "Pesalink in-app", "templateContent": "The ${value_store_type} ${account_number} has been successfully registered on Pesalink.", "templateDescription": "Successful wallet/account set-up", "htmlContent": false}]}, {"id": "321f7b8e-9c00-4877-a575-00e21886aa0d", "eventType": "pesalink-transaction-success", "eventName": "Successful transaction", "settings": [{"id": "37640b4e-613e-437e-bb13-d554254f4f58", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "80b183a6-68f7-4acf-a854-d5f517ac42e8", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "28c43252-c94c-4b94-ae9e-a6b9428e0cf5", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${amount}", "${beneficiary_account_number}", "${reference}"], "templates": [{"id": "74f1e145-1a9a-4f48-853a-b951078e5f50", "templateName": "pesalink-transaction-success", "templateSubject": "Pesalink in-app", "templateContent": "Payment Success ${first_name}, you have successfully transferred ${currency} ${amount} to ${beneficiary_account_number}. Ref ${reference}.", "templateDescription": "Successful transaction", "htmlContent": false}]}, {"id": "526ef26b-169e-4567-a43e-b55e8c5f9278", "eventType": "pesalink-unlink-account-success", "eventName": "Successful unlinking of an account/wallet", "settings": [{"id": "39a16cd1-48cd-4fa5-bea6-c391099ef419", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "c7c06afa-a41c-4dbc-b758-6f2ae3b71514", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "6e2fd42d-3650-4769-9cf2-78eeb7a159e0", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${account_number}"], "templates": [{"id": "f226cdd1-3aaf-4aa5-96fa-eef5f28a1573", "templateName": "pesalink-unlink-account-success", "templateSubject": "Pesalink in-app", "templateContent": "The account ${account_number} has been successfully removed as your default account on Pesalink.", "templateDescription": "Successful unlinking of an account/wallet", "htmlContent": false}]}, {"id": "e25f3f6d-c889-439d-96b2-072798a0e98b", "eventType": "pesalink-link-account-success", "eventName": "Successful linking of an account/wallet", "settings": [{"id": "f514a850-a4b1-4de8-ad1b-83052e5ea284", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "5b6b4f59-02bc-444e-8c71-526d0011f035", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "d8221ad4-92b8-42a2-ab35-f6d941ad6f8e", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${account_number}"], "templates": [{"id": "d5778fea-7ce4-4903-9f67-c473d0c4a16b", "templateName": "pesalink-link-account-success", "templateSubject": "Pesalink in-app", "templateContent": "Your account ${account_number} has been successfully linked on Pesalink.", "templateDescription": "Successful linking of an account/wallet", "htmlContent": false}]}, {"id": "77c229cc-ba80-4179-a463-819d7acddcfb", "eventType": "momo-topup-success", "eventName": "Successful top-up", "settings": [{"id": "4f1ddacb-97e3-4766-9d38-32ff78a3974d", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "1b814a5e-ec21-462b-9809-2ed3e5b89ddd", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "bc5edd6e-7f88-4e4a-aaa4-5826773c0b0b", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${value_store_type}", "${currency}", "${amount}", "${mobile_rail}", "${sender_phone_number}"], "templates": [{"id": "e2800549-6903-4c67-b3fa-a7e292fcbc5b", "templateName": "momo-topup-success", "templateSubject": "Top up Wallet/Account from Mobile Money in-app, sms", "templateContent": "Top Up Successful. Your ${value_store_type} has been successfully topped up with ${currency} ${amount} via ${mobile_rail} from ${sender_phone_number}.", "templateDescription": "Successful top-up", "htmlContent": false}]}, {"id": "b8f8cf2a-a9be-4f27-b66b-231ac0c06c67", "eventType": "momo-topup-initiated", "eventName": "Account top-up via momo initiation", "settings": [{"id": "1ffd6cf1-827c-4847-93f3-5d51bf931dd1", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "d1d53809-f6db-4799-82c4-58651c58246b", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "8b1c743d-dd22-4b4c-a379-f4977c1cbd13", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "e9618738-6d16-4bfc-8c2b-3410bad2fbad", "templateName": "momo-topup-initiated", "templateSubject": "Top up Wallet/Account from Mobile Money in-app, sms", "templateContent": "Your top-up request has been initiated, wait for the authorization request from your selected top up mode.", "templateDescription": "Account top-up via momo initiation", "htmlContent": false}]}, {"id": "b50babd9-311f-497e-8ffe-5e6ad665bafc", "eventType": "send-to-momo-self-success", "eventName": "Successful payment to self", "settings": [{"id": "c432a003-678e-4241-ac59-5a4534f58bd2", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "********-77d4-48aa-b886-ac4c2fe7304a", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "cea0edaa-07f4-4238-b20a-2c2ddf88dc00", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${reference}"], "templates": [{"id": "fe60ab7d-826f-4ec0-830b-910e2c68c473", "templateName": "send-to-momo-self-success", "templateSubject": "Send to Mobile Money wallet (self) in-app, sms", "templateContent": "Good News! Your mobile money transfer to self was successful. Mobile reference ${reference}. Bank reference ${reference}.", "templateDescription": "Successful payment to self", "htmlContent": false}]}, {"id": "d3d1eb7f-f88c-4116-aa53-30f9def69394", "eventType": "send-to-momo-other-success", "eventName": "Successful payment to others", "settings": [{"id": "edc722eb-a2d6-434b-8dbf-80cf2336a616", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "1068c970-0213-443a-93c9-c55300bbe170", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "9b71e7b2-0589-4fa4-a248-148beb91f88b", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${beneficiary_mobile_number}", "${reference}"], "templates": [{"id": "ab2a98f9-010a-4a0f-a368-30a8a81fb522", "templateName": "send-to-momo-other-success", "templateSubject": "Send to Mobile Money wallet in-app, sms", "templateContent": "Good News! Your mobile money transfer to ${beneficiary_mobile_number} was successful. Mobile reference ${reference}. Bank reference ${reference}.", "templateDescription": "Successful payment to others", "htmlContent": false}]}, {"id": "73d5f9d1-f37c-451e-a9b8-c5c6e6e7b479", "eventType": "send-to-rtgs-success", "eventName": "Successful RTGS transaction", "settings": [{"id": "f247187a-0800-4be1-aa73-0f583e3ec2a6", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "bddb2525-e935-4f3f-a898-d736aba65a66", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "b3ccbda5-2a7a-422c-841d-ca7d992a31db", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${currency}", "${amount}", "${beneficiary_account_number}", "${reference}"], "templates": [{"id": "1d0e599d-7f60-4617-a93c-73325b5762a4", "templateName": "send-to-rtgs-success", "templateSubject": "Send to RTGS in-app, sms", "templateContent": "Payment Success, you have successfully transferred ${currency} ${amount} to ${beneficiary_account_number}. Ref. ${reference}.", "templateDescription": "Successful RTGS transaction", "htmlContent": false}]}, {"id": "f37713a2-7ff1-48cd-988d-9153bb565506", "eventType": "send-to-ift-success", "eventName": "Successful IFT transaction", "settings": [{"id": "cfc98775-abc9-4926-8c8e-2fbe28b6318f", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "010db08e-7726-40fc-b78b-9963e92d6dce", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "0bf4e23d-419c-4f47-8297-bd2eadbb6316", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${reference}", "${currency}", "${amount}", "${beneficiary_account_number}"], "templates": [{"id": "596ca8ab-445e-470d-97a4-512450a72f22", "templateName": "send-to-ift-success", "templateSubject": "Send to IFT in-app, sms", "templateContent": "Payment Success ${reference}, you have successfully transferred ${currency} ${amount} to ${beneficiary_account_number}.", "templateDescription": "Successful IFT transaction", "htmlContent": false}]}, {"id": "********-dcf6-4241-9e98-e69ec60369a6", "eventType": "open-fixed-deposit-success", "eventName": "On successful set up of fixed deposit account", "settings": [{"id": "46e5584c-d021-4ec0-9867-59564015b586", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "7628d989-f49d-4a7d-911d-ced11b654ce9", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "3bd61104-9bdc-40fc-8072-71e9a468486f", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${amount}", "${tenor}", "${rate}", "${maturity_date}"], "templates": [{"id": "1b346b07-b24a-4889-aed7-74aee63fe23d", "templateName": "open-fixed-deposit-success", "templateSubject": "Open a fixed deposit account in-app", "templateContent": "Dear ${first_name}, your fixed deposit of KES xxx has been set up on DBP.", "templateDescription": "On successful set up of fixed deposit account", "htmlContent": false}]}, {"id": "e1e2d8af-c1ba-45d3-ab5e-fd31701e354e", "eventType": "upgrade-wallet-fail-tax-identifier", "eventName": "Failed Tax PIN", "settings": [{"id": "7dc4deb1-dd8e-4cce-b9d8-97c23e79ad77", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "84b2300b-790a-485a-8dfb-e9fe74ce99a3", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "da5ffe9a-0c2c-4fc8-97ec-ee888d61f168", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${document_name}"], "templates": [{"id": "1e593f42-3f3c-48c3-bfd7-e90614f79f1e", "templateName": "upgrade-wallet-fail-tax-identifier", "templateSubject": "Upgrade wallet tier in-app", "templateContent": "Dear ${first_name}, The ${document_name} provided is incorrect. Kindly check and resubmit the correct ${document_name}.", "templateDescription": "Failed Tax PIN", "htmlContent": false}]}, {"id": "bfdcbf77-1732-4207-9893-0e73ab284de2", "eventType": "upgrade-wallet-success", "eventName": "Wallet upgrade success", "settings": [{"id": "e8e0efd4-a68a-48ed-b449-a31dc3cb716a", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "02979cf7-efd7-41e0-a89f-4da21b69181c", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "07473acc-5f56-480d-9f2e-73233b7a2c56", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${wallet_tier}"], "templates": [{"id": "e650481a-d473-434b-a3b3-307ce172d29c", "templateName": "upgrade-wallet-success", "templateSubject": "Upgrade wallet tier in-app", "templateContent": "Dear ${first_name}, you have successfully upgraded your wallet to ${wallet_tier}.", "templateDescription": "Wallet upgrade success", "htmlContent": false}]}, {"id": "4ca4b266-2813-4b34-8a2a-f76b2aca755e", "eventType": "fingerprint-id-does-not-exist-on-device", "eventName": "Fingerprint not existing on device", "settings": [{"id": "3ac9a219-3e60-458d-a994-d77707fd4508", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "511f0a9a-6837-4c6f-90c7-c7c670691ac4", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "6dbdcb25-541a-424f-b669-640f45200ca4", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "29e22159-010d-4343-bd4a-4b5d2d172237", "templateName": "fingerprint-id-does-not-exist-on-device", "templateSubject": "Activate/deactivate fingerprint in-app", "templateContent": "You have not enrolled your fingerprints on the device. Kindly proceed to your device settings to set this up.", "templateDescription": "Fingerprint not existing on device", "htmlContent": false}]}, {"id": "df5efd75-ccb9-450d-ba6b-dc1574b64d8e", "eventType": "face-id-does-not-exist-on-device", "eventName": "Facial ID not existing on device", "settings": [{"id": "43ec9394-add1-46f7-ad2a-527f858a17be", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "fdf50c16-3b6d-4f66-8856-0984b8dba864", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "2cdee9fa-7385-49c8-b3ef-e95960ea129b", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "36bb9634-1d47-4c1d-a9b0-502259290ef1", "templateName": "face-id-does-not-exist-on-device", "templateSubject": "Activate/deactivate facial recognition in-app", "templateContent": "You have not enrolled your face ID on the device. Kindly proceed to your device settings to set this up.", "templateDescription": "Facial ID not existing on device", "htmlContent": false}]}, {"id": "f79159d8-b52c-4f2a-9524-d7443be9c001", "eventType": "account-locked-max-security-question-retries", "eventName": "Account locked after max security question retries", "settings": [{"id": "1feafda2-bf15-4df8-8d20-426825aa1391", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "fe0943ff-7dd1-4b5a-a76b-c44febf5f9fb", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "3c90250f-48f8-4135-a255-a4eb9d659d79", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "1bb6f268-39b7-4b0c-a3eb-45ed47e73cbd", "templateName": "account-locked-max-security-question-retries", "templateSubject": "Security question retries in-app", "templateContent": "Your account has been locked. Please contact customer care.", "templateDescription": "Account locked after max security question retries", "htmlContent": false}]}, {"id": "d3d85e24-d828-4a98-834c-79787af7ce17", "eventType": "change-email-otp-message", "eventName": "Email change OTP message", "settings": [{"id": "387c7ad9-e127-4433-8886-c7c4e9ead680", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "1f2fd289-6c93-4ddf-84da-fc168e15540a", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "7be2646d-6b34-489e-8320-7a24be61b54b", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "d5994ba0-234a-4abe-8b51-ceaddd331cc3", "templateName": "change-email-otp-message", "templateSubject": "Change email in-app", "templateContent": "Check your email to confirm.", "templateDescription": "Email change OTP message", "htmlContent": false}]}, {"id": "7929ad2a-0b67-40ab-ab54-7ef3b621be15", "eventType": "reset-pin-failed-pin-mismatch", "eventName": "PIN mismatch", "settings": [{"id": "faba085d-b89e-41c5-a194-0fd9840f3cef", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "a90fea23-fdde-47e8-b8b0-581092a790c0", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "0d4de5b3-12c2-4672-b321-6028344b168d", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "dea74065-68e2-4132-8543-0ba778dd87ce", "templateName": "reset-pin-failed-pin-mismatch", "templateSubject": "Reset PIN in-app", "templateContent": "The PIN values do not match. Kindly try again.", "templateDescription": "PIN mismatch", "htmlContent": false}]}, {"id": "45684ab8-a38e-4510-a560-0ad8f8ee371d", "eventType": "reset-pin-failed-wrong-answers", "eventName": "Erroneous answers to security questions: Message to be displayed", "settings": [{"id": "1f0a6114-bd23-4cca-a5e7-c8ff200d968a", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "d33d93f6-8c94-4036-a00a-05513f42ae38", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "b87d39b6-aac1-43d9-b85e-8d2ca7d46b1d", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "f04afb9a-6869-4d2b-becc-5a0b2abb6b3d", "templateName": "reset-pin-failed-wrong-answers", "templateSubject": "Reset PIN in-app", "templateContent": "You have supplied invalid response to the security questions.", "templateDescription": "Erroneous answers to security questions: Message to be displayed", "htmlContent": false}]}, {"id": "c6d5bbec-c3fd-47eb-8012-37f44bc43733", "eventType": "reset-pin-success", "eventName": "Reset PIN: Successful Reset of PIN: Message to be displayed", "settings": [{"id": "44fc1ad0-bd9a-4f24-a55f-3b5556a7a711", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "ad96ec50-9858-45e6-b24b-564c0abec8fe", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "07711c15-7186-4324-9a54-93156190c2ce", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "30a0150d-30be-4c51-963d-4565e4da8609", "templateName": "reset-pin-success", "templateSubject": "Reset PIN in-app, sms", "templateContent": "You have successfully reset your Password. Kindly log in.", "templateDescription": "Reset PIN: Successful Reset of PIN: Message to be displayed", "htmlContent": false}]}, {"id": "ff291fa1-e0f8-4860-9bdd-b030cd108065", "eventType": "change-pin-fail-mismatch", "eventName": "Change PIN:New PIN mismatch: Message to be displayed", "settings": [{"id": "a73c4937-7cef-4196-8255-a438c583643b", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "8dc52e97-2b1a-4005-ab11-9b21ea1a8ee6", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "c88aeb9f-11a3-49f1-8921-ff82ae5244a1", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "b8c183c7-1a40-49c4-b31d-5f726612fd5d", "templateName": "change-pin-fail-mismatch", "templateSubject": "Change PIN in-app", "templateContent": "The PIN values you have supplied do not match. Kindly try again.", "templateDescription": "Change PIN:New PIN mismatch: Message to be displayed", "htmlContent": false}]}, {"id": "71d46183-b718-490f-9d04-393b138c1ab7", "eventType": "kyc_loan_exception_email", "eventName": "Loan request exceptions", "settings": [], "subscribers": [], "placeHolders": [], "templates": []}, {"id": "5c36da0e-4813-46ca-a72e-1e348d9b1d2a", "eventType": "lms_api_credentials_changed_email", "eventName": "lms api credentials changes", "settings": [], "subscribers": [], "placeHolders": [], "templates": []}, {"id": "54462146-c897-49a5-a218-0ee8f4389c32", "eventType": "test-sms", "eventName": "string", "settings": [{"id": "674ae31d-78ee-4746-908a-9d886f9ba5ce", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "f7d8c109-7bec-40ce-ad80-9c39f11f5bf4", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "ad277b81-de6a-46c3-b156-eb593f4aa1dd", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "4bfd579a-8cff-411d-b34a-91d90669da9a", "templateName": "test-sms", "templateSubject": "sms test", "templateContent": "Dear ${first_name} we are testing sms", "templateDescription": "string", "htmlContent": false}]}, {"id": "bd27861c-daaa-4eee-8877-28e36bb7e2a8", "eventType": "reset-pin-otp", "eventName": "template to reset pin using otp", "settings": [{"id": "181550b5-c71f-4743-928b-b935e1c24816", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "863996db-b33e-443a-a45e-df442cd677fe", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}, {"id": "eab3a4c1-c784-4c1a-a503-df7996edd28b", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "75829b90-1a4a-4cc6-98ab-7eba47f9febd", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${otp}", "${hash_key}"], "templates": [{"id": "d55fe05e-ee65-4864-94c9-0480e3cab75c", "templateName": "reset-pin-otp", "templateSubject": "Reset pin", "templateContent": "Dear ${first_name}, your one time pin is ${otp}", "templateDescription": "template to reset pin using otp", "htmlContent": false}, {"id": "6e4152d5-ec91-43f0-ad55-f3fe3d12f411", "templateName": "reset-pin-otp-sms", "templateSubject": "Reset pin", "templateContent": "Dear ${first_name}, your one time pin is ${otp}.\\n${hash_key}", "templateDescription": "template to reset pin using otp", "htmlContent": false}]}, {"id": "4ea60c8e-9ff0-450b-98d9-395cfb6c8207", "eventType": "sca-initiate-otp", "eventName": "One time PIN", "settings": [{"id": "66b4cdb6-16c5-443e-881c-297721b33fd6", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "166dbdcc-197c-4f41-be29-b1d401e752e4", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "05aec1e8-d092-4e96-9560-608d915a3532", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${otp}", "${hash_key}"], "templates": [{"id": "c6f8b128-49af-4315-8af1-2ca34c38326b", "templateName": "sca-initiate-otp", "templateSubject": "OTP", "templateContent": "Dear ${first_name}, your one time pin is ${otp}.\\n${hash_key}", "templateDescription": "One time PIN", "htmlContent": false}]}, {"id": "76367316-62e8-44f1-baf8-70e72a11b14b", "eventType": "cardless-withdrawal-otp", "eventName": "<PERSON><PERSON>wal OTP", "settings": [{"id": "a09bef31-fee4-46c9-8e8a-2bec26fd3147", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "f435e53a-bcb7-4254-878e-6d89f7a69acf", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "c4c01fd5-334c-4c6a-8b96-661ab2a8caeb", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "25ad7dd1-494a-4707-b4b9-8f102490f6d0", "templateName": "cardless-withdrawal-otp", "templateSubject": "<PERSON><PERSON>wal OTP", "templateContent": "Dear ${first_name}, your cardless withdrawal OTP request was successful. Your OTP is ${otp} and is valid for 5 minutes.", "templateDescription": "<PERSON><PERSON>wal OTP", "htmlContent": false}]}, {"id": "4639afc8-b362-4494-91ca-e9e99f53ffb9", "eventType": "customer-creation-otp", "eventName": "Customer Creation", "settings": [{"id": "ee5c342c-2338-427a-8e40-83fb670e8f43", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "f64ce1c0-9830-4b69-8c1c-d87d760be35b", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "fd42ac18-9190-4381-8c7a-16039ee5de2f", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": [], "templates": [{"id": "9fc79542-15eb-4a95-b86c-3dc223afb8b5", "templateName": "customer-creation-otp", "templateSubject": "Activate your X247 profile", "templateContent": "Hi ${first_name}, this is your One Time PIN ${otp}, dial *382# to complete your request.", "templateDescription": "Customer Creation", "htmlContent": false}]}, {"id": "cc3d5078-d240-41a9-a006-8d0129c5e9f9", "eventType": "profile-accounts-linking-success", "eventName": "Accounts linking", "settings": [{"id": "cb1b23e6-9ef8-407b-b981-2aa69d3a22a7", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "19763b40-878a-4492-9b4d-f125057f9fd6", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "e04b3f6d-e5c6-4e6f-8699-1feea0f3a77e", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}, {"id": "998f14f6-2ea5-40b5-aede-73b382d39450", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${account_numbers}", "${plural_hashave}", "${plural_s}"], "templates": [{"id": "********-1734-4132-8640-51b78c8bd16e", "templateName": "profile-accounts-linking-success", "templateSubject": "Success", "templateContent": "Dear ${first_name}, your account${plural_s} ${account_numbers} ${plural_hashave} been successfully linked to your DBP user profile.", "templateDescription": "Accounts linking", "htmlContent": false}, {"id": "8fa3f047-5553-4f93-8d7f-ab44c9827578", "templateName": "profile-accounts-linking-success-html-email", "templateSubject": "Accounts linking successful", "templateContent": "<!DOCTYPE html> <html lang=\\\"en\\\"> <head> <meta charset=\\\"UTF-8\\\"> <meta http-equiv=\\\"X-UA-Compatible\\\" content=\\\"IE=edge\\\"> <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\"> </head> <body> <div> <p>Dear ${first_name},</p>  <p>your account${plural_s} <strong>${account_numbers}</strong> ${plural_hashave} been successfully linked to your DBP user profile.</p>  <p>If you did not request this, please ignore this email.</p> </div> <div> <p>Got any questions or need support? Email us at <a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a>, we're always happy to help!</p> </div> <div> <p>Thanks,</p> <p>DTB</p> </div> </body> </html>", "templateDescription": "Accounts linking successful", "htmlContent": false}]}, {"id": "6fda1f37-0554-4818-b1bc-33fe14282143", "eventType": "ORGANIZATION_CREATION_EMAIL", "eventName": "Organization Creation Email", "settings": [{"id": "9d3b3896-4492-40e8-bb07-bfdcd3bca761", "notificationType": "ORGANIZATION", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${organizationName}", "${organizationEmailAddress}", "${mobileNumber}", "${bankAccount}", "${physicalAddress}", "${bankName}"], "templates": [{"id": "d3aad98d-9db2-4b8e-8c71-a4c0a1729b71", "templateName": "organization_creation_email", "templateSubject": "Organization Creation Email", "templateContent": "<html><body> <p>Dear  ${organizationName},</p> <p> You have been successfully onboarded as an Organization on the DTB Loan Origination Platform!</p><p>Please find below your organization details:</p><p> <strong>Name:</strong> ${organizationName}</p><p> <strong>Email Address:</strong> ${organizationEmailAddress}</p><p> <strong>Mobile Number :</strong> ${mobileNumber}</p><p> <strong>Bank Name :</strong> ${bankName}</p><p> <strong>Bank Account :</strong> ${bankAccount}</p><p> <strong>Physical Address :</strong> ${physicalAddress}</p><p>DTB</p>", "templateDescription": "organization creation email", "htmlContent": false}]}, {"id": "35d0b357-6bf9-4a2c-85e7-4671b86c9493", "eventType": "API_CREDENTIALS_CHANGED", "eventName": "Generated API credentials", "settings": [{"id": "029dd517-2c55-48e6-99b8-e37de0d62c40", "notificationType": "BROKER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "676d7c52-1c96-4ef6-a6e5-2be81e1a70c4", "notificationType": "BROKER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${clientId}", "${clientSecret}", "${brokerName}", "${email}"], "templates": [{"id": "050ab5ec-f7d4-45b2-a547-87df9a5d6b5a", "templateName": "lms_api_credentials_changed_email", "templateSubject": "${brokerName} -API CREDENTIALS CHANGES", "templateContent": "<html><body> <p>Dear User,</p> <p> Please note that your API  Credentials has changed. Please find below the details of the new credentials :</p><p> <strong>Client Id:</strong> ${clientId}</p><p> <strong>Client secret:</strong> ${clientSecret}</p><p> ", "templateDescription": "lms api credentials changes", "htmlContent": false}, {"id": "6d912521-fc76-4e81-9772-59a60ff101fc", "templateName": "broker_api_credential_generation_email", "templateSubject": "Broker API Credential Generation Email", "templateContent": "<html><body> <p>Greetings,</p> <p> This email address was nominated to receive the DTB Loan Origination Platform API Credential  details for ${brokerName}.Here they are  :</p><p> <strong> API Secret: </strong> ${clientSecret}</p> <p> <strong> API Key: </strong> ${clientId}</p><p>Please ensure to keep these details safe and secret.</p><p></p><p>DTB</p> ", "templateDescription": "Broker API Credential Generation Email", "htmlContent": false}]}, {"id": "4a464cc6-f976-4198-a32b-8cd2cb04087b", "eventType": "OVERDUE_LOAN_CANCELLATION", "eventName": "Loan Overdue Cancellation", "settings": [{"id": "0a15d4b0-475f-404d-afc3-1f1a902f6e0c", "notificationType": "ORGANIZATION", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${productName}", "${productCode}", "${productExposureLimit}", "${productMinimumAmount}", "${productMaximumAmount}", "${productMinimumTenure}", "${productMaximumTenure}", "${productMeasureOfTenure}", "${productInterestRateType}", "${productInterestRate}", "${productPrepaymentType}", "${productMinimumInterestValue}", "${productLoanCreationBranchValue}", "${productLoanCreationBranch}", "${productUpfrontInterestRecognitionType}", "${productOrganizationName}", "${productCbsIdentifier}", "${productOrgLimit}", "${productExternalProductName}", "${overDueDays}"], "templates": [{"id": "26ad173c-e13f-444d-8be9-6edfe6e66611", "templateName": "loan_cancellation_request_email", "templateSubject": "${productOrganizationName} – ${productName} Cancellation Instruction", "templateContent": "<!DOCTYPE html><body><p>Dear ${productOrganizationName},</p><p>Kindly consider this email an official request for cancellation and refund of the attached list of Loans for ${productName}, which are now overdue by ${overDueDays} Days.</p><p>Please make arrangements to process the refunds into the customer Loan Repayment Account numbers as per the attached list.</p><p>DTB</p></body></html>", "templateDescription": "Loan Cancellation Request", "htmlContent": false}]}, {"id": "df0a0972-29a2-4220-9153-c9d30020f921", "eventType": "LOAN_APPROVAL", "eventName": "<PERSON><PERSON>", "settings": [{"id": "090beda6-ad7e-4fda-842e-3e1f0e8f5fcc", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "68b8f7fe-7db2-404b-957e-7eac40736dba", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${customerLastName}", "${customerMiddleName}", "${customerFullName}", "${brokerName}", "${productName}", "${loanRequestDate}", "${loanId}", "${loanRepaymentAccountNumber}", "${flexCubeLoanRef}", "${loanAmount}", "${loanTenure}", "${loanPrepaymentAmount}"], "templates": [{"id": "0c9d3f47-f31c-4d80-aa69-dd5e29419914", "templateName": "loan_approval_customer_sms", "templateSubject": "<PERSON><PERSON>", "templateContent": "Congratulations, ${customerFirstName}! Your ${productName} loan has been approved. Proceed to complete your Loan Deposit payment to activate your loan. DTB.", "templateDescription": "sms template for loan approval", "htmlContent": false}, {"id": "5d50422b-de53-4587-842c-183027023efb", "templateName": "loan_approval_customer_email", "templateSubject": "${customerFirstName} – ${customerLastName} Loan Disbursement", "templateContent": "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>Loan Approval</title></head><body><p>Dear ${customerFirstName} ${customerLastName},</p><p>Congratulations! Your ${productName} <PERSON>an has been Approved. Please find below details:</p><ul><li><strong>Customer Name:</strong> ${customerFullName}</li><li><strong>Loan Request Date:</strong> ${loanRequestDate}</li><li><strong>Loan Amount:</strong> ${loanAmount}</li><li><strong>Deposit Amount:</strong> ${loanPrepaymentAmount}</li><li><strong>Loan Tenor:</strong> ${loanTenure}</li></ul><p>Kindly make your Loan Deposit payment to activate your loan.</p><p>DTB.</p></body></html>", "templateDescription": "sms template for loan approval", "htmlContent": false}]}, {"id": "c8fdb6a5-64b7-47b6-930f-d5ec8892a710", "eventType": "LOAN_REJECTION", "eventName": "Loan Rejection", "settings": [{"id": "3336caaa-0d07-4e6a-a342-c250e7be88c9", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${customerLastName}", "${productName}"], "templates": [{"id": "ab6b6f52-8252-47e1-bb33-0c7b44584525", "templateName": "loan_rejection_customer_sms", "templateSubject": "Loan Rejection", "templateContent": "Dear, ${customerFirstName} ${customerLastName} Your ${productName} loan request was not successful. DTB.", "templateDescription": "sms template for loan rejection", "htmlContent": false}]}, {"id": "7d5aca9c-9e05-43d8-93f5-9d13b6100a01", "eventType": "BROKER_CREATION_EMAIL", "eventName": "Broker Creation Email", "settings": [{"id": "c27a61ca-ac05-40a5-a9f4-10de38621a60", "notificationType": "BROKER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${brokerName}", "${email}", "${mobileNumber}", "${bankName}", "${bankAccount}", "${physicalAddress}", "${callbackUrl}", "${productName}", "${productId}"], "templates": [{"id": "6bdc27d8-23fe-461f-aff2-7353df06288f", "templateName": "broker_creation_email", "templateSubject": "Broker Creation Email", "templateContent": "<html><body> <p>Dear  ${brokerName},</p> <p> You have been successfully onboarded as an Broker on the DTB Loan Origination Platform!</p><p>Please find below your broker details:</p><p> <strong>Name:</strong> ${brokerName}</p><p> <strong>Email Address:</strong> ${email}</p><p> <strong>Mobile Number :</strong> ${mobileNumber}</p><p> <strong>Bank Name :</strong> ${bankName}</p><p> <strong>Bank Account :</strong> ${bankAccount}</p><p> <strong>Physical Address :</strong> ${physicalAddress}</p><p> <strong>CallBack Url :</strong> ${callbackUrl}</p><p>Linked Products</p><table><tr><th>Product Name</th><th>Product Id</th></tr><tr><td>${productName}</td><td>${productId}</td></tr></table><p>DTB</p>", "templateDescription": "broker creation email", "htmlContent": false}]}, {"id": "c97fc894-57aa-4d82-acc3-6eaed2c444a5", "eventType": "MARKETING_CONSENT_OPT_IN_SUCCESS", "eventName": "Marketing Consent Opt In", "settings": [{"id": "15cf7080-dca5-433a-a041-8e707c7fc4f8", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "********-7800-427d-bff0-ca889d4b024b", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${hyper_link}"], "templates": [{"id": "5e60b584-1cea-4f87-85ca-************", "templateName": "marketing-consent-opt-in-success", "templateSubject": "Success", "templateContent": "Dear ${first_name}. You have successfully subscribed to receive content updates from DTB. To change your consent preference please click on ${hyper_link}", "templateDescription": "Marketing Consent Opt In", "htmlContent": false}]}, {"id": "ee0050ee-f724-4e1b-920c-e47000e3a8f0", "eventType": "MARKETING_CONSENT_OPT_OUT_SUCCESS", "eventName": "Marketing Consent Opt Out", "settings": [{"id": "1de360c8-8b62-4bbf-af98-fcfd6b5d4e99", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "be1f4491-bbff-4df9-98ae-653baa2a435b", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${hyper_link}"], "templates": [{"id": "74316ec3-c6fa-4583-b8d3-c2d097b0c82d", "templateName": "marketing-consent-opt-out-success", "templateSubject": "Success", "templateContent": "Dear ${first_name}. You have successfully unsubscribed from receiving promotional content from DTB. To change your consent preference please click on ${hyper_link}", "templateDescription": "Marketing Consent Opt Out", "htmlContent": false}]}, {"id": "a82c0c2a-a0f2-4ff2-82ad-f3576d24e88b", "eventType": "ACCOUNT_DEBIT", "eventName": "Account debit event", "settings": [{"id": "1640db60-5911-4677-9480-14cb6b30e245", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "1fb6fe40-cea2-46a6-8953-c71ad72b6e89", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "47f7ac0c-bbfe-45b3-adbd-d53e095abf63", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${accountNumber}", "${currency}", "${amount}", "${transactionDate}", "${reference}", "${narration}"], "templates": [{"id": "8d4b0d9b-29c2-446c-846e-78d1e649312e", "templateName": "account_debit_email_template", "templateSubject": "DEBIT ALERT", "templateContent": "<html><body> <p>Dear ${customerFirstName},</p> <p> Your account ${accountNumber}, has been debited with ${currency} ${amount} at ${transactionDate}. Transaction reference ${reference}.</p> </body></html>", "templateDescription": "account debit email template", "htmlContent": false}]}, {"id": "4b9dae93-b926-46a8-8fe6-f3b9545253a6", "eventType": "ACCOUNT_CREDIT", "eventName": "Account credit event", "settings": [{"id": "08407c96-c258-4ec6-923d-c1f4a543db89", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "919260ec-581f-4364-ab70-c7dfbb9047d1", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "91acc320-2839-4cda-8eb5-b49d1487e508", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${accountNumber}", "${currency}", "${amount}", "${transactionDate}", "${reference}", "${narration}"], "templates": [{"id": "b96b8b8d-8c88-4779-8cda-280cec5b42ea", "templateName": "account_credit_email_template", "templateSubject": "CREDIT ALERT", "templateContent": "<html><body> <p>Dear ${customerFirstName},</p> <p> Your account ${accountNumber}, has been credited with ${currency} ${amount} at ${transactionDate}. Transaction reference ${reference}.</p> </body></html>", "templateDescription": "account credit email template", "htmlContent": false}]}, {"id": "d606c435-8d38-4bbe-b33d-7e9ce017543d", "eventType": "WESTERN_UNION", "eventName": "Western Union", "settings": [{"id": "bbc07ef0-b6d4-4613-bc8a-27e6509f8015", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${mtcn}", "${transaction_reference_no}"], "templates": [{"id": "a7184346-7927-4cb7-b2d6-48cc8ebc5de8", "templateName": "mts-western-union", "templateSubject": "MTS - Western Union", "templateContent": "Dear {first_name}, your western union transfer was successful with tracking number {mtcn} through DTB mobile. Ref No: {transaction_reference_no}.", "templateDescription": "Western Union Money Transfer Service", "htmlContent": false}]}, {"id": "ac655b05-3b37-4c24-9e0c-e421d0cd7c1a", "eventType": "CUSTOMER-M247-BENEFICIARY-SYNC-SUCCESS", "eventName": "Customer M247 beneficiary synchronization success", "settings": [{"id": "ee957e6c-2547-4c34-b86a-301346b1a576", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "53d729bf-7561-4628-a2b0-f968ab9dc4f0", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "113dd423-8a49-486b-bf7f-6effc27e15e7", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${last_name}"], "templates": [{"id": "9e7ae4d8-ceb4-4159-ae5a-ed3084b82924", "templateName": "customer-m247-beneficiary-sync-success-email", "templateSubject": "Your Beneficiaries Have Been Successfully Migrated", "templateContent": "<!DOCTYPE html> <html lang=\"en\"> <head> <meta charset=\"UTF-8\"> <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"> </head> <body> <p> <strong>Dear ${first_name},</strong> </br> </br> We’re pleased to inform you that your M247 beneficiaries have been successfully migrated to DTB Mobile. You can now view and manage them seamlessly under the <strong>Beneficiaries</strong> section of the app. </br> </br> If you have any questions or encounter any issues, please don’t hesitate to contact our support team. </br> </br> Thank you for choosing DTB Mobile.</p> </body> </html>", "templateDescription": "template to notify successful synchronization of customer M247 beneficiaries", "htmlContent": false}, {"id": "da31fe17-a3b3-4eda-98ec-dddc9ded9cf2", "templateName": "customer-m247-beneficiary-sync-success", "templateSubject": "Your Beneficiaries Have Been Successfully Migrated", "templateContent": "Dear ${first_name},\\nWe’re pleased to inform you that your M247 beneficiaries have been successfully migrated to DTB Mobile. You can now view and manage them seamlessly under the Beneficiaries section of the app.\\nIf you have any questions or encounter any issues, please don’t hesitate to contact our support team.\\nThank you for choosing DTB Mobile.", "templateDescription": "template to notify successful synchronization of customer M247 beneficiaries", "htmlContent": false}]}, {"id": "9ead44b1-8c02-4b56-b8c6-c81f067be852", "eventType": "MARKETING_CONSENT_OPT_IN_EMAIL_SUCCESS", "eventName": "Marketing Consent Opt In Email", "settings": [{"id": "03c8828d-b625-4522-a866-9f8dd3e75f94", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${hyper_link}"], "templates": [{"id": "412b6cef-d87b-404d-b2be-9525d065b64d", "templateName": "marketing-consent-opt-in-email-success", "templateSubject": "Success", "templateContent": "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/></head><body><div><p>Dear ${first_name},</p><p>You have successfully subscribed to receive content updates from DTB. To change your consent preference, please click on the link below.</p><p><a href=\"${hyper_link}\"><strong>Change preference</strong></a></p><p>Sincerely,</p><p>DTB</p></div></body></html>", "templateDescription": "Marketing Consent Opt In Email Success", "htmlContent": false}]}, {"id": "ebf64b3f-6845-4bf4-bb41-ecbbc59bfa4f", "eventType": "MARKETING_CONSENT_OPT_OUT_EMAIL_SUCCESS", "eventName": "Marketing Consent Opt OUT Email", "settings": [{"id": "3fac09fe-0484-45cc-90b6-c8a41d9a09ea", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${first_name}", "${hyper_link}"], "templates": [{"id": "65167df6-96f8-4291-905d-e6628b07bfac", "templateName": "marketing-consent-opt-out-email-success", "templateSubject": "Success", "templateContent": "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"/><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/></head><body><div><p>Dear ${first_name},</p><p>You have successfully unsubscribed to receive content updates from DTB. To change your consent preference, please click on the link below.</p><p><a href=\"${hyper_link}\"><strong>Change preference</strong></a></p><p>Sincerely,</p><p>DTB</p></div></body></html>", "templateDescription": "Marketing Consent Opt Out Email Success", "htmlContent": false}]}, {"id": "db5795a8-41d8-45dc-b216-1cfb3662c269", "eventType": "BALANCE_ALERT", "eventName": "Account balance event", "settings": [{"id": "6a8a527d-211c-478c-b786-149f5879c9e4", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "5a996a07-f221-4c6e-83ab-f026d0217738", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${accountNumber}", "${currency}", "${date}", "${charge}", "${balance}"], "templates": [{"id": "925edf02-d45a-497c-ac36-b3cd3ab28605", "templateName": "account_balance_email_template", "templateSubject": "ACCOUNT BALANCE", "templateContent": "<html><body> <p>Dear ${customerFirstName},</p> <p> Your account ${accountNumber} balance as of ${date} is ${currency} ${balance}.A Charge of ${currency} ${charge} has been applied.</p> </body></html>", "templateDescription": "account balance email template", "htmlContent": false}]}, {"id": "1f14b3c4-56c3-4534-a4cc-794517b2598e", "eventType": "OVERDRAFT_ALERT", "eventName": "Account overdraft event", "settings": [{"id": "aa454400-e506-4dc7-84f8-9a7c21e322da", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "5e8e8fca-bbf7-45b7-b3dc-683b89c29592", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${accountNumber}", "${currency}", "${date}", "${charge}", "${balance}"], "templates": [{"id": "ed0614a4-b3fb-4715-8b38-d0b32f2e8f46", "templateName": "account_overdraft_email_template", "templateSubject": "ACCOUNT OVERDRAFT", "templateContent": "<html><body> <p>Dear ${customerFirstName},</p> <p> Your account ${accountNumber} has been overdrawn by ${currency} ${balance} onn ${date}.A Charge of ${currency} ${charge} has been applied.</p> </body></html>", "templateDescription": "account overdraft email template", "htmlContent": false}]}, {"id": "0d48819f-4933-403c-a4c4-db0cf8d6a6d0", "eventType": "ACCOUNT_STATEMENT", "eventName": "Account statement event", "settings": [{"id": "da4ba0ac-bb92-4b2a-a4cb-e2d570af3077", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${accountNumber}", "${currency}", "${dateFrom}", "${dateTo}", "${charge}"], "templates": [{"id": "9e9be8e0-b5e4-4ae5-932b-bdbb224628fa", "templateName": "account_statement_email_template", "templateSubject": "ACCOUNT STATEMENT", "templateContent": "<html><body> <p>Dear ${customerFirstName},</p> <p> Here is your statement of period ${dateFrom} ${dateTo}.A Charge of ${currency} ${charge} has been applied.</p> </body></html>", "templateDescription": "account statement email template", "htmlContent": false}]}, {"id": "6f25784f-bfd8-4dc8-b749-c0c1ec949941", "eventType": "FULL_STATEMENT", "eventName": "Full Statement Event", "settings": [{"id": "2e2433a7-85b6-463e-bf43-35185ec4e656", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}], "subscribers": [], "placeHolders": ["${customer_name}", "${account_number}", "${from_date}", "${to_date}"], "templates": [{"id": "dd693da0-7572-445e-b8cd-83feadd18b6c", "templateName": "account_full_statement_details", "templateSubject": "Your Requested Account Statement is Ready", "templateContent": "<html><body> <p> Dear ${customer_name}, </p> <p> As per your request, your account statement has been successfully generated and is now ready for your review. Please find the statement attached to this email.</p> <p> Details: </p> <p>Account Name : ${customer_name}</p> <p>Account Number: ${account_number}</p> <p> <Statement Period: ${from_date} to ${to_date}</p><p>If you have any questions or require further assistance, feel free to contact our customer support team at <a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a></p> <p>We appreciate your continued trust in DTB.</p><p>Warm regards</p> <p>DTB Mobile Team</p> <p> Diamond Trust Bank</p> </body> </html>", "templateDescription": "Account full statement details", "htmlContent": false}]}, {"id": "51dec4e5-9d98-4b95-ad2f-1764f87a30a8", "eventType": "FIXED_DEPOSIT_MATURITY_ALERT", "eventName": "Fixed deposit maturity event", "settings": [{"id": "e9c6db58-dba1-4b4b-8c4f-12f6ad5f93e0", "notificationType": "CUSTOMER", "deliveryMode": "EMAIL", "templateName": null}, {"id": "9ba0d268-91d6-474d-b06d-a4e3709e6d99", "notificationType": "CUSTOMER", "deliveryMode": "SMS", "templateName": null}, {"id": "********-fbbf-48cc-b7dd-4ed28388037a", "notificationType": "CUSTOMER", "deliveryMode": "PUSH", "templateName": null}], "subscribers": [], "placeHolders": ["${customerFirstName}", "${accountNumber}", "${maturityDate}", "${currency}", "${amount}", "${supportNumber}"], "templates": [{"id": "6c939ff3-8aa8-496a-acb7-e5bfbeaedb5f", "templateName": "fixed_deposit_maturity_email_template", "templateSubject": "FIXED DEPOSIT MATURITY ALERT", "templateContent": "<html><body> <p>Dear ${customerFirstName},</p> <p> Your Fixed Deposit account ${accountNumber} is maturing on ${maturityDate} with an amount of ${currency} ${amount}. Please visit your nearest branch or use our app to reinvest or withdraw. For assistance, contact us at ${supportNumber}.</p> </body></html>", "templateDescription": "Fixed deposit maturity email template", "htmlContent": false}]}]