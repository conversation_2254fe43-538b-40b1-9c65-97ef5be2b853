import { mount } from 'cypress/react18'

declare global {
  declare namespace Cypress {
    interface Chainable {
      mount: typeof mount
    }
  }
  declare namespace Cypress {
    interface Chainable {
      loginToAAD: (username: string, password: string) => Chainable<Element> // Add this line to declare the command
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      setToken(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      tokenKey(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      refreshToken(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchRoles(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchChannelModules(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchPermissions(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchFilteredPermissions(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchTypes(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchModules(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchFilteredModules(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchUsers(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByEmail(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByFirstName(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByLastName(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByPhoneNumber(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByRoles(roleIds: string | string[]): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByStatus(status: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByDateCreated(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByLastLogin(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersByStatusAndRole(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      exportUsersAsExcel(): Chainable<void>
    }
  }
}
