import token from '../../fixtures/x247/token.json'
import env from '../../fixtures/x247/env.json'
import roles from '../../fixtures/x247/roles.json'
import permissions from '../../fixtures/x247/permissions.json'
import types from '../../fixtures/x247/types.json'
import filteredPermission from '../../fixtures/x247/filteredPermission.json'
import modules from '../../fixtures/x247/modules.json'
import filteredModules from '../../fixtures/x247/filteredModules.json'
import approvals from '../../fixtures/x247/approvals.json'
import users from '../../fixtures/x247/users.json'
import dateFilteredUsers from '../../fixtures/ams/dateFilteredUsers.json'
import lastLoginFilteredUsers from '../../fixtures/ams/lastLoginFilteredUsers.json'
import multipleFilteredUsers from '../../fixtures/ams/multipleFilteredUsers.json'

Cypress.Commands.add('setToken', () => {
  cy.setLocalStorage('accessToken', localStorage.accessToken)
  cy.setLocalStorage('refreshToken', localStorage.refreshToken)
})

Cypress.Commands.add('tokenKey', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/token?tokenKey=*`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('tokenKey')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/token`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('tokenKey')
})

Cypress.Commands.add('refreshToken', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/refresh-token`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('refreshToken')
})

Cypress.Commands.add('fetchRoles', () => {
  cy.intercept('GET', `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles`, {
    statusCode: 200,
    body: roles,
  }).as('fetchRoles')
})

Cypress.Commands.add('fetchPermissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions`,
    {
      statusCode: 200,
      body: permissions,
    }
  ).as('fetchPermissions')
})

Cypress.Commands.add('fetchTypes', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/types?*`,
    {
      statusCode: 200,
      body: types,
    }
  ).as('fetchTypes')
})

Cypress.Commands.add('fetchFilteredPermissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions/filter?*`,
    {
      statusCode: 200,
      body: filteredPermission,
    }
  ).as('fetchFilteredPermissions')
})

Cypress.Commands.add('fetchModules', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/modules`,
    {
      statusCode: 200,
      body: modules,
    }
  ).as('fetchModules')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/modules?*`,
    {
      statusCode: 200,
      body: filteredModules,
    }
  ).as('fetchFilteredModules')
})

Cypress.Commands.add('fetchApprovals', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals?*`,
    {
      statusCode: 200,
      body: approvals,
    }
  ).as('fetchApprovals')
})

Cypress.Commands.add('fetchUsers', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10`,
    {
      statusCode: 200,
      body: users,
    }
  ).as('fetchUsers')
})

Cypress.Commands.add('filterUsersByEmail', (filter: string) => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.filter((user) => user.email.includes(filter))
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&email=*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsersByEmail')
})

Cypress.Commands.add('filterUsersByFirstName', (filter: string) => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.filter((user) =>
    user.firstName.toLowerCase().includes(filter.toLowerCase())
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&firstName=*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsersByFirstName')
})

Cypress.Commands.add('filterUsersByLastName', (filter: string) => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.filter((user) =>
    user.lastName.toLowerCase().includes(filter.toLowerCase())
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&lastName=*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsersByLastName')
})

Cypress.Commands.add('filterUsersByPhoneNumber', (filter: string) => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.filter((user) =>
    user.phoneNumber.includes(filter)
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&phoneNumber=*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsersByPhoneNumber')
})

Cypress.Commands.add('filterUsersByRoles', (roleIds: string | string[]) => {
  const roleIdArray = Array.isArray(roleIds) ? roleIds : [roleIds]

  const usersOriginal = users.data.filter((user) =>
    user.roles.some((role) => roleIdArray.includes(role.id))
  )

  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&roleIds=*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsersByRoles')
})

Cypress.Commands.add('filterUsersByStatus', (status: string) => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.filter(
    (user) => user.status.toLowerCase() === status.toLowerCase()
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&status=*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsersByStatus')
})

Cypress.Commands.add('filterUsersByDateCreated', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&dateCreatedFrom=*`,
    {
      statusCode: 200,
      body: dateFilteredUsers,
    }
  ).as('filterUsersByDateCreated')
})

Cypress.Commands.add('filterUsersByLastLogin', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&lastLoginDateFrom=*`,
    {
      statusCode: 200,
      body: lastLoginFilteredUsers,
    }
  ).as('filterUsersByLastLogin')
})

Cypress.Commands.add('filterUsersByStatusAndRole', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?page=1&size=10&status=*&roleIds=*`,
    {
      statusCode: 200,
      body: multipleFilteredUsers,
    }
  ).as('filterUsersByStatusAndRole')
})

Cypress.Commands.add('exportUsersAsExcel', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/reports/users/export-to-excel*`,
    {
      statusCode: 200,
      headers: {
        'content-type': 'application/zip',
        'content-length': '7745',
      },
      body: '',
    }
  ).as('exportUsersAsExcel')
})
