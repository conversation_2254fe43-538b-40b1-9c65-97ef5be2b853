import env from '../../fixtures/eatta/env.json'
import token from '../../fixtures/eatta/token.json'
import channelModules from '../../fixtures/eatta/channelModules.json'
import uploadCatalogue from '../../fixtures/eatta/uploadCatalogue.json'
import catalogueData from '../../fixtures/eatta/catalogueData.json'
import roles from '../../fixtures/eatta/roles.json'
import auctions from '../../fixtures/eatta/auctions.json'
import companies from '../../fixtures/eatta/companies.json'
import companyPageOnboarding from '../../fixtures/eatta/companyPageOnboarding.json'
import banks from '../../fixtures/eatta/banks.json'
import branches from '../../fixtures/eatta/branches.json'
import paymentDetailsDTB from '../../fixtures/eatta/paymentDetailsDTB.json'
import paymentDetailsOther from '../../fixtures/eatta/paymentDetailsOther.json'
import payments from '../../fixtures/eatta/payments.json'
import commissions from '../../fixtures/eatta/commissions.json'
import setCommissions from '../../fixtures/eatta/setCommissions.json'
import companyUsers from '../../fixtures/eatta/companyUsers.json'
import registerCompanyUser from '../../fixtures/eatta/registerCompanyUser.json'
import editUser from '../../fixtures/eatta/editUser.json'
import teamMembersDTB from '../../fixtures/eatta/teamMembersDTB.json'
import teamMembersOther from '../../fixtures/eatta/teamMemberOther.json'
import onboardingKeyDTB from '../../fixtures/eatta/onboardingKeyDTB.json'
import onboardingKeyOther from '../../fixtures/eatta/onboardingKeyOther.json'
import activateCommission from '../../fixtures/eatta/activateCommission.json'

Cypress.Commands.add('setLocalStorage', (key: string, value: string) => {
  window.localStorage.setItem(key, value)
})

Cypress.Commands.add('setToken', () => {
  cy.setLocalStorage('accessToken', localStorage.accessToken)
  cy.setLocalStorage('refreshToken', localStorage.refreshToken)
})

Cypress.Commands.add('tokenKey', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/token?tokenKey=*`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('tokenKey')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/token`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('tokenKey')
})

Cypress.Commands.add('refreshToken', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/refresh-token`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('refreshToken')
})
Cypress.Commands.add('fetchChannelModules', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/user/channel-modules`,
    {
      statusCode: 200,
      body: channelModules,
    }
  ).as('fetchChannelModules')
})
Cypress.Commands.add('uploadCatalogue', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/catalogue`,
    {
      statusCode: 200,
      body: uploadCatalogue,
    }
  ).as('uploadCatalogue')
})
Cypress.Commands.add('fetchCatalogue', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/catalogue?*
`,
    {
      statusCode: 200,
      body: catalogueData,
    }
  ).as('fetchCatalogue')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: roles,
    }
  ).as('fetchCatalogue')
})
Cypress.Commands.add('fetchAuctions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/catalogue/post/summaries?*
  `,
    {
      statusCode: 200,
      body: auctions,
    }
  ).as('fetchAuctions')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: roles,
    }
  ).as('fetchAuctions')
})
Cypress.Commands.add('fetchCompanies', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/organizations?*
  `,
    {
      statusCode: 200,
      body: companies,
    }
  ).as('fetchCompanies')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: roles,
    }
  ).as('fetchCompanies')
})
Cypress.Commands.add('createCompanyPage', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/register-organization`,
    {
      statusCode: 200,
      body: companyPageOnboarding,
    }
  ).as('createCompanyPage')
})
Cypress.Commands.add('fetchBanks', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/banks`,
    {
      statusCode: 200,
      body: banks,
    }
  ).as('fetchBanks')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/banks/branches`,
    {
      statusCode: 200,
      body: roles,
    }
  ).as('fetchBranches')
})
Cypress.Commands.add('fetchBranches', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/banks/branches`,
    {
      statusCode: 200,
      body: branches,
    }
  ).as('fetchBranches')
})

Cypress.Commands.add('createPaymentDetailsPage', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/organization/*`,
    {
      statusCode: 200,
      body: paymentDetailsDTB,
    }
  ).as('createPaymentDetailsPage')
})
Cypress.Commands.add('createPaymentDetailsPage', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/organization/*`,
    {
      statusCode: 200,
      body: paymentDetailsOther,
    }
  ).as('createPaymentDetailsPage')
})

Cypress.Commands.add('fetchPayments', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/invoice?*`,
    {
      statusCode: 200,
      body: payments,
    }
  ).as('fetchPayments')
})
Cypress.Commands.add('fetchCommissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/configuration/commission?*`,
    {
      statusCode: 200,
      body: commissions,
    }
  ).as('fetchCommissions')
})
Cypress.Commands.add('setCommissions', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/configuration/commission`,
    {
      statusCode: 200,
      body: setCommissions,
    }
  ).as('setCommissions')
})
Cypress.Commands.add('activateCommission', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/configuration/commission/activate`,
    {
      statusCode: 200,
      body: activateCommission,
    }
  ).as('activateCommission')
})
Cypress.Commands.add('fetchCompanyUsers', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/users/TEST14?*`,
    {
      statusCode: 200,
      body: companyUsers,
    }
  ).as('fetchCompanyUsers')
})
Cypress.Commands.add('registerCompanyUser', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/register-user`,
    {
      statusCode: 200,
      body: registerCompanyUser,
    }
  ).as('registerCompanyUser')
})
Cypress.Commands.add('editCompanyUser', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/register-user`,
    {
      statusCode: 200,
      body: editUser,
    }
  ).as('editCompanyUser')
})
Cypress.Commands.add('addTeamMembers', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/register-user`,
    {
      statusCode: 200,
      body: teamMembersDTB,
    }
  ).as('addTeamMembers')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/key`,
    {
      statusCode: 200,
      body: onboardingKeyDTB,
    }
  ).as('OnboardingKey')
})
Cypress.Commands.add('addTeamMembers', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/eatta-service/onboarding/register-user`,
    {
      statusCode: 200,
      body: teamMembersOther,
    }
  ).as('addTeamMembers')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/key`,
    {
      statusCode: 200,
      body: onboardingKeyOther,
    }
  ).as('OnboardingKey')
})

