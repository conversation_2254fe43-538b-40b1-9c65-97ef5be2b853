/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { mount } from 'cypress/react18'

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
    namespace Cypress {
      interface Chainable {
        mount: typeof mount
        loginToAAD(username: string, password: string): Chainable<Element>
        setToken(): Chainable<void>
        setLocalStorage(key: string, value: string): Chainable<void>
        tokenKey(): Chainable<void>
        refreshToken(): Chainable<void>
        fetchRoles(): Chainable<void>
        fetchChannelModules(): Chainable<void>
        uploadCatalogue(): Chainable<void>
        fetchCatalogue(): Chainable<void>
        fetchAuctions(): Chainable<void>
        fetchCompanies(): Chainable<void>
        createCompanyPage(): Chainable<void>
        fetchBanks(): Chainable<void>
        fetchBranches(): Chainable<void>
        createPaymentDetailsPage(): Chainable<void>
        fetchPayments(): Chainable<void>
        fetchCommissions(): Chainable<void>
        setCommissions(): Chainable<void>
        activateCommission(): Chainable<void>
        fetchCompanyUsers(): Chainable<void>
        registerCompanyUser(): Chainable<void>
        editCompanyUser(): Chainable<void>
        addTeamMembers(): Chainable<void>
      }
    }
}
  