// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './x247/commands'
import './eatta/commands'
import './eattaClient/commands'
import './ams/commands'
import '@cypress/code-coverage/support'

// Alternatively you can use CommonJS syntax:
// require('./commands')

Cypress.on('uncaught:exception', (err) => {
  if (err.message.includes('ResizeObserver loop limit exceeded') || err.message.includes('ResizeObserver loop completed with undelivered notifications') || err.message.includes('Hydration')  || err.message.includes('hydrating') || err.message.includes('Cannot read properties of null')) {
    return false;
  }
});