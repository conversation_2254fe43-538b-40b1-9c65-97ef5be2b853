/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { mount } from 'cypress/react18'

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount
      loginToEATTA(
        organizationCode: string,
        username: string,
        password: string
      ): Chainable<void>
      setLocalStorage(key: string, value: string): Chainable<void>
      userInfo(): Chainable<void>
      verifyLogin(): Chainable<void>
      setClientToken(): Chainable<void>
      login(): Chainable<void>
      generateOtp(): Chainable<void>
      verifyBrokerLogin(): Chainable<void>
      verifyBuyerLogin(): Chainable<void>
      verifyPartnerLogin(): Chainable<void>
      ChangePassword(): Chainable<void>
      fetchBrokerToken(): Chainable<void>
      fetchBuyerToken(): Chainable<void>
      fetchPartnerToken(): Chainable<void>
      forgotPassword(): Chainable<void>
      fetchAuctions(): Chainable<void>
      fetchBrokerInvoiceEntries(): Chainable<void>
      fetchBrokerInvoices(): Chainable<void>
      fetchBuyerInvoices(): Chainable<void>
      fetchBrokerTransactionHistory(): Chainable<void>
      fetchBuyerTransactionHistory(): Chainable<void>
      fetchBrokerLotsInvoiced(): Chainable<void>
      fetchBuyerLotsInvoiced(): Chainable<void>
      fetchCheckoutLots(): Chainable<void>
      fetchInsights(): Chainable<void>
      uploadInvoice(): Chainable<void>
      processInvoice(): Chainable<void>
      sendBrokerInvoice(): Chainable<void>
    }
  }
}
