import env from '../../fixtures/eattaClient/env.json'
import brokerToken from '../../fixtures/eattaClient/brokerToken.json'
import buyerToken from '../../fixtures/eattaClient/buyerToken.json'
import getCredentials from '../../fixtures/eattaClient/loginResponse.json'
import onboardingKey from '../../fixtures/eattaClient/onboardingKey.json'
import generateOtp from '../../fixtures/eattaClient/generateOtp.json'
import deliverMode from '../../fixtures/eattaClient/deliverMode.json'
import verifyBrokerLogin from '../../fixtures/eattaClient/verifyBrokerLogin.json'
import auctions from '../../fixtures/eattaClient/auctions.json'
import forgotPassword from '../../fixtures/eattaClient/forgotPassword.json'
import brokerInvoiceEntries from '../../fixtures/eattaClient/brokerInvoiceEntries.json'
import brokerInvoices from '../../fixtures/eattaClient/brokerInvoice.json'
import brokerTransactionHistory from '../../fixtures/eattaClient/brokerTransactionHistory.json'
import brokerLotsInvoiced from '../../fixtures/eattaClient/brokerLotsInvoiced.json'
import singlebrokerInvoiceEntry from '../../fixtures/eattaClient/SinglebrokerInvoiceEntry.json'
import brokerAccountBalance from '../../fixtures/eattaClient/brokerAccountBalance.json'
import verifyBuyerLogin from '../../fixtures/eattaClient/verifyBuyerLogin.json'
import checkoutLots from '../../fixtures/eattaClient/checkoutLots.json'
import paymentHistory from '../../fixtures/eattaClient/paymentHistory.json'
import buyerAccountBalance from '../../fixtures/eattaClient/buyerAccountBalance.json'
import buyerLotsInvoiced from '../../fixtures/eattaClient/buyerLotsInvoiced.json'
import singlebuyerInvoiceEntry from '../../fixtures/eattaClient/singlebuyerInvoiceEntry.json'
import buyerTransactionHistory from '../../fixtures/eattaClient/buyerTransactionHistory.json'
import changePasswordKey from '../../fixtures/eattaClient/changePasswordKey.json'
import changePassword from '../../fixtures/eattaClient/changePassword.json'
import partnerToken from '../../fixtures/eattaClient/partnerToken.json'
import verifyPartnerLogin from '../../fixtures/eattaClient/verifyPartnerLogin.json'
import organizationInsights from '../../fixtures/eattaClient/organizationInsights.json'
import statsInsights from '../../fixtures/eattaClient/statsInsights.json'
import catalogueData from '../../fixtures/eattaClient/catalogueData.json'
import uploadInvoice from '../../fixtures/eattaClient/uploadInvoice.json'
import processInvoice from '../../fixtures/eattaClient/processInvoice.json'
import sendInvoice from '../../fixtures/eattaClient/sendInvoice.json'

Cypress.Commands.add('setLocalStorage', (key: string, value: string) => {
  window.localStorage.setItem(key, value)
})

Cypress.Commands.add('userInfo', () => {
  cy.setLocalStorage('username', localStorage.username)
  cy.setLocalStorage('email', localStorage.email)
  cy.setLocalStorage('phoneNumber', localStorage.phoneNumber)
})

Cypress.Commands.add('verifyLogin', () => {
  cy.setLocalStorage('deliveryMode', localStorage.deliveryMode)
  cy.setLocalStorage('otp', localStorage.otp)
})

Cypress.Commands.add('setClientToken', () => {
  cy.setLocalStorage('accessToken', localStorage.access_token)
  cy.setLocalStorage('refreshToken', localStorage.refresh_token)
})

Cypress.Commands.add('login', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/key`,
    {
      statusCode: 200,
      body: onboardingKey,
    }
  ).as('login')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/login`,
    {
      statusCode: 200,
      body: getCredentials,
    }
  ).as('login')
})

Cypress.Commands.add('generateOtp', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/otp/generate`,
    {
      statusCode: 200,
      body: deliverMode,
    }
  ).as('generateOtp')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/otp/verify`,
    {
      statusCode: 200,
      body: generateOtp,
    }
  ).as('generateOtp')
})

Cypress.Commands.add('verifyBrokerLogin', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/otp/verify`,
    {
      statusCode: 200,
      body: verifyBrokerLogin,
    }
  ).as('verifyBrokerLogin')
})

Cypress.Commands.add('fetchBrokerToken', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/login`,
    {
      statusCode: 200,
      body: brokerToken,
    }
  ).as('fetchBrokerToken')
})

Cypress.Commands.add('fetchAuctions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/post/summaries?*
    `,
    {
      statusCode: 200,
      body: auctions,
    }
  ).as('fetchAuctions')
})
Cypress.Commands.add('forgotPassword', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/password/forgot
    `,
    {
      statusCode: 200,
      body: forgotPassword,
    }
  ).as('forgotPassword')
})

Cypress.Commands.add('fetchBrokerInvoiceEntries', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/query/broker-invoice-entry?*
  `,
    {
      statusCode: 200,
      body: brokerInvoiceEntries,
    }
  ).as('fetchBrokerInvoiceEntries')
})

Cypress.Commands.add('fetchBrokerInvoices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/query/broker-invoice?*
  `,
    {
      statusCode: 200,
      body: brokerInvoices,
    }
  ).as('fetchBrokerInvoices')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/wallet/balance
  `,
    {
      statusCode: 200,
      body: brokerAccountBalance,
    }
  ).as('fetchBrokerInvoices')
})
Cypress.Commands.add('fetchBrokerLotsInvoiced', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/broker-invoice/********-52f9-40af-9497-5b3e41ade41b
  `,
    {
      statusCode: 200,
      body: brokerLotsInvoiced,
    }
  ).as('fetchBrokerLotsInvoiced')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/query/broker-invoice-entry?*
  `,
    {
      statusCode: 200,
      body: singlebrokerInvoiceEntry,
    }
  ).as('fetchBrokerLotsInvoiced')
})
Cypress.Commands.add('fetchBrokerTransactionHistory', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/wallet/transaction?*
  `,
    {
      statusCode: 200,
      body: brokerTransactionHistory,
    }
  ).as('fetchBrokerTransactionHistory')
})

Cypress.Commands.add('fetchBuyerToken', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/login`,
    {
      statusCode: 200,
      body: buyerToken,
    }
  ).as('fetchBuyerToken')
})
Cypress.Commands.add('verifyBuyerLogin', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/otp/verify`,
    {
      statusCode: 200,
      body: verifyBuyerLogin,
    }
  ).as('verifyBuyerLogin')
})

Cypress.Commands.add('fetchCheckoutLots', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue?*
  `,
    {
      statusCode: 200,
      body: checkoutLots,
    }
  ).as('fetchCheckoutLots')
})

Cypress.Commands.add('fetchBuyerInvoices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/query/broker-invoice?*
  `,
    {
      statusCode: 200,
      body: paymentHistory,
    }
  ).as('fetchBuyerInvoices')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/wallet/balance
  `,
    {
      statusCode: 200,
      body: buyerAccountBalance,
    }
  ).as('fetchBuyerInvoices')
})
Cypress.Commands.add('fetchBuyerLotsInvoiced', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/broker-invoice/31c56af0-2eb1-495a-8cf4-fda27ecb1720
  `,
    {
      statusCode: 200,
      body: buyerLotsInvoiced,
    }
  ).as('fetchBuyerLotsInvoiced')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/query/broker-invoice-entry?*
  `,
    {
      statusCode: 200,
      body: singlebuyerInvoiceEntry,
    }
  ).as('fetchBuyerLotsInvoiced')
})
Cypress.Commands.add('fetchBuyerTransactionHistory', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/wallet/transaction?*
  `,
    {
      statusCode: 200,
      body: buyerTransactionHistory,
    }
  ).as('fetchBuyerTransactionHistory')
})

Cypress.Commands.add('ChangePassword', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/key`,
    {
      statusCode: 200,
      body: changePasswordKey,
    }
  ).as('ChangePassword')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/password/change-password`,
    {
      statusCode: 200,
      body: changePassword,
    }
  ).as('ChangePassword')
})

Cypress.Commands.add('fetchPartnerToken', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/onboarding/login`,
    {
      statusCode: 200,
      body: partnerToken,
    }
  ).as('fetchPartnerToken')
})
Cypress.Commands.add('verifyPartnerLogin', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/otp/verify`,
    {
      statusCode: 200,
      body: verifyPartnerLogin,
    }
  ).as('verifyPartnerLogin')
})
Cypress.Commands.add('fetchInsights', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/stats/organization
    `,
    {
      statusCode: 200,
      body: organizationInsights,
    }
  ).as('fetchInsights')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/stats
    `,
    {
      statusCode: 200,
      body: statsInsights,
    }
  ).as('fetchInsights')
})
Cypress.Commands.add('fetchCatalogue', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue?*
`,
    {
      statusCode: 200,
      body: catalogueData,
    }
  ).as('fetchCatalogue')
})
Cypress.Commands.add('uploadInvoice', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/catalogue/broker-invoice`,
    {
      statusCode: 200,
      body: uploadInvoice,
    }
  ).as('uploadInvoice')
})
Cypress.Commands.add('processInvoice', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/invoice/broker-invoice`,
    {
      statusCode: 200,
      body: processInvoice,
    }
  ).as('processInvoice')
})
Cypress.Commands.add('sendBrokerInvoice', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/v1/eatta-service/invoice/broker-invoice/confirm`,
    {
      statusCode: 200,
      body: sendInvoice,
    }
  ).as('sendBrokerInvoice')
})
