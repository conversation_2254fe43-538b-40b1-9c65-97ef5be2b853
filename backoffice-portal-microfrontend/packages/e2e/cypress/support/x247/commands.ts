// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
import localStorage from '../../fixtures/x247/localStorage.json'
import token from '../../fixtures/x247/token.json'
import env from '../../fixtures/x247/env.json'
import roles from '../../fixtures/x247/roles.json'
import users from '../../fixtures/x247/users.json'
import permissions from '../../fixtures/x247/permissions.json'
import filteredPermission from '../../fixtures/x247/filteredPermission.json'
import paginatedPermissions from '../../fixtures/x247/paginatedPermissions.json'
import searchFilteredPermission from '../../fixtures/x247/searchFilteredPermission.json'
import notVisiblePermissions from '../../fixtures/x247/notVisiblePermissions.json'
import X247Permissions from '../../fixtures/x247/X247Permissions.json'
import modules from '../../fixtures/x247/modules.json'
import filteredModules from '../../fixtures/x247/filteredModules.json'
import approvals from '../../fixtures/x247/approvals.json'
import userApprovals from '../../fixtures/x247/userApprovals.json'
import customerApprovals from '../../fixtures/x247/customerApprovals.json'
import searchedUsers from '../../fixtures/x247/searchedUsers.json'
import lmsProducts from '../../fixtures/x247/lmsProducts.json'
import editUser from '../../fixtures/x247/editUser.json'
import assignLoanProducts from '../../fixtures/x247/assignLoanProducts.json'
import roleDetails from '../../fixtures/x247/roleDetails.json'
import filteredRoles from '../../fixtures/x247/filteredRoles.json'
import editRole from '../../fixtures/x247/editRole.json'
import types from '../../fixtures/x247/types.json'
import customers from '../../fixtures/x247/customers.json'
import customer_id from '../../fixtures/x247/customer_id.json'
import devices from '../../fixtures/x247/devices.json'
import device_id from '../../fixtures/x247/device_id.json'
import device_id_logs from '../../fixtures/x247/device_id_logs.json'
import profileAccounts from '../../fixtures/x247/profileAccounts.json'
import profileAccounts_id from '../../fixtures/x247/profileAccounts_id.json'
import activateInactiveDevice from '../../fixtures/x247/activateInactiveDevice.json'
import deactivateActiveDevice from '../../fixtures/x247/deactivateActiveDevice.json'
import activedDevices from '../../fixtures/x247/activedDevices.json'
import deactivatedDevice from '../../fixtures/x247/deactivatedDevice.json'
import accountLogs from '../../fixtures/x247/accountLogs.json'
import changeLogs from '../../fixtures/x247/changeLogs.json'
import profilePins from '../../fixtures/x247/profilePins.json'
import reviewRequest from '../../fixtures/x247/reviewRequest.json'
import approveRequest from '../../fixtures/x247/approveRequest.json'
import rejectRequest from '../../fixtures/x247/rejectRequest.json'
import newApprovals from '../../fixtures/x247/newApprovals.json'
import accounts_id from '../../fixtures/x247/account_id.json'
import accountApproval_id from '../../fixtures/x247/accountApproval_id.json'
import accountRequestApproval from '../../fixtures/x247/accountRequestApproval.json'
import accountRequestRejection from '../../fixtures/x247/accountRequestRejection.json'
import acceptActivateGroup from '../../fixtures/x247/acceptActivateGroup.json'
import rejectActivateGroup from '../../fixtures/x247/rejectActivateGroup.json'
import pinLogChanges from '../../fixtures/x247/pinLogChanges.json'
import profilePinAccount from '../../fixtures/x247/profilePinAccount.json'
import filteredCustomerDates from '../../fixtures/x247/filteredCustomerDates.json'
import afterPinReset from '../../fixtures/x247/afterPinReset.json'
import inactiveDevices from '../../fixtures/x247/inactiveDevices.json'
import activeDevices from '../../fixtures/x247/activeDevices.json'
import allDevices from '../../fixtures/x247/allDevices.json'
import ussdFilter from '../../fixtures/x247/ussdFilter.json'
import profileLinkAccounts from '../../fixtures/x247/profileLinkAccounts.json'
import linkSingleAccount from '../../fixtures/x247/linkSingleAccount.json'
import AccountLinkTarrif from '../../fixtures/x247/AccountLinkTarrif.json'
import notificationEvents from '../../fixtures/x247/notificationEvents.json'
import notificationFrequencies from '../../fixtures/x247/notificationFrequencies.json'
import notificationPreferenceChanges from '../../fixtures/x247/notificationPreferenceChanges.json'
import notificationSubscriptions from '../../fixtures/x247/notificationSubscriptions.json'
import channelModules from '../../fixtures/x247/channelModules.json'

Cypress.Commands.add('setLocalStorage', (key: string, value: string) => {
  window.localStorage.setItem(key, value)
})

Cypress.Commands.add('setToken', () => {
  cy.setLocalStorage('accessToken', localStorage.accessToken)
  cy.setLocalStorage('refreshToken', localStorage.refreshToken)
})

Cypress.Commands.add('tokenKey', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/token?tokenKey=*`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('tokenKey')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/token`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('tokenKey')
})

Cypress.Commands.add('refreshToken', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/login/refresh-token`,
    {
      statusCode: 200,
      body: token,
    }
  ).as('refreshToken')
})
Cypress.Commands.add('fetchChannelModules', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/user/channel-modules`,
    {
      statusCode: 200,
      body: channelModules,
    }
  ).as('fetchChannelModules')
})
Cypress.Commands.add('fetchRoles', () => {
  cy.intercept('GET', `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles`, {
    statusCode: 200,
    body: roles,
  }).as('fetchRoles')
})
Cypress.Commands.add('searchUsers', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/search/adusers?*`,
    {
      statusCode: 200,
      body: searchedUsers,
    }
  ).as('searchUsers')
})
Cypress.Commands.add('fetchUsers', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?*`,
    {
      statusCode: 200,
      body: users,
    }
  ).as('fetchUsers')
})
Cypress.Commands.add('fetchEditedUsers', () => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.map((user) =>
    user.id === '99a21412-1e01-4f11-a22f-28dfde6bf064'
      ? { ...user, roles: editUser.roles }
      : user
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('fetchEditedUsers')
})
Cypress.Commands.add('fetchPermissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions`,
    {
      statusCode: 200,
      body: permissions,
    }
  ).as('fetchPermissions')
})

Cypress.Commands.add('fetchPaginatedPermissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions/filter?page=2*`,
    { statusCode: 200, body: paginatedPermissions }
  ).as('fetchPaginatedPermissions')
})

Cypress.Commands.add('fetchFilteredPermissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions/filter?*`,
    {
      statusCode: 200,
      body: filteredPermission,
    }
  ).as('fetchFilteredPermissions')
})

Cypress.Commands.add('fetchNotVisiblePermissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions/filter?*isVisible=false*`,
    {
      statusCode: 200,
      body: notVisiblePermissions,
    }
  ).as('fetchNotVisiblePermissions')
})

Cypress.Commands.add('fetchX247Permissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions/filter?*groupName=X247%20Reports*`,
    {
      statusCode: 200,
      body: X247Permissions,
    }
  ).as('fetchX247Permissions')
})

Cypress.Commands.add('fetchSearchFilteredPermissions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/permissions/filter?*`,
    {
      statusCode: 200,
      body: searchFilteredPermission,
    }
  ).as('fetchSearchFilteredPermissions')
})
Cypress.Commands.add('fetchApprovals', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals?*`,
    {
      statusCode: 200,
      body: approvals,
    }
  ).as('fetchApprovals')
})
Cypress.Commands.add('fetchUserApprovals', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals?*`,
    {
      statusCode: 200,
      body: userApprovals,
    }
  ).as('fetchUserApprovals')
})
Cypress.Commands.add('fetchCustomerApprovals', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/approvals?*`,
    {
      statusCode: 200,
      body: customerApprovals,
    }
  ).as('fetchCustomerApprovals')
})
Cypress.Commands.add('fetchModules', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/modules`,
    {
      statusCode: 200,
      body: modules,
    }
  ).as('fetchModules')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/modules?*`,
    {
      statusCode: 200,
      body: filteredModules,
    }
  ).as('fetchFilteredModules')
})

Cypress.Commands.add('lmsProducts', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/lms/products?*`,
    {
      statusCode: 200,
      body: lmsProducts,
    }
  ).as('lmsProducts')
})
Cypress.Commands.add('editUser', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/*`,
    {
      statusCode: 200,
      body: editUser,
    }
  ).as('editUser')
})
Cypress.Commands.add('assignLoans', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/*/resources`,
    {
      statusCode: 200,
      body: assignLoanProducts,
    }
  ).as('assignLoans')
})
Cypress.Commands.add('filterUsers', (filter: string) => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.filter((user) => user.email.includes(filter))
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsers')
})
Cypress.Commands.add('filterCustomers', (filter: string) => {
  const filteredCustomers = customers.data.filter((customer) => {
    return (
      customer.firstName.includes(filter) ||
      customer.lastName.includes(filter) ||
      customer.email?.includes(filter)
    )
  })

  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers?page=1&size=7&firstName=${filter}`,
    {
      statusCode: 200,
      body: {
        ...customers,
        data: filteredCustomers,
      },
    }
  ).as('filterCustomers')
})

Cypress.Commands.add('filteredRoles', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/filter?*`,
    {
      statusCode: 200,
      body: filteredRoles,
    }
  ).as('filteredRoles')
})

Cypress.Commands.add('roleDetails', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: roleDetails,
    }
  ).as('roleDetails')
})
Cypress.Commands.add('editRole', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('editRole')
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('editRole')
})
Cypress.Commands.add('deleteRole', () => {
  cy.intercept(
    'DELETE',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*/make
`,
    {
      statusCode: 200,
      body: {},
    }
  ).as('deleteRole')
  cy.intercept(
    'DELETE',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*
`,
    {
      statusCode: 200,
      body: {},
    }
  ).as('deleteRole')
})
Cypress.Commands.add('filterRoles', (filter: string) => {
  let rolesOriginal = [...filteredRoles.data]
  rolesOriginal = rolesOriginal.filter((role) =>
    role.name.toLowerCase().includes(filter.toLowerCase())
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/filter?*`,
    {
      statusCode: 200,
      body: {
        ...filteredRoles,
        data: rolesOriginal,
      },
    }
  ).as('filterRoles')
})
Cypress.Commands.add('createRole', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/make`,
    {
      statusCode: 200,
      body: {},
    }
  ).as('createRole')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles`,
    {
      statusCode: 200,
      body: {},
    }
  ).as('createRole')
})
Cypress.Commands.add('createUser', () => {
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/make`,
    {
      statusCode: 200,
      body: {},
    }
  ).as('createUser')
  cy.intercept(
    'POST',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users`,
    {
      statusCode: 200,
      body: {},
    }
  ).as('createUser')
})
Cypress.Commands.add('filterUsersForApprovals', (filter: string) => {
  let usersOriginal = [...users.data]
  usersOriginal = usersOriginal.filter((user) => user.lastName.includes(filter))
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users?*`,
    {
      statusCode: 200,
      body: {
        ...users,
        data: usersOriginal,
      },
    }
  ).as('filterUsersForApprovals')
})
Cypress.Commands.add('filterApprovals', (filter: string) => {
  let approvalsOriginal = [...userApprovals.data]
  approvalsOriginal = approvalsOriginal.filter((approval) =>
    approval.maker.includes(filter)
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals?*`,
    {
      statusCode: 200,
      body: {
        ...userApprovals,
        data: approvalsOriginal,
      },
    }
  ).as('filterApprovals')
})
Cypress.Commands.add('filterNewApprovals', (filter: string) => {
  let approvalsOriginal = [...approvals.data]
  approvalsOriginal = approvalsOriginal.filter((newApprovals) =>
    newApprovals.maker.includes(filter)
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals?*`,
    {
      statusCode: 200,
      body: {
        ...newApprovals,
        data: approvalsOriginal,
      },
    }
  ).as('filterNewApprovals')
})
Cypress.Commands.add('filterRejectedApprovals', (filter: string) => {
  let approvalsOriginal = [...userApprovals.data]
  approvalsOriginal = approvalsOriginal.filter((approval) =>
    approval.status.includes(filter)
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals?*`,
    {
      statusCode: 200,
      body: {
        ...userApprovals,
        data: approvalsOriginal,
      },
    }
  ).as('filterRejectedApprovals')
})
Cypress.Commands.add('fetchTypes', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/types?*`,
    {
      statusCode: 200,
      body: types,
    }
  ).as('fetchTypes')
})

Cypress.Commands.add('fetchCustomers', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers?*`,
    {
      statusCode: 200,
      body: customers,
    }
  ).as('fetchCustomers')
})
Cypress.Commands.add('fetchCustomer_id', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/customer-accounts?account=*`,
    {
      statusCode: 200,
      body: customer_id,
    }
  ).as('fetchCustomer_id')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('fetchCustomer_id')
})
Cypress.Commands.add('fetchCustomerProfile_id', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*`,
    {
      statusCode: 200,
      body: customer_id,
    }
  ).as('fetchCustomerProfile_id')
})
// Your intercept setup
Cypress.Commands.add('fetchCustomerDevices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers//devices?*`,
    {
      statusCode: 200,
      body: devices,
    }
  ).as('fetchCustomerDevices')
})
Cypress.Commands.add('fetchCustomerActiveDevices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/devices?page=0&size=7&status=ACTIVE`,
    {
      statusCode: 200,
      body: activeDevices,
    }
  ).as('fetchCustomerActiveDevices')
})
Cypress.Commands.add('fetchCustomerAllDevices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/devices?*`,
    {
      statusCode: 200,
      body: allDevices,
    }
  ).as('fetchCustomerAllDevices')
})
Cypress.Commands.add('fetchCustomerInactiveDevices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/devices?page=0&size=7&status=INACTIVE`,
    {
      statusCode: 200,
      body: inactiveDevices,
    }
  ).as('fetchCustomerInactiveDevices')
})
Cypress.Commands.add('filterCustomerDeviceTypeByApp', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/devices?page=0&size=7&deviceType=App`,
    {
      statusCode: 200,
      body: activeDevices,
    }
  ).as('filterCustomerDeviceTypeByApp')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: roles,
    }
  ).as('filterCustomerDeviceTypeByApp')
})
Cypress.Commands.add('filterCustomerDeviceTypeByUssd', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7/devices?page=0&size=7&deviceType=Ussd`,
    {
      statusCode: 200,
      body: ussdFilter,
    }
  ).as('filterCustomerDeviceTypeByUssd')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: roles,
    }
  ).as('filterCustomerDeviceTypeByUssd')
})
Cypress.Commands.add('fetchCustomerDevice_id', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/devices/*`,
    {
      statusCode: 200,
      body: device_id,
    }
  ).as('fetchCustomerDevice_id')
})
Cypress.Commands.add('fetchCustomerDevice_id_logs', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers//devices/*/logs`,
    {
      statusCode: 200,
      body: device_id_logs,
    }
  ).as('fetchCustomerDevice_id_logs')
})
Cypress.Commands.add('fetchProfileAccounts', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers//accounts`,
    {
      statusCode: 200,
      body: profileAccounts,
    }
  ).as('fetchProfileAccounts')
})
Cypress.Commands.add('fetchLinkProfileAccounts', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/accounts`,
    {
      statusCode: 200,
      body: profileLinkAccounts,
    }
  ).as('fetchLinkProfileAccounts')
})
Cypress.Commands.add('fetchLinkSingleAccount', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7/customer-accounts
`,
    {
      statusCode: 200,
      body: linkSingleAccount,
    }
  ).as('fetchLinkSingleAccount')
})
Cypress.Commands.add('fetchLinkTarrifAccount', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/fee/tariffs
`,
    {
      statusCode: 200,
      body: AccountLinkTarrif,
    }
  ).as('fetchLinkTarrifAccount')
})
Cypress.Commands.add('fetchProfileAccount_id', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/accounts/*`,
    {
      statusCode: 200,
      body: profileAccounts_id,
    }
  ).as('fetchProfileAccount_id')
})
Cypress.Commands.add('editRoleMake', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('editRoleMake')
})

Cypress.Commands.add('editRole', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers//accounts/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('editRole')
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('editRole')
})
Cypress.Commands.add('fetchAccountlogs', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/accounts/*/logs`,
    {
      statusCode: 200,
      body: accountLogs,
    }
  ).as('fetchAccountlogs')
})
Cypress.Commands.add('fetchAccountlogs_changes', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/accounts/*/changes`,
    {
      statusCode: 200,
      body: changeLogs,
    }
  ).as('fetchAccountlogs_changes')
})
Cypress.Commands.add('fetchProfilePins_reset', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7/profile-pins`,
    {
      statusCode: 200,
      body: afterPinReset,
    }
  ).as('fetchProfilePins')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('fetchProfilePins_reset')
})
Cypress.Commands.add('fetchProfilePins', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7/profile-pins`,
    {
      statusCode: 200,
      body: profilePins,
    }
  ).as('fetchProfilePins')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('fetchProfilePins')
})
Cypress.Commands.add('fetchProfilePinsAccount', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7`,
    {
      statusCode: 200,
      body: profilePinAccount,
    }
  ).as('fetchProfilePinsAccount')
})
Cypress.Commands.add('fetchAccountsByDate', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers?page=1&size=7&dateCreatedFrom=2024-10-06&dateCreatedTo=2024-11-06`,
    {
      statusCode: 200,
      body: filteredCustomerDates,
    }
  ).as('fetchAccountsByDate')
})
Cypress.Commands.add('fetchProfilePinslogs_changes', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7/pin-changes?size=10&page=1`,
    {
      statusCode: 200,
      body: pinLogChanges,
    }
  ).as('fetchProfilePinslogs_changes')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('fetchProfilePinslogs_changes')
})
Cypress.Commands.add('pinResetApprovals', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7/pins/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('pinResetApprovals')
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('pinResetApprovals')
})
Cypress.Commands.add('linkAccountApprovals', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/83e07b30-f20f-4696-bb2d-844f6a785ba7/accounts/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('linkAccountApprovals')
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('linkAccountApprovals')
})
Cypress.Commands.add('deactivateCustomerDevice', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/devices/*/deactivate/make`,
    {
      statusCode: 200,
      body: deactivateActiveDevice,
    }
  ).as('deactivateCustomerDevice')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: permissions,
    }
  ).as('deactivateCustomerDevice')
})
Cypress.Commands.add('activateCustomerDevice', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/devices/*/activate/make`,
    {
      statusCode: 200,
      body: activateInactiveDevice,
    }
  ).as('activateCustomerDevice')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: permissions,
    }
  ).as('activateCustomerDevice')
})
Cypress.Commands.add('fetchProfilePins', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}


    /backoffice-bff/dbp/customers/*/devices/*`,
    {
      statusCode: 200,
      body: activateInactiveDevice,
    }
  ).as('fetchProfilePins')
})
Cypress.Commands.add('fetchActiveDevices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}


   /backoffice-bff/dbp/customers/*/devices?page=1&size=7`,
    {
      statusCode: 200,
      body: activedDevices,
    }
  ).as('fetchActiveDevices')
})
Cypress.Commands.add('fetchInactiveDevices', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}


   /backoffice-bff/dbp/customers/c1223b30-2d41-41fc-901a-8afcc7cd8405/devices/21564122-2875-4731-bfa2-027b680e9655`,
    {
      statusCode: 200,
      body: deactivatedDevice,
    }
  ).as('fetchInactiveDevices')
})
Cypress.Commands.add('editRole', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/undefined/pins/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('editRole')
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('editRole')
})
Cypress.Commands.add('deleteCustomerProfile', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/delete/*/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('deleteCustomerProfile')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('deleteCustomerProfile')
})
Cypress.Commands.add('deactivateCustomerProfile', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/deactivate/*/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('deactivateCustomerProfile')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('deactivateCustomerProfile')
})
Cypress.Commands.add('activateCustomerProfile', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/activate/*/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('activateCustomerProfile')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('activateCustomerProfile')
})

Cypress.Commands.add('deactivateAccount', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/accounts/*/deactivate/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('deactivateAccount')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('deactivateAccount')
})
Cypress.Commands.add('unlinkAccount', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/accounts/*/unlink/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('unlinkAccount')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('unlinkAccount')
})
Cypress.Commands.add('restrictAccount', () => {
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/accounts/*/restrict/make`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('restrictAccount')
  cy.intercept(
    'PATCH',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('restrictAccount')
})
Cypress.Commands.add('filteredTypes', (filter: string) => {
  let typesOriginal = [...types]
  typesOriginal = typesOriginal.filter((type) =>
    type.name.toLowerCase().includes(filter.toLowerCase())
  )
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}backoffice-auth/maker-checker/approvals?status=PENDING&channel=DBP&requestType=*&page=1&size=7`,
    {
      statusCode: 200,
      body: {
        ...types,
        data: typesOriginal,
      },
    }
  ).as('filteredTypes')
})
Cypress.Commands.add('fetchReviewRequest', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/*`,
    {
      statusCode: 200,
      body: reviewRequest,
    }
  ).as('fetchReviewRequest')
})
Cypress.Commands.add('ApproveRequestDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/approve/*`,
    {
      statusCode: 200,
      body: approveRequest,
    }
  ).as('ApproveRequestDetails')
})
Cypress.Commands.add('RejectRequestDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/reject/*`,
    {
      statusCode: 200,
      body: rejectRequest,
    }
  ).as('RejectRequestDetails')
})
Cypress.Commands.add('ApproveUserDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/*/approve`,
    {
      statusCode: 200,
      body: approveRequest,
    }
  ).as('ApproveUserDetails')
})
Cypress.Commands.add('RejectUserDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/users/*/reject`,
    {
      statusCode: 200,
      body: rejectRequest,
    }
  ).as('RejectUserDetails')
})
Cypress.Commands.add('UserApprovals', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('UserApprovals')
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/*`,
    {
      statusCode: 200,
      body: editRole,
    }
  ).as('UserApprovals')
})
Cypress.Commands.add('fetchNewApprovals', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/maker-checker/approvals?*`,
    {
      statusCode: 200,
      body: newApprovals,
    }
  ).as('fetchNewApprovals')
})
Cypress.Commands.add('fetchAccountsRequestType', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*`,
    {
      statusCode: 200,
      body: accounts_id,
    }
  ).as('fetchAccountsRequestType')
})
Cypress.Commands.add('fetchAccountApprovals', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/*/accounts`,
    {
      statusCode: 200,
      body: accountApproval_id,
    }
  ).as('fetchAccountApprovals')
})
Cypress.Commands.add('ApproveAccountRequestDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/approve/*`,
    {
      statusCode: 200,
      body: accountRequestApproval,
    }
  ).as('ApproveAccountRequestDetails')
})
Cypress.Commands.add('RejectAccountRequestDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/customers/reject/*`,
    {
      statusCode: 200,
      body: accountRequestRejection,
    }
  ).as('RejectAccountRequestDetails')
})
Cypress.Commands.add('ApproveActivateGroupDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/approve/*`,
    {
      statusCode: 200,
      body: acceptActivateGroup,
    }
  ).as('ApproveActivateGroupDetails')
})
Cypress.Commands.add('RejectActivateGroupDetails', () => {
  cy.intercept(
    'PUT',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-auth/roles/reject/*`,
    {
      statusCode: 200,
      body: rejectActivateGroup,
    }
  ).as('RejectActivateGroupDetails')
})
Cypress.Commands.add('notificationEvents', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/notifications/events`,
    {
      statusCode: 200,
      body: notificationEvents,
    }
  ).as('notificationEvents')
})
Cypress.Commands.add('notificationSubscriptions', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/notifications/accounts/*/subscriptions?*`,
    {
      statusCode: 200,
      body: notificationSubscriptions,
    }
  ).as('notificationSubscriptions')
})
Cypress.Commands.add('notificationFrequencies', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/notifications/events/frequencies`,
    {
      statusCode: 200,
      body: notificationFrequencies,
    }
  ).as('notificationFrequencies')
})
Cypress.Commands.add('notificationPreferenceChanges', () => {
  cy.intercept(
    'GET',
    `${env.NEXT_PUBLIC_API_BASE_URL}/backoffice-bff/dbp/accounts/*/preferences/changes`,
    {
      statusCode: 200,
      body: notificationPreferenceChanges,
    }
  ).as('notificationPreferenceChanges')
})
