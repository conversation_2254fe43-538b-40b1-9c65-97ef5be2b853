/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { mount } from 'cypress/react18'

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  declare namespace Cypress {
    interface Chainable {
      mount: typeof mount
    }
  }
  declare namespace Cypress {
    interface Chainable {
      loginToAAD: (username: string, password: string) => Chainable<Element> // Add this line to declare the command
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      setToken(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      setLocalStorage(key: string, value: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      tokenKey(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      refreshToken(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchRoles(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchChannelModules(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filteredRoles(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchUsers(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      searchUsers(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchPermissions(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchFilteredPermissions(): Chainable<void>
    }
  }

  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchPaginatedPermissions(): Chainable<void>
    }
  }

  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchNotVisiblePermissions(): Chainable<void>
    }
  }

  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchX247Permissions(): Chainable<void>
    }
  }

  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchSearchFilteredPermissions(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchModules(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchFilteredModules(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchUserApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      lmsProducts(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      editUser(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchEditedUsers(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      assignLoans(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsers(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      roleDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      editRole(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      deleteRole(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterRoles(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      createRole(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      createUser(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersForApprovals(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterApprovals(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterRejectedApprovals(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchTypes(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomers(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomer_id(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomer_accountId(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerProfile_id(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerActiveDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerInactiveDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerAllDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterCustomerDeviceTypeByApp(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterCustomerDeviceTypeByUssd(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      deactivateCustomerDevice(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      activateCustomerDevice(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchLinkProfileAccounts(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchLinkSingleAccount(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchLinkTarrifAccount(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      linkAccountApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchActiveDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchInactiveDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerDevice_id(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerDevice_id_logs(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfileAccounts(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfileAccount_id(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountsRequestType(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountlogs(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountlogs_changes(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfilePins(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchNewApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchReviewRequest(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      ApproveRequestDetails(): Chainable<void>
    }
  }

  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      ApproveUserDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      RejectUserDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      RejectRequestDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      ApproveAccountRequestDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      RejectAccountRequestDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      ApproveActivateGroupDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      RejectActivateGroupDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      UserApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      lmsProducts(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      editUser(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchEditedUsers(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      assignLoans(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsers(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filteredTypes(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterCustomers(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      roleDetails(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      editRole(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      editRoleMake(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      unlinkAccount(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      deactivateAccount(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      activateCustomerProfile(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      deactivateCustomerProfile(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      deleteCustomerProfile(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      restrictAccount(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      deleteRole(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterRoles(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      createRole(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterUsersForApprovals(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterApprovals(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterNewApprovals(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      filterRejectedApprovals(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchTypes(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomers(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomer_id(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomer_accountId(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerProfile_id(filter: string): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      deactivateCustomerDevice(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      activateCustomerDevice(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchActiveDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchInactiveDevices(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerDevice_id(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchCustomerDevice_id_logs(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfileAccounts(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfileAccount_id(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountsRequestType(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountlogs(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountlogs_changes(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfilePins(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfilePins_reset(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfilePinslogs_changes(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchProfilePinsAccount(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      pinResetApprovals(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      fetchAccountsByDate(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      notificationFrequencies(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      notificationPreferenceChanges(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      notificationEvents(): Chainable<void>
    }
  }
  declare namespace Cypress {
    interface Chainable<Subject = unknown> {
      notificationSubscriptions(): Chainable<void>
    }
  }
}

Cypress.Commands.add('mount', mount)
