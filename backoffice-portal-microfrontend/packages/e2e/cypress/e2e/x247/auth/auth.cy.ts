// cypress/support/e2e.ts

function loginViaAAD(username: string, password: string) {
  cy.visit('http://localhost:3000/')
  cy.get('button').contains('Sign in with Microsoft').click()

  // Login to your AAD tenant.
  cy.origin(
    'login.microsoftonline.com',
    {
      args: {
        username,
      },
    },
    ({ username }) => {
      cy.get('input[type="email"]').type(username, {
        log: false,
      })
      cy.get('input[type="submit"]').click()
    }
  )
  cy.origin(
    'login.microsoftonline.com',
    {
      args: {
        password,
      },
    },
    ({ password }) => {
      cy.get('input[type="password"]').should('be.visible').type(password, {
        log: false,
      })
      cy.get('input[type="submit"]').click()
    }
  )
}

Cypress.Commands.add('loginToAAD', (username: string, password: string) => {
  const log = Cypress.log({
    displayName: 'Azure Active Directory Login',
    message: [`🔐 Authenticating | ${username}`],
    autoEnd: false,
  })
  log.snapshot('before')

  loginViaAAD(username, password)

  log.snapshot('after')
  log.end()
})

describe('Azure Active Directory Authentication', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.refreshToken()
  })

  it('logs in to the backoffice via mirosoft Single Sign On', () => {
    cy.loginToAAD(Cypress.env('aad_username'), Cypress.env('aad_password'))
    cy.setToken()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.get('h5')
      .invoke('text')
      .then((text) => {
        expect(text).to.match(/Good morning, John|Good afternoon, John/)
      })
  })
})
