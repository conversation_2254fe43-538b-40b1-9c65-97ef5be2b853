describe('Customer management', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.fetchUsers()
    cy.fetchCustomers()
    cy.fetchPermissions()
    cy.fetchApprovals()
    cy.fetchModules()
    cy.lmsProducts()
    cy.refreshToken()
    cy.editUser()
    cy.editRole()
    cy.unlinkAccount()
    cy.deactivateAccount()
    cy.activateCustomerProfile()
    cy.deactivateCustomerProfile()
    cy.deleteCustomerProfile()
    cy.restrictAccount()
    cy.assignLoans()
    cy.fetchTypes()
    cy.fetchCustomers()
    cy.fetchCustomer_id('**********')
    cy.fetchAccountsRequestType()
    cy.fetchCustomerDevices()
    cy.fetchCustomerActiveDevices()
    cy.fetchCustomerInactiveDevices()
    cy.filterCustomerDeviceTypeByApp()
    cy.filterCustomerDeviceTypeByUssd()
    cy.fetchCustomerDevice_id()
    cy.deactivateCustomerDevice()
    cy.activateCustomerDevice()
    cy.fetchCustomerAllDevices()
    cy.fetchActiveDevices()
    cy.fetchInactiveDevices()
    cy.fetchCustomerDevice_id_logs()
    cy.fetchProfileAccounts()
    cy.linkAccountApprovals()
    cy.fetchLinkTarrifAccount()
    cy.fetchProfileAccount_id()
    cy.fetchLinkProfileAccounts()
    cy.fetchLinkSingleAccount()
    cy.fetchAccountlogs()
    cy.fetchNewApprovals()
    cy.fetchAccountlogs_changes()
    cy.fetchProfilePins()
    cy.fetchProfilePins_reset()
    cy.fetchProfilePinslogs_changes()
    cy.fetchProfilePinsAccount()
    cy.pinResetApprovals()
    cy.fetchAccountsByDate()
    cy.fetchCustomerApprovals()
    cy.notificationEvents()
    cy.notificationSubscriptions()
    cy.notificationFrequencies()
    cy.notificationPreferenceChanges()

    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('x24/7 Back Office').should('be.visible').click()
    cy.get('[aria-label="Customers"]').click()
  })

  it('navigates to the customer management flow', () => {
    cy.contains('Customers').should('be.visible')
  })
  it('filters the approvals', () => {
    cy.get('button').contains('Filter').should('be.visible').click()
    cy.get('button').contains('Date Created').should('be.visible').click()
  })

  it('searches for customer profiles', () => {
    cy.filterCustomers('Boulate')
    cy.get('input[id="search-box"]').type(
      'Boulate Nagre{enter}'
    )
    cy.get('tr').eq(1).contains('Action').should('be.visible').click()
    cy.contains('See more Details').should('be.visible').click()
  })
  it('activates customer account profile', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(9).contains('Action').click()
    cy.contains('Activate').should('be.visible').click()
    cy.contains(
      'Please let us know why you are activating this customer'
    ).should('be.visible')
    cy.contains('div', /^Reinstatement$/)
      .parent()
      .find('[aria-label="Checkbox"]')
      .check({ force: true })
    cy.get('.MuiTextField-root').should('be.visible').type('Kindly Approve')
    cy.get('button').contains('Activate').click()
  })
  it('deactivates customer account profile', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('Deactivate').should('be.visible').click()
    cy.contains(
      'Please let us know why you are deactivating this customer'
    ).should('be.visible')
    cy.contains('div', /^User Request$/)
      .parent()
      .find('[aria-label="Checkbox"]')
      .check({ force: true })
    cy.get('.MuiTextField-root').should('be.visible').type('Kindly Approve')
    cy.get('button').contains('Deactivate').click()
  })
  it('personal details management flow', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('.MuiFormControl-root').should('be.visible')
    cy.contains('div', /^Edit$/).click()
  })
  it('KYC Management Flow', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('KYC').click()
    cy.get('button').contains('Show Filters').click()
    cy.get('button').contains('Hide Filters').click()
    cy.get('button')
      .contains('Initiate KYC Check')
      .invoke('removeAttr', 'disabled')
      .click()
    cy.get('.MuiTableCell-root > .MuiBox-root').first().click()
  })

  it('activates a customer device', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(2).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Devices').click()
    cy.get('button').contains('Show Filters').click()
    cy.get('button').contains('Hide Filters').click()
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('Activate').should('be.visible').click()
    cy.contains('div', /^Device verification complete$/)
      .parent()
      .find('input.PrivateSwitchBase-input')
      .check({ force: true })
    cy.get('input[placeholder="Type your reason here"]').type('Kindly Approve')
    cy.get('button.MuiButton-containedPrimary.MuiButton-contained')
      .should('be.visible')
      .click()
  })
  it('deactivates a customer device', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(2).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Devices').click()
    cy.get('button').contains('Show Filters').click()
    cy.get('button').contains('Hide Filters').click()
    cy.get('tr').eq(2).contains('Action').click()
    cy.contains('Deactivate').should('be.visible').click()
    cy.contains('div', /^Suspicious Activity$/)
      .parent()
      .find('input.PrivateSwitchBase-input')
      .check({ force: true })
    cy.get('input[placeholder="Type your reason here"]').type('Kindly Approve')
    cy.get('button.MuiButton-containedPrimary.MuiButton-contained')
      .should('be.visible')
      .click()
  })
  it('filters customer by device status (active)', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Devices').click()
    cy.get('button').contains('Show Filters').click()
    cy.get('[aria-labelledby="Device Status"]').should('be.visible').click()
    cy.contains('li', 'Active').should('be.visible').click()
  })
  it('filters customer by device status (Inactive)', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Devices').click()
    cy.get('button').contains('Show Filters').click()
    cy.get('[aria-labelledby="Device Status"]').should('be.visible').click()
    cy.contains('li', 'Inactive').should('be.visible').click()
  })
  it('filters customer by device type (App)', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Devices').click()
    cy.get('button').contains('Show Filters').click()
    cy.get('[aria-labelledby="Device Type"]').should('be.visible').click()
    cy.contains('li', 'App').should('be.visible').click()
  })
  it('filters customer by device type (Ussd)', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Devices').click()
    cy.get('button').contains('Show Filters').click()
    cy.get('[aria-labelledby="Device Type"]').should('be.visible').click()
    cy.contains('li', 'USSD').should('be.visible').click()
  })

  it('navigates to single account details view', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(5).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.get('tr').eq(1).find('button').click()
    cy.contains('See More Details').should('be.visible').click()
    cy.contains('button', 'View History').should('be.visible').click()
    cy.get('svg[data-testid="CloseRoundedIcon"]').should('be.visible').click()
    cy.get('div.MuiStack-root')
      .contains('Notifications/Alerts')
      .parent()
      .find('button:contains("History")')
      .scrollIntoView()
      .click({ force: true })
    cy.get('.MuiPaper-elevation svg[data-testid="CloseRoundedIcon"]').should('be.visible').click()
    cy.get('div.MuiStack-root')
      .contains('E-statement subscriptions')
      .parent()
      .find('button:contains("History")')
      .scrollIntoView()
      .click({ force: true })
  })
  it('restricts customer account', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(5).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.get('tr').eq(1).find('button').click()
    cy.contains('See More Details').should('be.visible').click()
    cy.contains('button', 'Restrict').should('be.visible').click()
    cy.contains('div', /^Fraudulent Activity$/)
      .parent()
      .find('input.PrivateSwitchBase-input')
      .check({ force: true })
    cy.get('textarea[placeholder="Type your reason here"]')
      .should('be.visible')
      .type('Kindly Approve')
    cy.get('button.MuiButton-containedPrimary.MuiButton-contained')
      .should('be.visible')
      .click()
  })
  it('deactivates customer account', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(5).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.get('tr').eq(1).find('button').click()
    cy.contains('See More Details').should('be.visible').click()
    cy.contains('button', 'Deactivate').should('be.visible').click()
    cy.contains('div', /^User Request$/)
      .parent()
      .find('input.PrivateSwitchBase-input')
      .check({ force: true })
    cy.get('textarea[placeholder="Type your reason here"]')
      .should('be.visible')
      .type('Kindly Approve')
    cy.get('button.MuiButton-containedPrimary.MuiButton-contained')
      .should('be.visible')
      .click()
  })
  it('unlinks customer account', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(5).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.get('tr').eq(1).find('button').click()
    cy.contains('See More Details').should('be.visible').click()
    cy.contains('button', 'Unlink').should('be.visible').click()
    cy.contains('div', /^User Request$/)
      .parent()
      .find('input.PrivateSwitchBase-input')
      .check({ force: true })
    cy.get('textarea[placeholder="Type your reason here"]')
      .should('be.visible')
      .type('Kindly Approve')
    cy.get('button.MuiButton-containedPrimary.MuiButton-contained')
      .should('be.visible')
      .click()
  })
  it('links customer account', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.contains('button', 'Link Account').should('be.visible').click()
    cy.get('div.mui-5wj7jh-MuiStack-root').should('be.visible')
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('button').contains('Next')
      .should('be.visible')
      .click()
    cy.get('.MuiDrawer-root.MuiDrawer-modal form').should('be.visible').scrollIntoView()
    cy.get('button').contains('Submit to Checker')
      .should('be.visible')
      .click()
  })
  it('links customer account & subscribe to Tarrif', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.contains('button', 'Link Account').should('be.visible').click()
    cy.get('div.mui-5wj7jh-MuiStack-root').should('be.visible')
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('span')
      .contains('Expand')
      .should('be.visible')
      .click()
    cy.get('div.mui-upg5xr-MuiStack-root').first().should('be.visible').click()
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('input[value="CUSTOMER"]')
      .scrollIntoView()
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('button').contains('Save')
      .should('be.visible')
      .click()
    cy.get('.MuiDrawer-root.MuiDrawer-modal form').should('be.visible').scrollIntoView()
    cy.get('button').contains('Next')
      .should('be.visible')
      .click()
    cy.get('.MuiDrawer-root.MuiDrawer-modal form').should('be.visible').scrollIntoView()
    cy.get('button').contains('Submit to Checker')
      .should('be.visible')
      .click()
  })
  it('links customer account & subscribe to Notification service', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.contains('button', 'Link Account').should('be.visible').click()
    cy.get('div.mui-5wj7jh-MuiStack-root').should('be.visible')
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('span')
      .contains('Expand')
      .should('be.visible')
      .click()
    cy.get('div.mui-upg5xr-MuiStack-root').eq(1).should('be.visible').click()
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('.MuiDrawer-root.MuiDrawer-modal form p').contains('Credit & Debit').should('be.visible').scrollIntoView()
    cy.get('button').contains('Next')
      .should('be.visible')
      .click()
    cy.get('button').contains('Submit to Checker')
      .should('be.visible')
      .click()
  })
  it('links customer account & subscribe to E-statement service', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.contains('button', 'Link Account').should('be.visible').click()
    cy.get('div.mui-5wj7jh-MuiStack-root').should('be.visible')
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('span')
      .contains('Expand')
      .should('be.visible')
      .click()
    cy.get('div.mui-upg5xr-MuiStack-root').eq(2).should('be.visible').click()
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('.MuiDrawer-root.MuiDrawer-modal form').should('be.visible').scrollIntoView()
    cy.get('button').contains('Next')
      .should('be.visible')
      .click()
    cy.get('.MuiDrawer-root.MuiDrawer-modal form').should('be.visible').scrollIntoView()
    cy.get('button').contains('Submit to Checker')
      .should('be.visible')
      .click()
  })
  it('links customer account & subscribe to periodic balance alerts service', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Accounts').click()
    cy.contains('button', 'Link Account').should('be.visible').click()
    cy.get('div.mui-5wj7jh-MuiStack-root').should('be.visible')
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('span')
      .contains('Expand')
      .should('be.visible')
      .click()
    cy.get('div.mui-upg5xr-MuiStack-root').eq(3).should('be.visible').click()
    cy.get('input.PrivateSwitchBase-input')
      .should('not.be.disabled')
      .check({ force: true })
      .should('be.checked')
    cy.get('.MuiDrawer-root.MuiDrawer-modal form').should('be.visible').scrollIntoView()
    cy.get('button').contains('Next')
      .should('be.visible')
      .click()
    cy.get('.MuiDrawer-root.MuiDrawer-modal form').should('be.visible').scrollIntoView()
    cy.get('button').contains('Submit to Checker')
      .should('be.visible')
      .click()
  })
  it('deletes customer account profile', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('Delete').should('be.visible').click()
    cy.contains(
      'Please let us know why you are deleting this customer profile'
    ).should('be.visible')
    cy.contains('div', /^User Request$/)
      .parent()
      .find('[aria-label="Checkbox"]')
      .check({ force: true })
    cy.get('.MuiTextField-root').should('be.visible').type('Kindly Approve')
    cy.get('button').contains('Delete').click()
  })
  it('wallets Management Flow', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Wallets').click()
  })
  it('security Management Flow', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(6).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Security').click()
    cy.get('button').contains('Pin History').should('be.visible').click()
    cy.get('div').contains('PIN History').parent().next('button')
      .should('be.visible')
      .click()
    cy.get('button').contains('Reset').should('be.visible').click()
    cy.get('input[type="checkbox"]').check({ force: true })
    cy.get('textarea[placeholder="Type your reason here"]')
      .should('be.visible')
      .type('Kindly Approve')
    cy.get('button')
      .contains(/^Reset$/)
      .should('be.visible')
      .click()
  })
  it('filters customers by date created', () => {
    cy.get('button').contains('Filter').should('be.visible').click()
    cy.get('button').contains('Date Created').should('be.visible').click()
    cy.get('button').contains('Apply')
      .should('be.visible')
      .click()
  })
  it('notifications Management Flow', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Notifications').click()
  })
  it('subscriptions Management Flow', () => {
    cy.contains('Customers').should('be.visible')
    cy.get('tr').eq(1).contains('Action').click()
    cy.contains('See more Details').should('be.visible').click()
    cy.get('button').contains('Subscriptions').click()
  })
  it('searches for an existing customer and populates their details', () => {
    cy.contains('Customers').click()
    cy.get('button').contains('Create new customer').click()
    cy.get('.MuiFormControl-root').should('be.visible')
    cy.get('[placeholder="Search by account number"]').type('**********{enter}')
    cy.get('.MuiCircularProgress-root').should('not.exist')
    cy.fetchCustomer_id('**********')
    cy.fetchCustomerProfile_id('**********')
  })
})
