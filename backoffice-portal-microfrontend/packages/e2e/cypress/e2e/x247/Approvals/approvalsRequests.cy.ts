describe('Approval Requests', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.fetchTypes()
    cy.fetchProfileAccounts()
    cy.fetchProfileAccount_id()
    cy.fetchAccountApprovals()
    cy.fetchReviewRequest()
    cy.ApproveRequestDetails()
    cy.RejectRequestDetails()
    cy.ApproveAccountRequestDetails()
    cy.RejectAccountRequestDetails()
    cy.ApproveActivateGroupDetails()
    cy.RejectActivateGroupDetails()
    cy.fetchCustomers()
    cy.UserApprovals()
    cy.fetchUsers()
    cy.fetchPermissions()
    cy.fetchNewApprovals()
    cy.fetchAccountsRequestType()
    cy.fetchModules()
    cy.fetchCustomerApprovals()
    cy.ApproveUserDetails()
    cy.RejectUserDetails()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('x24/7 Back Office').should('be.visible').click()
    cy.get('[aria-label="Approval requests"]').click()
  })
  /**
   * These test cases for "Create Users" follow the same pattern and logic as other CRUD operations like "Delete Users" and "Update Users".
   *
   * The core flow for each of these tests remains consistent:
   * - Filtering approvals based on the request type (e.g., Create, Update, or Delete).
   * - Searching for users by name or specific criteria.
   * - Navigating to request details and performing actions such as viewing, reviewing, approving, or rejecting requests.
   *
   * To avoid redundancy, the tests for "Delete Users" and "Update Users" follow similar steps:
   * filtering the approval requests by the appropriate type, searching for specific requests, and then handling the request actions.
   */

  context('Create Users Request Type', () => {
    context('Pending Requests', () => {
      beforeEach(() => {
        cy.contains('Pending Requests').click()
      })
      it('filters the approvals', () => {
        cy.get('button').contains('Filter').click()
        cy.get('button').contains('Request type').click()
        cy.get('button').contains('Create users').click()
        cy.filteredTypes('Create Users')
      })
      it('searches the approvals', () => {
        cy.filterApprovals('Alikula')
        cy.get('[placeholder="Search by Maker first name"]').type('Alikula')
        cy.filterApprovals('Alikula')
        cy.get('.MuiTableCell-root').contains('Alikula').parent()
      })
      it('navigates to request summary & review request', () => {
        cy.get('tr').contains('Actions').click()
        cy.get('.MuiMenu-list').contains('See request summary').click()
        cy.get('button').contains('Review Request').click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/staff-users/details'
        )
      })
      it('navigates directly to review request', () => {
        cy.get('button').contains('Actions').click()
        cy.get('.MuiMenu-list').contains('Review request').click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/staff-users/details'
        )
      })
    })
    context('All Requests', () => {
      beforeEach(() => {
        cy.contains('All Requests').click()
      })
      it('filters the approvals', () => {
        cy.get('button').contains('Filter').click()
        cy.get('button').contains('Request type').click()
        cy.get('button').contains('Create users').click()
        cy.filteredTypes('Create Users')
      })
      it('searches the approvals', () => {
        cy.filterApprovals('Alikula')
        cy.get('[placeholder="Search by Maker first name"]').type('Alikula')
        cy.filterApprovals('Alikula')
        cy.get('.MuiTableCell-root').contains('Alikula').parent()
      })
      it('navigates to request summary & go to module', () => {
        cy.get('button').contains('Actions').click()
        cy.get('.MuiList-root').contains('See request summary').click()
        cy.get('button.MuiButton-containedPrimary')
          .contains('Go to module')
          .click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/staff-users/details'
        )
      })
      it('approves request after viewing details', () => {
        cy.get('button').contains('Actions').click()
        cy.get('.MuiList-root').contains('Go To Module').click()
        cy.get('button.MuiButton-outlinedPrimary')
          .contains('View Approval Request Details')
          .click()
        cy.get('textarea[placeholder="Write your comments here"]').type(
          'Approve!'
        )
        cy.get('button.MuiButton-containedPrimary[type="submit"]')
          .contains('Approve')
          .click()
      })
      it('rejects request after viewing details', () => {
        cy.get('button').contains('Actions').click()
        cy.get('.MuiList-root').contains('Go To Module').click()
        cy.get('button.MuiButton-outlinedPrimary')
          .contains('View Approval Request Details')
          .click()
        cy.get('textarea[placeholder="Write your comments here"]').type(
          'Reject!'
        )
        cy.get('div.MuiDialogActions-root')
          .find('button')
          .contains('Reject')
          .click()
      })
    })
  })
  /**
   * These test cases for "Create Accounts" follow the same pattern and logic as other CRUD operations like "Delete Accounts" and "Update Accounts".
   *
   * The core flow for each of these tests remains consistent:
   * - Filtering approvals based on the request type (e.g., Create, Update, or Delete).
   * - Searching for accounts by name or specific criteria.
   * - Navigating to request details and performing actions such as viewing, reviewing, approving, or rejecting requests.
   *
   * To avoid redundancy, the tests for "Delete Accounts" and "Update Accounts" follow similar steps:
   * filtering the approval requests by the appropriate type, searching for specific requests, and then handling the request actions.
   */

  context('Create accounts Request Type', () => {
    context('Pending Requests', () => {
      beforeEach(() => {
        cy.contains('Pending Requests').click()
      })
      it('filters the approvals', () => {
        cy.get('button').contains('Filter').click()
        cy.get('button').contains('Request type').click()
        cy.get('button').contains('Create accounts').click()
        cy.filteredTypes('Create accounts')
      })
      it('navigates to request summary & review request', () => {
        cy.get('tr').eq(6).contains('Actions').click()
        cy.get('.MuiMenu-list').contains('See request summary').click()
        cy.get('button').contains('Review Request').click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/customers/customer'
        )
      })
      it('navigates directly to review request', () => {
        cy.get('tr').eq(4).contains('Actions').click()
        cy.get('.MuiMenu-list').contains('Review request').click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/customers/customer'
        )
      })
      it('approves request after viewing details', () => {
        cy.get('tr').eq(4).contains('Actions').click()
        cy.get('.MuiMenu-list').contains('Review request').click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/customers/customer'
        )
        cy.get('li')
          .contains('View Approval Request Details')
          .should('be.visible')
          .click()
        cy.get('.MuiDialogContent-root h6').contains('5109180005').scrollIntoView()
        cy.get('textarea[rows="4"]').should('be.visible').type('Approve!')
        cy.get('.MuiDialogContent-root button')
          .contains('Approve')
          .click()
        cy.url().should('eq', 'http://localhost:3000/dashboard/customers')
      })
      it('rejects request after viewing details', () => {
        cy.get('tr').eq(4).contains('Actions').click()
        cy.get('.MuiMenu-list').contains('Review request').click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/customers/customer'
        )
        cy.get('li')
          .contains('View Approval Request Details')
          .should('be.visible')
          .click()
        cy.get('.MuiDialogContent-root h6').contains('5109180005').scrollIntoView()
        cy.get('textarea[rows="4"]').should('be.visible').type('Reject!')
        cy.get('.MuiDialogContent-root button')
          .contains('Reject')
          .click()
        cy.url().should('eq', 'http://localhost:3000/dashboard/customers')
      })
    })
    context('All Requests', () => {
      beforeEach(() => {
        cy.contains('All Requests').click()
      })

      it('filters the approvals', () => {
        cy.get('button').contains('Filter').click()
        cy.get('button').contains('Request type').click()
        cy.get('button').contains('Create accounts').click()
        cy.filteredTypes('Create accounts')
      })

      it('searches the approvals', () => {
        cy.filterApprovals('Alikula')
        cy.get('[placeholder="Search by Maker first name"]').type('Alikula')
        cy.filterApprovals('Alikula')
        cy.get('.MuiTableCell-root').contains('Alikula').parent()
      })

      it('navigates to request summary & go to module', () => {
        cy.get('tr').eq(4).contains('Actions').click()
        cy.get('.MuiList-root').contains('See request summary').click()
        cy.get('button.MuiButton-containedPrimary')
          .contains('Go to module')
          .click()
        cy.url().should(
          'eq',
          'http://localhost:3000/dashboard/customers/customer'
        )
      })

      it('approves request after viewing details', () => {
        cy.get('tr').eq(4).contains('Actions').click()
        cy.get('.MuiList-root').contains('Go To Module').click()
        cy.get('li')
          .contains('View Approval Request Details')
          .should('be.visible')
          .click()
        cy.get('.MuiDialogContent-root h6').contains('5109180005').scrollIntoView()
        cy.get('textarea[rows="4"]').should('be.visible').type('Approve!')
        cy.get('.MuiDialogContent-root button')
          .contains('Approve')
          .click()
        cy.url().should('eq', 'http://localhost:3000/dashboard/customers')
      })

      it('rejects request after viewing details', () => {
        cy.get('tr').eq(4).contains('Actions').click()
        cy.get('.MuiList-root').contains('Go To Module').click()
        cy.get('li')
          .contains('View Approval Request Details')
          .should('be.visible')
          .click()
        cy.get('.MuiDialogContent-root h6').contains('5109180005').scrollIntoView()
        cy.get('textarea[rows="4"]').should('be.visible').type('Reject!')
        cy.get('.MuiDialogContent-root button')
          .contains('Reject')
          .click()
        cy.url().should('eq', 'http://localhost:3000/dashboard/customers')
      })
    })
  })
  context('Activate groups Request Type', () => {
    beforeEach(() => {
      cy.contains('Pending Requests').click()
    })

    it('filters the approvals', () => {
      cy.get('button').contains('Filter').click()
      cy.get('button').contains('Request type').click()
      cy.get('button').contains('Activate groups').click()
      cy.filteredTypes('Activate groups')
    })

    it('navigates to request summary & review request', () => {
      cy.get('tr').eq(11).contains('Actions').click()
      cy.get('.MuiMenu-list').contains('See request summary').click()
      cy.get('button').contains('Review Request').click()
      cy.url().should(
        'eq',
        'http://localhost:3000/dashboard/staff-users/details'
      )
    })

    it('navigates to request summary & Review Request', () => {
      cy.get('tr').eq(11).contains('Actions').click()
      cy.get('.MuiList-root').contains('See request summary').click()
      cy.get('button.MuiButton-containedPrimary')
        .contains('Review Request')
        .click()
      cy.url().should(
        'eq',
        'http://localhost:3000/dashboard/staff-users/details'
      )
    })

    it('navigates directly to review request', () => {
      cy.get('tr').eq(11).contains('Actions').click()
      cy.get('.MuiMenu-list').contains('Review request').click()
      cy.url().should(
        'eq',
        'http://localhost:3000/dashboard/staff-users/details'
      )
    })

    it('approves request after viewing details', () => {
      cy.get('tr').eq(11).contains('Actions').click()
      cy.get('.MuiMenu-list').contains('Review request').click()
      cy.url().should(
        'eq',
        'http://localhost:3000/dashboard/staff-users/details'
      )
      cy.get('button')
        .contains('View Approval Request Details')
        .should('be.visible')
        .click()
      cy.get('input[value="Sidney Omondi"]').should('be.visible').scrollIntoView()
      cy.get('textarea[placeholder="Write your comments here"]')
        .should('be.visible')
        .type('Approve!')
      cy.get('.MuiPaper-elevation button')
        .contains('Approve')
        .click()
      cy.get('button[aria-label="close"]').should('be.visible').click()
      cy.url().should(
        'eq',
        'http://localhost:3000/dashboard/staff-users/details'
      )
    })
    it('rejects request after viewing details', () => {
      cy.get('tr').eq(11).contains('Actions').click()
      cy.get('.MuiMenu-list').contains('Review request').click()
      cy.url().should(
        'eq',
        'http://localhost:3000/dashboard/staff-users/details'
      )
      cy.get('button')
        .contains('View Approval Request Details')
        .should('be.visible')
        .click()
      cy.get('input[value="Sidney Omondi"]').should('be.visible').scrollIntoView()
      cy.get('textarea[placeholder="Write your comments here"]')
        .should('be.visible')
        .type('Reject!')
      cy.get('.MuiPaper-elevation button')
        .contains('Reject')
        .click()
      cy.get('button[aria-label="close"]').should('be.visible').click()
      cy.url().should(
        'eq',
        'http://localhost:3000/dashboard/staff-users/details'
      )
    })
  })
})
