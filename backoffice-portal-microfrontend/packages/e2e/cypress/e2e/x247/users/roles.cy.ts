describe('User management', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.filteredRoles()
    cy.fetchUsers()
    cy.fetchPermissions()
    cy.fetchApprovals()
    cy.fetchModules()
    cy.lmsProducts()
    cy.fetchTypes()
    cy.refreshToken()
    cy.editRole()
    cy.editRoleMake()
    cy.deleteRole()
    cy.createRole()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('x24/7 Back Office').should('be.visible').click()
    cy.get('[aria-label="User Management"]').click()
  })
  it('navigates to the roles tab', () => {
    cy.contains('Roles').should('be.visible').click()
    cy.contains('Id am checker').should('be.visible')
    cy.contains('It maintenance').should('be.visible')
  })
  it('views roles details', () => {
    cy.contains('Roles').should('be.visible').click()
    cy.get('tr').eq(4).find('button').click()
    cy.roleDetails()
    cy.contains('View Details').should('be.visible').click()
    cy.contains('Accept delete groups').should('be.visible')
    cy.contains('Make reset password').should('be.visible')
  })
  it('filters roles', () => {
    cy.contains('Roles').should('be.visible').click()
    cy.filterRoles('Urmg')
    cy.contains('User Management').should('be.visible')
    cy.get('[placeholder="Search"]').should('be.visible').type('Urmg')
    cy.get('tr').eq(1).contains('Urmg').should('be.visible')
  })
  it('edit roles details', () => {
    cy.contains('Roles').should('be.visible').click()
    cy.get('tr').eq(5).find('button').click()
    cy.contains('Edit Details').should('be.visible').click()
    cy.get('.MuiDrawer-paper').should('be.visible')
    cy.get('.MuiAutocomplete-root.MuiAutocomplete-fullWidth .MuiOutlinedInput-root').click()
    cy.get('.MuiPaper-elevation .MuiAutocomplete-root').find('.MuiAutocomplete-endAdornment button[title="Open"]').filter(':visible').click()
    cy.get('div[id="permissions-listbox"] li').eq(5).scrollIntoView()
    cy.get('div[id="permissions-listbox"] li').contains('Profiles').should('be.visible').click({waitForAnimations: false})
    cy.get('span').contains('Profiles').should('be.visible').click()
    cy.get('.MuiAccordionActions-root').scrollIntoView()
    cy.contains('Done').click()
    cy.contains('Save').click()
  })
  it('delete role', () => {
    cy.contains('Roles').should('be.visible').click()
    cy.get('tr').eq(4).find('button').click()
    cy.contains('Delete').should('be.visible').click()
    cy.contains(
      'All users assigned to Ops-IDAM Checker will lose access to all assigned rights. Are you sure you want to proceed?'
    ).should('be.visible')
    cy.get('button').contains('Delete').should('be.visible').click()
  })
  it('creates a new role', () => {
    cy.contains('Roles').should('be.visible').click()
    cy.get('.MuiPaper-elevation').find('button[title="Close"]').should('be.visible').click()
    cy.get('button').contains('Create new role').should('be.visible').click()
    cy.get('[name="name"]').should('be.visible').type('Test role')
    cy.get('[name="description"]')
      .should('be.visible')
      .type('This is a test role')
    cy.get('span').contains('Start typing to search for a module and select rights').next('.MuiAutocomplete-root')
      .should('be.visible')
      .type('Customer')
    cy.get('.MuiAutocomplete-option').click()
    cy.get(
      '.MuiAccordionSummary-content .MuiBox-root .MuiButtonBase-root'
    ).click()
    cy.get('.MuiDrawer-paper').scrollIntoView()
    cy.get('button').contains('Create a role').should('be.visible').click()
    cy.contains('New role has been created').should('be.visible')
  })
})
