describe('User management', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.fetchTypes()
    cy.fetchUsers()
    cy.fetchPermissions()
    cy.fetchUserApprovals()
    cy.fetchModules()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('x24/7 Back Office').should('be.visible').click()
    cy.get('[aria-label="User Management"]').click()
  })
  it('navigates to the approvals tab', () => {
    cy.contains('Approval Requests').should('be.visible').click()
    cy.contains('Deactivate Users').should('be.visible')
  })
  it('filters the approvals', () => {
    cy.contains('Approval Requests').should('be.visible').click()
    cy.get('button').contains('Show Filters').should('be.visible').click()
    cy.get('[aria-labelledby="status"]').should('be.visible').click()
    cy.filterRejectedApprovals('REJECTED')
    cy.get('[data-value="REJECTED"]').click()
    cy.get('tr').should('have.length', 5)
  })
  it('searches the approvals', () => {
    cy.contains('Approval Requests').should('be.visible').click()
    cy.filterUsersForApprovals('Alikula')
    cy.filterApprovals('Alikula')
    cy.get('[placeholder="Search by Maker first name"]')
      .should('be.visible')
      .type('Alikula')
    cy.get('td')
      .contains('Alikula')
      .should('be.visible')
    cy.get('tr').should('have.length', 3)
  })
})
