describe('User management', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.fetchUsers()
    cy.fetchPermissions()
    cy.fetchApprovals()
    cy.fetchModules()
    cy.lmsProducts()
    cy.refreshToken()
    cy.editUser()
    cy.fetchTypes()
    cy.assignLoans()
    cy.searchUsers()
    cy.createUser()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('x24/7 Back Office').should('be.visible').click()
    cy.get('[aria-label="User Management"]').click()
  })

  it('navigates to the user management flow', () => {
    cy.contains('User Management').should('be.visible')
    cy.contains('+254 712293003').should('be.visible')
  })
  it('views user details', () => {
    cy.contains('User Management').should('be.visible')
    cy.get('tr').eq(4).find('button').click()
    cy.contains('View more details').should('be.visible').click()
    cy.contains('Bonface Maina').should('be.visible')
  })
  it('checks the permissions in a users role', () => {
    cy.contains('User Management').should('be.visible')
    cy.get('tr').eq(4).find('button').click()
    cy.contains('Edit Details').should('be.visible').click()
    cy.get('label').contains('Pick Existing Role').next('div').find('.MuiAutocomplete-endAdornment button[title="Open"]').click()
    cy.get('div[id="roleIds-listbox"] li').contains('User Admin Maker').parent().next('p').contains('See rights').should('be.visible').click()
    cy.get('.MuiAccordionSummary-root').find('p').contains('Users').should('be.visible').click()
    cy.get('.MuiAccordionDetails-root').find('p').contains('Super update users').should('be.visible')
    cy.get('.MuiAccordionSummary-root').find('p').contains('Users').should('be.visible').click()
    cy.get('.MuiAccordionSummary-root').find('p').contains('Groups').should('be.visible').click()
    cy.get('.MuiAccordionDetails-root').find('p').contains('Make update groups').should('be.visible')
  })
  it('deactivates user', () => {
    cy.contains('User Management').should('be.visible')
    cy.get('tr').eq(4).find('button').click()
    cy.contains('Deactivate').should('be.visible').click()
    cy.contains(
      'Bonface will lose access to all assigned rights. Are you sure you want to proceed?'
    ).should('be.visible')
  })
  it('assigns loan products', () => {
    cy.contains('User Management').should('be.visible')
    cy.get('tr').eq(4).find('button').click()
    cy.contains('Assign Loan Products').should('be.visible').click()
    cy.contains('Test Product 5').should('be.visible').click()
    cy.get('label').contains('Jubilee4 Copy').scrollIntoView()
    cy.get('label').contains('Merchant Finance').scrollIntoView()
    cy.get('button')
      .contains('Save Changes')
      .should('be.visible')
      .click()
  })
  it('filters users', () => {
    cy.filterUsers('bmaina')
    cy.contains('User Management').should('be.visible')
    cy.get('[placeholder="Search"]').should('be.visible').type('bmaina')
    cy.get('tr').eq(1).contains('Bonface Maina').should('be.visible')
  })
  it('edits user details', () => {
    cy.contains('User Management').should('be.visible')
    cy.get('tr').eq(4).find('button').click()
    cy.contains('Edit Details').should('be.visible').click()
    cy.get('label').contains('Pick Existing Role').next('div').should('be.visible').click()
    cy.get('label').contains('Pick Existing Role').next('div').click()
    cy.get('.MuiAutocomplete-popper li').eq(15).scrollIntoView()
    cy.get('.MuiAutocomplete-popper li').contains('testing 4').should('be.visible').click()
    cy.fetchEditedUsers()
    cy.get('button').contains('Save').should('be.visible').click()
    cy.get('tr').eq(4).contains('testing 4').should('be.visible')
  })
  it('creates a new user', () => {
    cy.contains('User Management').should('be.visible')
    cy.get('button').contains('Create new user').should('be.visible').click()
    cy.get('body').click(0, 0)
    cy.get('label').contains('DTB Email').next('.MuiInputBase-root').click().type('alikula')
    cy.get('span.MuiTypography-root').contains('Alikula').should('be.visible').click()
    cy.get('[name="phoneNumber"]').should('be.visible').click().type('700123456')
    cy.get('label').contains('Pick Existing Role').next('.MuiInputBase-root').click()
    cy.get('.MuiAutocomplete-popper li').eq(5).scrollIntoView()
    cy.get('.MuiAutocomplete-popper li').contains('Testing 57').should('be.visible').click()
    cy.get('button').contains('Create User').should('be.visible').click()
    cy.get('.MuiAlert-message').contains('User was Successfully Created').should('be.visible')
  })
})
