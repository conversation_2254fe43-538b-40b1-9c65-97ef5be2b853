describe('User management', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.fetchUsers()
    cy.fetchTypes()
    cy.fetchPermissions()
    cy.fetchFilteredPermissions()
    cy.fetchApprovals()
    cy.fetchModules()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('x24/7 Back Office').should('be.visible').click()
    cy.get('[aria-label="User Management"]').click()
  })
  it('navigates to the rights tab', () => {
    cy.contains('Rights').should('be.visible').click()
    cy.contains('View all users').should('be.visible')
  })
  it('searches the rights', () => {
    cy.contains('Rights').should('be.visible').click()
    cy.fetchSearchFilteredPermissions()
    cy.get('[placeholder="Search"]').should('be.visible').type('customer')
    // cy.contains('Next').should('be.visible').click()
    cy.contains('Super update customers').should('be.visible')
    cy.contains('Make activate customers').should('be.visible')
  })
  it('filters the rights', () => {
    cy.contains('Rights').should('be.visible').click()
    cy.get('button').contains('Show Filters').should('be.visible').click()
    cy.get('[aria-labelledby="Module"]').should('be.visible').click()
    cy.fetchSearchFilteredPermissions()
    cy.get('.MuiPaper-elevation li').eq(2).should('be.visible').click()
    cy.contains('Super create customers').should('be.visible')
  })
  it('views the roles assigned', () => {
    cy.contains('Rights').should('be.visible').click()
    cy.get('tr').eq(1).contains('+').should('be.visible').click()
    cy.get('.MuiPaper-elevation [placeholder="Search"]')
      .should('be.visible')
      .type('checker')
    cy.contains('Card Centre Checkers').should('be.visible')
    cy.contains('Customer maker checker').should('be.visible')
  })
})
