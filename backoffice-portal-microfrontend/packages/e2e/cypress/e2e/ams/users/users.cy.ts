import env from '../../../fixtures/x247/env.json'

describe('User Management tests', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.fetchTypes()
    cy.fetchPermissions()
    cy.fetchFilteredPermissions()
    cy.fetchModules()
    cy.fetchChannelModules()
    cy.fetchApprovals()
    cy.fetchUsers()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('Access Management').should('be.visible').click()
  })

  it('Lands on User Management module', () => {
    cy.location('pathname').should('include', '/ams')
    cy.location('pathname').should('include', '/users')
    cy.contains('User Management').should('be.visible')
  })

  it('Searches by email', () => {
    cy.get('span').contains('Search By email').click()
    cy.get('li[role="option"][data-value="email"]').should('be.visible').click()
    cy.filterUsersByEmail('am')
    cy.get('input[type="text"][placeholder="Search"]').type('am')
    cy.filterUsersByEmail('<EMAIL>')
    cy.get('input[type="text"][placeholder="Search"]').type(
      '<EMAIL>'
    )
    cy.get('table').within(() => {
      cy.get('thead th input[type="checkbox"]').should('exist')
      cy.get('thead th').contains('Name').should('be.visible')
      cy.get('thead th').contains('Status').should('be.visible')
      cy.get('thead th').contains('Phone Number').should('be.visible')
      cy.get('thead th').contains('Role').should('be.visible')
      cy.get('thead th').contains('Date Created').should('be.visible')
      cy.get('thead th')
        .contains('Last Login')
        .should('have.text', 'Last Login')
      cy.get('thead th').contains('Actions').should('have.text', 'Actions')

      cy.contains('<EMAIL>').should('be.visible')
      cy.get('tbody tr td:last-child').click()
    })
    cy.get('ul[role="menu"]').contains('Edit Details').should('be.visible')
    cy.get('ul[role="menu"]').contains('View more details').should('be.visible')
    cy.get('ul[role="menu"]').contains('Deactivate').should('be.visible')
    cy.get('ul[role="menu"]')
      .contains('Assign Loan Products')
      .should('be.visible')
  })

  it('Searches by first name', () => {
    cy.get('span').contains('Search By email').click()
    cy.get('li[role="option"][data-value="firstName"]').click()
    cy.filterUsersByFirstName('a')
    cy.get('input[type="text"][placeholder="Search"]').type('a')
    cy.filterUsersByFirstName('alex')
    cy.get('input[type="text"][placeholder="Search"]').type('lex')
    cy.get('table').within(() => {
      cy.get('tbody tr td:nth-child(2)').each(($cell) => {
        cy.wrap($cell).contains('Alex').should('be.visible')
      })
    })
  })

  it('Searches by last name', () => {
    cy.get('span').contains('Search By email').click()
    cy.get('li[role="option"][data-value="lastName"]').click()
    cy.filterUsersByLastName('m')
    cy.get('input[type="text"][placeholder="Search"]').type('m')
    cy.filterUsersByLastName('murithi')
    cy.get('input[type="text"][placeholder="Search"]').type('urithi')
    cy.get('table').within(() => {
      cy.get('tbody tr td:nth-child(2)').each(($cell) => {
        cy.wrap($cell).contains('murithi').should('be.visible')
      })
    })
  })

  it('Searches by phone number', () => {
    cy.get('span').contains('Search By email').click()
    cy.get('li[role="option"][data-value="phoneNumber"]').click()
    cy.filterUsersByPhoneNumber('7')
    cy.get('input[type="text"][placeholder="Search"]').type('7')
    cy.filterUsersByPhoneNumber('712293')
    cy.get('input[type="text"][placeholder="Search"]').type('12293')
    cy.get('table').within(() => {
      cy.get('tbody tr td:nth-child(4)').each(($cell) => {
        cy.wrap($cell).contains('712293').should('be.visible')
      })
    })
  })

  it('Shows the necessary filters', () => {
    cy.get('button').contains('Show Filters').click()
    cy.get('button').contains('Clear All').should('be.visible')
    cy.get('button').contains('Role').should('be.visible')
    cy.get('div[aria-labelledby="Status"]').should('be.visible')
    cy.get('input[value="Date created : From - To"]').should('be.visible')
    cy.get('input[value="Last Login : From - To"]').should('be.visible')
    cy.get('button').contains('Hide Filters').click()
    cy.get('button').contains('Clear All').should('not.be.visible')
  })

  it('Filters by role', () => {
    cy.get('button').contains('Show Filters').click()
    cy.get('button').contains('Role').click()
    cy.get('.MuiPaper-root input[placeholder="Search"]').type('x')
    cy.get('.MuiPaper-root li[role="menuitem"]').each(($cell) => {
      cy.wrap($cell).should('include.text', 'X')
    })
    cy.get('.MuiPaper-root li[role="menuitem"]').contains('X247 User').click()
    cy.filterUsersByRoles('9347ce3e-da04-4f65-819d-dc27e8159393')
    cy.get('button').contains('Apply').click()
    cy.get('table').within(() => {
      cy.get('tbody tr td:nth-child(5)')
        .contains('x247 User')
        .should('be.visible')
    })

    // Filters by more than 1 role
    cy.get('button').contains('Role').click()
    cy.get('.MuiPaper-root input[placeholder="Search"]')
      .clear()
      .type('testing 4')
    cy.get('.MuiPaper-root li[role="menuitem"]').contains('testing 4').click()
    cy.filterUsersByRoles([
      '9347ce3e-da04-4f65-819d-dc27e8159393',
      '4c5d4ee4-bc04-454f-8fcc-942fe69a93b3',
    ])
    cy.get('button').contains('Apply').click()
    cy.get('table').within(() => {
      cy.get('tbody tr td:nth-child(5)')
        .invoke('text')
        .should((text) => {
          expect(text).to.match(/x247 User|testing 4/)
        })
    })
    cy.get('button').contains('Clear All').click()
  })

  it('Filters by status', () => {
    cy.get('button').contains('Show Filters').click()
    cy.get('div[aria-labelledby="Status"]').click()
    cy.filterUsersByStatus('ACTIVE')
    cy.get('li[data-value="ACTIVE"]').click()
    cy.get('body').then(($body) => {
      if ($body.find('table').length === 0) {
        // If table is not present
        expect($body).to.contain('No user found')
      } else {
        // Table exists, check for "Active"
        cy.get('table').within(() => {
          cy.get('tbody tr td:nth-child(3)').each(($cell) => {
            cy.wrap($cell).contains('Active').should('exist')
          })
        })
      }
    })

    cy.get('div[aria-labelledby="Status"]').click()
    cy.filterUsersByStatus('INACTIVE')
    cy.get('li[data-value="INACTIVE"]').click()
    cy.get('body').then(($body) => {
      if ($body.find('table').length === 0) {
        // If table is not present
        expect($body).to.contain('No user found')
      } else {
        // Table exists, check for "Inactive"
        cy.get('table').within(() => {
          cy.get('tbody tr td:nth-child(3)').each(($cell) => {
            cy.wrap($cell).contains('Inactive').should('exist')
          })
        })
      }
    })

    cy.get('div[aria-labelledby="Status"]').click()
    cy.filterUsersByStatus('PENDING')
    cy.get('li[data-value="PENDING"]').click()
    cy.get('body').then(($body) => {
      if ($body.find('table').length === 0) {
        // If table is not present
        expect($body).to.contain('No user found')
      } else {
        // Table exists, check for "Pending"
        cy.get('table').within(() => {
          cy.get('tbody tr td:nth-child(3)').each(($cell) => {
            cy.wrap($cell).contains('Pending').should('exist')
          })
        })
      }
    })

    cy.get('div[aria-labelledby="Status"]').click()
    cy.filterUsersByStatus('DORMANT')
    cy.get('li[data-value="DORMANT"]').click()
    cy.get('body').then(($body) => {
      if ($body.find('table').length === 0) {
        // If table is not present
        expect($body).to.contain('No user found')
      } else {
        // Table exists, check for "Dormant"
        cy.get('table').within(() => {
          cy.get('tbody tr td:nth-child(3)').each(($cell) => {
            cy.wrap($cell).contains('Dormant').should('exist')
          })
        })
      }
    })
  })

  it('Filters by date created', () => {
    cy.get('button').contains('Show Filters').click()
    cy.get('input[value="Date created : From - To"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('div[role="rowgroup"] button').contains('1').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('div[role="rowgroup"] button').contains('1').click()
    cy.filterUsersByDateCreated()
    cy.get('button').contains('Apply').click()
    cy.get('body').then(($body) => {
      if ($body.find('table').length === 0) {
        // If table is not present
        expect($body).to.contain('No user found')
      } else {
        // Table exists, check for "2025"
        cy.get('table').within(() => {
          cy.get('tbody tr td:nth-child(6)').each(($cell) => {
            cy.wrap($cell).contains('2025').should('exist')
          })
        })
      }
    })
  })

  it('Filters by last login', () => {
    cy.get('button').contains('Show Filters').click()
    cy.get('input[value="Last Login : From - To"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('button[title="Previous month"]').click()
    cy.get('div[role="rowgroup"] button').contains('1').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('button[title="Next month"]').click()
    cy.get('div[role="rowgroup"] button').contains('1').click()
    cy.filterUsersByLastLogin()
    cy.get('button').contains('Apply').click()
    cy.get('body').then(($body) => {
      if ($body.find('table').length === 0) {
        // If table is not present
        expect($body).to.contain('No user found')
      } else {
        // Table exists, check for "2025"
        cy.get('table').within(() => {
          cy.get('tbody tr td:nth-child(7)').each(($cell) => {
            cy.wrap($cell).contains('2025').should('exist')
          })
        })
      }
    })
  })

  it('Filters by multiple filters', () => {
    cy.get('button').contains('Show Filters').click()
    cy.get('div[aria-labelledby="Status"]').click()
    cy.filterUsersByStatus('ACTIVE')
    cy.get('li[data-value="ACTIVE"]').click()
    cy.get('button').contains('Role').click()
    cy.get('.MuiPaper-root input[placeholder="Search"]').type('idam')
    cy.get('.MuiPaper-root li[role="menuitem"]').contains('IdAM Maker').click()
    cy.filterUsersByStatusAndRole()
    cy.get('button').contains('Apply').click()
    cy.get('body').then(($body) => {
      if ($body.find('table').length === 0) {
        // If table is not present
        expect($body).to.contain('No user found')
      } else {
        // Table exists, check for "Active" and "IdAM Maker"
        cy.get('table').within(() => {
          cy.get('tbody tr td:nth-child(3)').each(($cell) => {
            cy.wrap($cell).contains('Active').should('exist')
          })
          cy.get('tbody tr td:nth-child(5)').each(($cell) => {
            cy.wrap($cell).contains('IdAM Maker').should('exist')
          })
        })
      }
    })

    // Clears all filters
    cy.get('button').contains('Clear All').click()
  })

  it('Exports generated users report as Excel', () => {
    cy.get('button').contains('Export All').click()
    cy.get('input[value="excel"]').should('have.attr', 'checked')
    cy.exportUsersAsExcel()
    cy.get('div[aria-modal="true"] button').contains('Export').click()
    cy.wait('@exportUsersAsExcel').then((interception) => {
      const headers = interception.response?.headers

      // Basic response check
      expect(interception.response?.statusCode).to.eq(200)

      // Confirm content-type is application/zip
      expect(headers).to.have.property('content-type')
      expect(headers!['content-type']).to.include('application/zip')

      // Optionally check the content-length is non-zero
      expect(Number(headers!['content-length'])).to.be.greaterThan(0)
    })
  })
})
