describe('Rights management tests', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.fetchRoles()
    cy.fetchTypes()
    cy.fetchPermissions()
    cy.fetchFilteredPermissions()
    cy.fetchModules()
    cy.fetchChannelModules()
    cy.fetchApprovals()
    cy.fetchUsers()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('Access Management').should('be.visible').click()
    cy.contains('User Management').should('be.visible')
    cy.get('[aria-label="Rights"]').click()
    cy.contains('Rights Management').should('be.visible')
  })

  it('Searches by rights name', () => {
    cy.fetchSearchFilteredPermissions()
    cy.get('input[placeholder="Search"]').type('customer')
    cy.get('table').within(() => {
      cy.contains('View customers reports').should('be.visible')
      cy.contains('Reject update customer kyc validation record').should(
        'be.visible'
      )
    })

    // Shows all columns
    cy.get('table').within(() => {
      cy.get('thead').within(() => {
        cy.contains('Right').should('be.visible')
        cy.contains('Is Visible').should('be.visible')
        cy.contains('Module').should('be.visible')
        cy.contains('Roles Assigned').should('be.visible')
      })
    })
  })

  it('Selects a row', () => {
    cy.get('table').within(() => {
      cy.get('tbody > tr:first-of-type')
        .should('have.attr', 'role', 'checkbox')
        .click()
      cy.get('tbody > tr:first-of-type').should(
        'have.attr',
        'aria-checked',
        'true'
      )
      cy.get('tbody > tr:first-of-type').click()
      cy.get('tbody > tr:first-of-type').should(
        'have.attr',
        'aria-checked',
        'false'
      )
    })
  })

  it('Navigates to next and previous table pages', () => {
    cy.get('button').contains('previous').should('be.disabled')
    cy.fetchPaginatedPermissions()
    cy.get('button').contains('next').click()
    cy.get('tbody > tr:first-of-type')
      .contains('View transaction reports')
      .should('not.exist')
    cy.get('button').contains('previous').should('be.enabled').click()
    cy.get('tbody > tr:first-of-type')
      .contains('View transaction reports')
      .should('exist')
  })

  it('Filters by isVisible and module', () => {
    // Shows and hides the filters
    cy.get('button').contains('Clear All').should('not.be.visible')
    cy.get('button').contains('Show Filters').click()
    cy.get('button').contains('Clear All').should('be.visible')
    cy.get('button').contains('Hide Filters').click()
    cy.get('button').contains('Clear All').should('not.be.visible')

    // Filters by isVisible
    cy.get('button').contains('Show Filters').click()
    cy.get('[aria-labelledby="Is Visible"]').click()
    cy.fetchNotVisiblePermissions()
    cy.get('[data-value="no"]').click()
    cy.get('tbody').within(() => {
      cy.get('tr').should('have.length', 0)
    })
    cy.get('[data-value="yes"]').click()
    cy.get('tbody').within(() => {
      cy.get('tr').should('not.have.length', 0)
    })

    // Filters by Module
    cy.fetchX247Permissions()
    cy.get('[aria-labelledby="Module"]').click()
    cy.get('[data-value="X247 Reports"]').click()
    cy.get('tbody').within(() => {
      cy.get('tr td:nth-child(4)').each(($cell) => {
        cy.wrap($cell).should('have.text', 'X247 Reports')
      })
    })

    // Clears all filters
    cy.get('button').contains('Clear All').click()
    cy.get('button').contains('Clear All').should('not.be.visible')
  })
})
