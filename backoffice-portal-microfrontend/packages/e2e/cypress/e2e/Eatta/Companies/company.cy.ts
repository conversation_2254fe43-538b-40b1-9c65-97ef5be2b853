describe('Eatta admin Onboarding company flow', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.refreshToken()
    cy.fetchChannelModules()
    cy.uploadCatalogue()
    cy.fetchCatalogue()
    cy.fetchAuctions()
    cy.fetchCompanies()
    cy.createCompanyPage()
    cy.createPaymentDetailsPage()
    cy.fetchBanks()
    cy.fetchBranches()
    cy.addTeamMembers()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('p', 'Settlement Engine')
      .should('exist')
      .scrollIntoView()
      .should('be.visible')
      .click()
  })
  it('Eatta admin views a list of all companies', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/companies')
  })

  context('Eatta admin Onboarding company flow', () => {
    context('payment details using DTB Account', () => {
      it('Eatta admin onboards a new company as a broker and navigates to payment details using DTB', () => {
        cy.visit('http://localhost:3000/eatta/backoffice/companies')
        cy.contains('button', 'New Company').should('be.visible').click()

        // Select Company Type
        cy.get('button[aria-label="Open"]').click()
        cy.contains('li', 'Broker').click()

        // Enter EATTA Membership Code
        cy.get('input[name="code"]').type('EATTA001')

        // Enter EATTA Entry Date
        cy.get('input[placeholder="MM/DD/YYYY"]').type('01/01/2024')

        // Enter Company Name
        cy.get('input[name="name"]').type('John Doe Company Ltd')

        // Enter KRA PIN
        cy.get('input[name="kraPin"]').type('A123456789B')

        // Enter Phone Number
        cy.get('input[name="phoneNumber"]').type('*********')

        // Enter Email Address
        cy.get('input[name="emailAddress"]').type('<EMAIL>')

        // Enter Company License Number
        cy.get('input[name="licenseNumber"]').type('TBK/2024/001')

        // Submit the form
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()

        //////////payment details page
        cy.get('.MuiTypography-h6')
          .contains('Payment Details')
          .should('be.visible')

        // Select Bank
        cy.get('#bankCode').clear().type('DIAMOND TRUST BANK')
        cy.contains('li', 'DIAMOND TRUST BANK').click()

        // Enter Bank Account Number
        cy.get('input[name="accountNumber"]').type('**********')

        // Enter Bank Account Currency
        cy.get('#accountCurrency').clear().type('United States Dollar')
        cy.contains('li', 'United States Dollar').should('be.visible').click()

        // Enter channel code
        cy.get('#channel').clear().type('IFT')
        cy.contains('li', 'IFT').should('be.visible').click()

        // Enter branch name
        cy.get('#bankBranchName').clear().type('DTB Centre Branch')
        cy.contains('li', 'DTB Centre Branch').should('be.visible').click()

        // Enter Schedule
        cy.get('#settlementMode').clear().type('INSTANT')
        cy.contains('li', 'INSTANT').should('be.visible').click()

        // Enter EATTA Entry Date
        cy.get('input[placeholder="MM/DD/YYYY"]').type('01/01/2024')

        // Submit the form
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()

        //////////Add team members page

        // Type into the First Name field
        cy.get('input[name="firstname"]').clear().type('John')

        // Type into the Last Name field
        cy.get('input[name="lastname"]').clear().type('Doe')

        // Type into the Username field
        cy.get('input[name="username"]').clear().type('johndoe1')

        // Type into the Email field
        cy.get('input[name="email"]').clear().type('<EMAIL>')

        // Type into the Phone Number field
        cy.get('input[name="phoneNumber"]').type('*********')

        // Type into the Password field
        cy.get('input[name="password"]').clear().type('Password123!') 

        // Type into the Confirm Password field
        cy.get('input[name="confirm"]').clear().type('Password123!')

        // Submit the form (if there's a submit button)
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()

        //////////complete onboarding page
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()
      })
    })

    context('payment details using other banks', () => {
      it('Eatta admin onboards a new company as a broker and navigates to payment details using DTB', () => {
        cy.visit('http://localhost:3000/eatta/backoffice/companies')
        cy.contains('button', 'New Company').should('be.visible').click()

        // Select Company Type
        cy.get('button[aria-label="Open"]').click()
        cy.contains('li', 'Broker').click()

        // Enter EATTA Membership Code
        cy.get('input[name="code"]').type('EATTA001')

        // Enter EATTA Entry Date
        cy.get('input[placeholder="MM/DD/YYYY"]').type('01/01/2024')

        // Enter Company Name
        cy.get('input[name="name"]').type('John Doe Company Ltd')

        // Enter KRA PIN
        cy.get('input[name="kraPin"]').type('A123456789B')

        // Enter Phone Number
        cy.get('input[name="phoneNumber"]').type('*********')

        // Enter Email Address
        cy.get('input[name="emailAddress"]').type('<EMAIL>')

        // Enter Company License Number
        cy.get('input[name="licenseNumber"]').type('TBK/2024/001')

        // Submit the form
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()

        //////////payment details page
        cy.get('.MuiTypography-h6')
          .contains('Payment Details')
          .should('be.visible')

        // Select Bank
        cy.get('#bankCode').clear().type('KENYA COMMERCIAL BANK LTD')
        cy.contains('li', 'KENYA COMMERCIAL BANK LTD').click()

        // Enter Bank Account Number
        cy.get('input[name="accountNumber"]').type('**********')

        // Enter Bank Account Currency
        cy.get('#accountCurrency').clear().type('United States Dollar')
        cy.contains('li', 'United States Dollar').should('be.visible').click()

        // Enter channel code
        cy.get('#channel').clear().type('RTGS')
        cy.contains('li', 'RTGS').should('be.visible').click()

        // Enter Schedule
        cy.get('#settlementMode').clear().type('INSTANT')
        cy.contains('li', 'INSTANT').should('be.visible').click()

        // Enter EATTA Entry Date
        cy.get('input[placeholder="MM/DD/YYYY"]').type('01/01/2024')

        // Submit the form
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()

        //////////Add team members page

        // Type into the First Name field
        cy.get('input[name="firstname"]').clear().type('John')

        // Type into the Last Name field
        cy.get('input[name="lastname"]').clear().type('Doe')

        // Type into the Username field
        cy.get('input[name="username"]').clear().type('johndoe2')

        // Type into the Email field
        cy.get('input[name="email"]').clear().type('<EMAIL>')

        // Type into the Phone Number field
        cy.get('input[name="phoneNumber"]').type('*********')

        // Type into the Password field
        cy.get('input[name="password"]').clear().type('Password123!') //please note new password is johndoe2@123!

        // Type into the Confirm Password field
        cy.get('input[name="confirm"]').clear().type('Password123!')

        // Submit the form (if there's a submit button)
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()

        //////////complete onboarding page
        cy.get('button.MuiButton-contained').should('not.be.disabled').click()
      })
    })
  })
})
