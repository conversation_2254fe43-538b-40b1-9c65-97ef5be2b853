describe('Eatta admin Uploads Post-Auction Catalogue', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.fetchChannelModules()
    cy.uploadCatalogue()
    cy.fetchAuctions()
    cy.fetchCatalogue()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('p', 'Settlement Engine')
      .should('exist')
      .scrollIntoView()
      .should('be.visible')
      .click()
  })
  it('Uploads Post-Auction Catalogue and view uploaded _catalogue.', () => {
    cy.contains('button', 'Upload New Catalogue')
      .get('button.MuiButton-outlinedSecondary')
      .should('be.visible')
      .click()
    cy.visit('http://localhost:3000/eatta/backoffice/post-auction/upload')
    cy.contains('button', 'Upload New Catalogue').should('be.visible').click()
    cy.get("[data-testid='CalendarIcon']").click()
    cy.get('div:nth-of-type(2) > button:nth-of-type(4)').click()
    cy.get('div.mui-1im5dit-MuiStack-root').should('be.visible').click()

    const postAuctionData =
      'Lot No,Package,Grade,Net Weight,Price\n' +
      '1,PKG001,A,100,500\n' +
      '2,PKG002,B,150,750\n' +
      '3,PKG003,A,200,1000'

    cy.window().then((win) => {
      const blob = new win.Blob([postAuctionData], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const testFile = new win.File([blob], 'PostAuctionData.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const dataTransfer = new win.DataTransfer()
      dataTransfer.items.add(testFile)
      cy.get('input[type="file"]', { timeout: 5000 })
        .should('exist')
        .then(($input) => {
          const input = $input[0] as HTMLInputElement
          input.files = dataTransfer.files
          cy.wrap($input).trigger('change', { force: true })
        })
    })
    cy.get('button.MuiButton-containedPrimary.MuiButton-contained')
      .contains('Upload Catalogue')
      .should('be.visible')
      .should('be.enabled')
      .click()
  })
})
