describe('Payment Management Flow', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.refreshToken()
    cy.fetchChannelModules()
    cy.fetchCatalogue()
    cy.fetchAuctions()
    cy.fetchPayments()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('p', 'Settlement Engine')
      .should('exist')
      .scrollIntoView()
      .should('be.visible')
      .click()
  })
  it('Eatta admin views a list of all payments', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/payments')
  })
})
