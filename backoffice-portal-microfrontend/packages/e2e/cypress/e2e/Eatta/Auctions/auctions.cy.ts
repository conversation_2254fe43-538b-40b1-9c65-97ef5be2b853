describe('Eatta admin views a list of all auctions', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.refreshToken()
    cy.fetchChannelModules()
    cy.uploadCatalogue()
    cy.fetchCatalogue()
    cy.fetchAuctions()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('p', 'Settlement Engine')
      .should('exist')
      .scrollIntoView()
      .should('be.visible')
      .click()
  })
  it('fetches all auctions and navigates to individual auction view.', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/post-auction')
    cy.get('button:has(svg[data-testid="ArrowForwardIcon"])')
      .first()
      .should('be.visible')
      .click()
  })
})
