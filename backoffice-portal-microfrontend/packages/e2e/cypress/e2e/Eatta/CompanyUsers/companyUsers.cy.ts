describe('Company Users Management Flow', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.refreshToken()
    cy.fetchChannelModules()
    cy.uploadCatalogue()
    cy.fetchCatalogue()
    cy.fetchAuctions()
    cy.fetchCompanies()
    cy.fetchCompanyUsers()
    cy.registerCompanyUser()
    cy.editCompanyUser()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('p', 'Settlement Engine')
      .should('exist')
      .scrollIntoView()
      .should('be.visible')
      .click()
  })
  it('Eatta admin views a list of all company users', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/companies')
    cy.get('tr:nth-of-type(1) > td:nth-of-type(7) svg').click()
    cy.get('li:nth-of-type(1)').click()
    cy.get('div.mui-2gstrh-MuiStack-root').click()
  })
  it('Eatta admin edits a company user', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/companies')
    cy.get('tr:nth-of-type(1) > td:nth-of-type(7) svg').click()
    cy.get('li:nth-of-type(1)').click()
    cy.get('div.mui-2gstrh-MuiStack-root').click()
    cy.get('div.mui-2gstrh-MuiStack-root > div:nth-of-type(1) svg').click({
      force: true,
    })

    // Type into the First Name field
    cy.get('input[name="firstname"]').clear().type('John')

    // Type into the Last Name field
    cy.get('input[name="lastname"]').clear().type('Jones')

    // Type into the Username field
    cy.get('input[name="username"]').clear().type('JJones')

    // Type into the Email field
    cy.get('input[name="email"]').clear().type('<EMAIL>')

    // Type into the Phone Number field
    cy.get('input[name="phoneNumber"]').type('*********')

    // Submit the form (if there's a submit button)
    cy.contains('button', 'Save changes').click()
  })
  context('Eatta admin creates a new company user', () => {
    context('Eatta admin creates a new company user from company list', () => {
      it('Eatta admin creates a new company user', () => {
        cy.visit('http://localhost:3000/eatta/backoffice/companies')
        cy.get('tr:nth-of-type(1) > td:nth-of-type(7) svg').click()
        cy.get('li:nth-of-type(2)').click()

        // Type into the First Name field
        cy.get('input[name="firstname"]').clear().type('John')

        // Type into the Last Name field
        cy.get('input[name="lastname"]').clear().type('Doe')

        // Type into the Username field
        cy.get('input[name="username"]').clear().type('johndoe123')

        // Type into the Email field
        cy.get('input[name="email"]').clear().type('<EMAIL>')

        // Type into the Phone Number field
        cy.get('input[name="phoneNumber"]').type('*********')

        // Type into the Password field
        cy.get('input[name="password"]').clear().type('Password123!')

        // Type into the Confirm Password field
        cy.get('input[name="confirm"]').clear().type('Password123!')

        // Submit the form (if there's a submit button)
        cy.get('div.MuiDialog-root button.MuiButton-contained').click()
      })
    })
    context(
      'Eatta admin creates a new company user from company users page',
      () => {
        it('Eatta admin creates a new company user', () => {
          cy.visit('http://localhost:3000/eatta/backoffice/companies')
          cy.get('tr:nth-of-type(1) > td:nth-of-type(7) svg').click()
          cy.get('li:nth-of-type(1)').click()
          cy.get('div.mui-1rvt0xq-MuiStack-root > button').click()

          // Type into the First Name field
          cy.get('input[name="firstname"]').clear().type('John')

          // Type into the Last Name field
          cy.get('input[name="lastname"]').clear().type('Doe')

          // Type into the Username field
          cy.get('input[name="username"]').clear().type('johndoe123')

          // Type into the Email field
          cy.get('input[name="email"]').clear().type('<EMAIL>')

          // Type into the Phone Number field
          cy.get('input[name="phoneNumber"]').type('*********')

          // Type into the Password field
          cy.get('input[name="password"]').clear().type('Password123!')

          // Type into the Confirm Password field
          cy.get('input[name="confirm"]').clear().type('Password123!')

          // Submit the form (if there's a submit button)
          cy.get('div.MuiDialog-root button.MuiButton-contained').click()
        })
      }
    )
  })
})
