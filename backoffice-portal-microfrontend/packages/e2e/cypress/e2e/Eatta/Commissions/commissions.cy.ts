describe('Eatta admin Broker Commissions Flow', () => {
  beforeEach(() => {
    cy.tokenKey()
    cy.setToken()
    cy.refreshToken()
    cy.refreshToken()
    cy.fetchChannelModules()
    cy.fetchCatalogue()
    cy.fetchAuctions()
    cy.fetchCommissions()
    cy.setCommissions()
    cy.activateCommission()
    cy.visit(
      'http://localhost:3000/authenticate/?accessToken=%7B%22statusCode%22%3A200%2C%22success%22%3Atrue%2C%22userName%22%3A%22jdoe%40dtbafrica.com%22%2C%22statusMessage%22%3A%22Login+successful%22%2C%22accessToken%22%3A%22sgiasCVK2q7eH2Wh%22%2C%22refreshToken%22%3A%224g928hDueB-Iun1W%22%2C%22expiresIn%22%3A120%7D'
    )
    cy.contains('p', 'Settlement Engine')
      .should('exist')
      .scrollIntoView()
      .should('be.visible')
      .click()
  })
  it('Eatta admin views a list of all commissions', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/settings')
  })
  it('Eatta admin sets the commissions for broker and producer', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/settings')

    // Enter Broker-Buyer Commission
    cy.get('input[name="buyerCommission"]')
      .should('be.visible')
      .clear()
      .type('5')

    // Enter Broker-Producer Commission
    cy.get('input[name="producerCommission"]')
      .should('be.visible')
      .clear()
      .type('2')

    // Submit the form
    cy.get('button.MuiButton-outlined')
      .contains('Apply')
      .should('not.be.disabled')
      .click()
  })
  it('Eatta admin Activates broker commission', () => {
    cy.visit('http://localhost:3000/eatta/backoffice/settings')

    cy.get('tbody > tr:nth-of-type(1) > td:nth-of-type(6) > button')
      .should('be.visible')
      .click()

    cy.get('li:nth-of-type(1)').click()
  })
})
