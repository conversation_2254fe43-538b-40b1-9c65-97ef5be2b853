import env from '../../../../fixtures/eattaClient/env.json'
import credentials from '../../../../fixtures/eattaClient/setBuyerCredentials.json'
import otp from '../../../../fixtures/eattaClient/deliverMode.json'

function loginViaEattaAuth(
  organizationCode: string,
  username: string,
  password: string
) {
  cy.visit(`${env.BASE_URL}`)

  cy.get('input[type="organizationCode"]').type(organizationCode, {
    log: false,
  })
  cy.get('input[type="username"]').type(username, {
    log: false,
  })
  cy.get('input[type="password"]').should('be.visible').type(password, {
    log: false,
  })
  cy.get('button.MuiButton-contained').should('not.be.disabled').click()
}

Cypress.Commands.add(
  'loginToEATTA',
  (organizationCode: string, username: string, password: string) => {
    const log = Cypress.log({
      displayName: 'Eatta Auth Login',
      message: [`🔐 Authenticating | ${username}`],
      autoEnd: false,
    })
    log.snapshot('before')

    loginViaEattaAuth(organizationCode, username, password)

    log.snapshot('after')
    log.end()
  }
)

describe('Eatta buyer Transaction History Flow', () => {
  beforeEach(() => {
    cy.userInfo()
    cy.generateOtp()
    cy.login()
    cy.verifyBuyerLogin()
    cy.forgotPassword()
    cy.fetchAuctions()
    cy.fetchCheckoutLots()
    cy.fetchBuyerInvoices()
    cy.fetchBuyerLotsInvoiced()
    cy.fetchBuyerTransactionHistory()
  })

  it('fetches all auctions and navigates to checkout page', () => {
    //login
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify
    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/transaction-history')

    
  })
})
