import env from '../../../../fixtures/eattaClient/env.json'
import credentials from '../../../../fixtures/eattaClient/setPartnerCredentials.json'
import otp from '../../../../fixtures/eattaClient/deliverMode.json'

function loginViaEattaAuth(
  organizationCode: string,
  username: string,
  password: string
) {
  cy.visit(`${env.BASE_URL}`)

  cy.get('input[type="organizationCode"]').type(organizationCode, {
    log: false,
  })
  cy.get('input[type="username"]').type(username, {
    log: false,
  })
  cy.get('input[type="password"]').should('be.visible').type(password, {
    log: false,
  })
  cy.get('button.MuiButton-contained').should('not.be.disabled').click()
}

Cypress.Commands.add(
  'loginToEATTA',
  (organizationCode: string, username: string, password: string) => {
    const log = Cypress.log({
      displayName: 'Eatta Auth Login',
      message: [`🔐 Authenticating | ${username}`],
      autoEnd: false,
    })
    log.snapshot('before')

    loginViaEattaAuth(organizationCode, username, password)

    log.snapshot('after')
    log.end()
  }
)

describe('The Login Page (PARTNER)', () => {
  beforeEach(() => {
    cy.userInfo()
    cy.generateOtp()
    cy.login()
    cy.verifyPartnerLogin()
    cy.fetchInsights()
    cy.forgotPassword()
    cy.ChangePassword()
  })

  it('logs in to the Eatta client via Eatta client Auth (PARTNER)', () => {
    //login
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify
    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/insights')
  })
  it('Forgot password', () => {
    cy.visit(`${env.BASE_URL}`)

    cy.get('form a').click()
    cy.location('href').should(
      'eq',
      'http://localhost:3004/auth/forgot-password'
    )

    cy.get('input[name="email"]').type('<EMAIL>')

    cy.get('button').click()
  })
  it('log out an Eatta user', () => {
    //login
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify
    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All insights

    cy.visit('http://localhost:3004/insights')

    //logout
    cy.get('button[aria-label="Log Out"]').click({ force: true })

    cy.contains('button', "Yes, I'm sure").click({ force: true })
  })
  it('Change Password for an Eatta user', () => { 
    //login
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify
    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/insights')

   //change password
    //organizationCode = EATTA001
    //username = johndoe2
    //password = changed password from Password@123! to johndoe2@123!
    cy.get('button[aria-label="Change Password"]').click({ force: true })

    cy.get('input[name="oldpassword"]').clear().type('Password@123!')

    cy.get('input[name="newpassword"]').clear().type('johndoe2@123!')

    cy.get('input[name="confirmpassword"]').clear().type('johndoe2@123!')

    cy.contains('button', "Next")
      .should('not.be.disabled')
      .click({ force: true })
  })
})
