import env from '../../../../fixtures/eattaClient/env.json'
import credentials from '../../../../fixtures/eattaClient/setBrokerCredentials.json'
import otp from '../../../../fixtures/eattaClient/deliverMode.json'

describe('Eatta broker Invoice Management Flow', () => {
  beforeEach(() => {
    cy.userInfo()
    cy.generateOtp()
    cy.login()
    cy.verifyBrokerLogin()
    cy.fetchAuctions()
    cy.fetchCatalogue()
    cy.fetchBrokerInvoiceEntries()
    cy.fetchBrokerTransactionHistory()
    cy.uploadInvoice()
    cy.processInvoice()
    cy.sendBrokerInvoice()
  })
  it('Eatta broker views a list of all invoice entries', () => {
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify

    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/sales')

    //fetch Alll invoices

    cy.visit('http://localhost:3004/invoices')
  })
  it('Eatta broker uploads invoice', () => {
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify

    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/sales')

    cy.get('button:has(svg[data-testid="ArrowForwardIcon"])')
      .first()
      .should('be.visible')
      .click()

    cy.contains('button', 'Upload New Invoice')
      .should('be.visible')
      .and('not.be.disabled')
      .click()

    cy.visit('http://localhost:3004/invoices/upload')

    cy.get('.MuiInputBase-input').eq(0).clear().type('123/1234')

    cy.get('input[placeholder="MM/DD/YYYY"]')
      .should('be.visible')
      .first()
      .clear()
      .type('01/01/2024{enter}')

    const InvoiceData =
      'Lot No,Package,Grade,Net Weight,Price\n' +
      '12539,40,PD,3000,2.58\n' +
      '12296,40,PF1,2712,2.00\n' +
      '12297,40,PF1,2712,2.00\n' +
      '12163,40,PF1,2712,2.00\n' +
      '12252,40,PD,3000,2.58\n' +
      '12253,40,PD,3000,2.58\n' +
      '12254,40,PD,3000,2.58\n' +
      '12255,40,PD,3000,2.58\n' +
      '12256,40,PD,3000,2.58'

    cy.window().then((win) => {
      const blob = new win.Blob([InvoiceData], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const testFile = new win.File([blob], 'InvoiceData.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      const dataTransfer = new win.DataTransfer()
      dataTransfer.items.add(testFile)
      cy.get('input[type="file"]', { timeout: 5000 })
        .should('exist')
        .then(($input) => {
          const input = $input[0] as HTMLInputElement
          input.files = dataTransfer.files
          cy.wrap($input).trigger('change', { force: true })
        })
    })
    cy.get('button.mui-hea1ac-MuiButtonBase-root-MuiButton-root').click()
  })
  it('Eatta broker processes invoice (Alternative journey)', () => {
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify

    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/sales')

    cy.get('button:has(svg[data-testid="ArrowForwardIcon"])')
      .first()
      .should('be.visible')
      .click()

    cy.get('.MuiCheckbox-root input[type="checkbox"]')
    .eq(2)
      .click({ force: true })

    cy.contains('button', 'Process Invoice')
      .should('be.visible')
      .and('not.be.disabled')
      .click()

    cy.get('input[placeholder="MM/DD/YYYY"]')
      .should('be.visible')
      .first()
      .clear()
      .type('01/01/2024{enter}')

    cy.get('input[placeholder="Enter Invoice number"]').clear().type('123/1234')

    cy.contains('button', 'Confirm Invoice Amount').click({ force: true })

    cy.contains('button', 'Send Invoice to Buyer').click({ force: true })
  })
})
