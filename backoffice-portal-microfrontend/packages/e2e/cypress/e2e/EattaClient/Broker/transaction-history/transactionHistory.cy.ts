import env from '../../../../fixtures/eattaClient/env.json'
import credentials from '../../../../fixtures/eattaClient/setBrokerCredentials.json'
import otp from '../../../../fixtures/eattaClient/deliverMode.json'

describe('Eatta broker Transaction History Flow', () => {
  beforeEach(() => {
    cy.userInfo()
    cy.generateOtp()
    cy.login()
    cy.verifyBrokerLogin()
    cy.fetchAuctions()
    cy.fetchBrokerInvoiceEntries()
    cy.fetchBrokerInvoices()
    cy.fetchBrokerTransactionHistory()
    cy.fetchBrokerLotsInvoiced()
  })
  it('Eatta broker views a list of all invoices', () => {
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify

    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/sales')

    //fetch Alll invoices

    cy.visit('http://localhost:3004/transaction-history')
  })
  it('Eatta broker views a list of lots invoiced', () => {
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify

    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/sales')

    //fetch Alll invoices

    cy.visit('http://localhost:3004/transaction-history')

    cy.get('tbody > tr:nth-of-type(1) button').should('be.visible').click()
  })
  it('Eatta broker views a list of transaction history per invoice', () => {
    cy.visit(`${env.BASE_URL}`)

    cy.get('input[name="organizationCode"]').type(
      credentials.organizationCode,
      {
        log: true,
      }
    )
    cy.get('input[name="username"]').type(credentials.username, {
      log: true,
    })
    cy.get('input[name="password"]')
      .should('be.visible')
      .type(credentials.password, {
        log: true,
      })
    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //verify

    cy.visit('http://localhost:3004/auth/verify')

    cy.get('div > div > div > div:nth-of-type(1) > div > span').click()

    cy.get('button.MuiButton-contained').should('not.be.disabled').click()

    //confirm
    cy.visit('http://localhost:3004/auth/confirm')

    cy.get('input:nth-of-type(1)').type(otp.otp, { log: true })

    cy.get('button.MuiButton-contained').click({ force: true })

    //successful login and fetch All Auctions

    cy.visit('http://localhost:3004/sales')

    //fetch Alll invoices

    cy.visit('http://localhost:3004/transaction-history')

    cy.get('tbody > tr:nth-of-type(1) button').should('be.visible').click()

    cy.get('p.mui-1onjaf6-MuiTypography-root').click()
  })
})
