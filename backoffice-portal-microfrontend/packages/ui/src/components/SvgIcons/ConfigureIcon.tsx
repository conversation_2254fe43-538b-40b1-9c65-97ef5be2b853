import type { IconsProps } from './IconTypes'

export const ConfigureIcon = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#000000',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M8.41747 3.33329C9.46959 2.3023 10.9105 1.66663 12.5 1.66663C15.7216 1.66663 18.3333 4.2783 18.3333 7.49996C18.3333 9.08941 17.6976 10.5304 16.6666 11.5825M13.3333 12.5C13.3333 15.7216 10.7216 18.3333 7.49996 18.3333C4.2783 18.3333 1.66663 15.7216 1.66663 12.5C1.66663 9.2783 4.2783 6.66663 7.49996 6.66663C10.7216 6.66663 13.3333 9.2783 13.3333 12.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
