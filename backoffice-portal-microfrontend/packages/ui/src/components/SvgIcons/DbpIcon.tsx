import type { IconsProps } from './IconTypes'

export const X247Icon = ({
  width = '39',
  height = '30',
  fill = 'none',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 39 30"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M24.7514 8.99576L23.2581 10.7626C23.2581 10.7626 23.121 10.9004 23.3774 10.9004H24.8892C24.8892 10.9004 25.6675 10.8582 25.7532 11.4698C25.7532 11.4698 25.8296 11.8256 25.4777 12.2356L22.257 16.1838C22.257 16.1838 22.002 16.4593 22.002 16.6946V17.7352C22.002 17.7352 21.9723 17.9711 22.1786 17.9711H26.7154C26.7154 17.9711 26.8926 17.9599 26.8926 18.1477V21.7078C26.8926 21.7078 26.8781 21.9503 27.0495 21.7869L28.7386 19.705C28.7386 19.705 28.8763 19.5871 28.8763 19.3709V18.128C28.8763 18.128 28.8678 17.9711 28.993 17.9711H29.9776C29.9776 17.9711 30.1206 17.9599 30.1153 17.8136V16.2827C30.1153 16.2827 30.11 16.083 29.9393 16.1054H29.0727C29.0727 16.1054 28.8954 16.1067 28.8954 15.909V12.6389C28.8954 12.6389 28.9152 12.3825 28.7188 12.6389L27.0106 14.6891C27.0106 14.6891 26.9315 14.7682 26.8926 14.9646V15.9479C26.8926 15.9479 26.8841 16.1245 26.7154 16.1245H25.0217C25.0217 16.1245 24.8286 16.1258 25.0019 15.9288C25.1673 15.7409 26.9309 13.611 26.9309 13.611C26.9309 13.611 27.9135 12.3146 27.8937 11.1752C27.8937 11.1752 27.9385 8.82507 24.9472 8.91667C24.9472 8.91667 24.8549 8.89954 24.7514 8.99576Z"
        fill="#EB0045"
      />
      <path
        d="M31.9804 17.9702H30.7829C30.7829 17.9702 30.6399 17.959 30.6452 17.8127V16.2818C30.6452 16.2818 30.6498 16.0821 30.8212 16.1045H34.8386C34.8386 16.1045 35.0291 16.0656 35.0146 16.2712L34.896 17.7883C34.896 17.7883 34.8735 18.0381 34.72 18.2009L30.3592 23.6359C30.3592 23.6359 30.1832 23.8422 30.0652 23.8422H27.6631C27.6631 23.8422 27.5168 23.827 27.6492 23.6504L32.097 18.0829C32.0977 18.0829 32.157 17.9702 31.9804 17.9702Z"
        fill="#EB0045"
      />
      <path
        d="M29.7738 1.19727L29.2268 1.88661L27.9997 3.42941L27.344 4.25056L25.9192 6.04774L19.6953 13.8711L22.6635 20.7171H16.1569L14.7697 18.0843L10.1406 21.0144L15.2086 15.4515L15.2369 15.5035L17.1007 19.1743H20.2284L17.6622 13.6352L17.5488 13.39L17.676 13.2622L19.8277 11.1203L24.5457 6.41153L26.6975 4.26901L27.5562 3.4103L29.1036 1.8675L29.7738 1.19727Z"
        fill="#EB0045"
      />
      <path
        d="M21.4226 6.04688L20.5968 6.95304L19.771 7.85921L17.3787 10.492L16.3546 11.615L15.6804 10.2792L14.4678 7.89216H11.3348L14.0144 13.6719L1.79395 25.864L11.8725 13.1954L8.89963 6.34937H15.4062L16.4633 8.34953L16.7981 8.9822L18.8365 7.68918L21.4226 6.04688Z"
        fill="#EB0045"
      />
      <path
        d="M29.8403 1.71484C33.8499 4.14535 36.5466 8.53319 36.5894 13.5662C36.6554 21.2927 30.4453 27.6102 22.7188 27.6768C17.6858 27.7196 13.2525 25.0986 10.7534 21.1312C13.1925 25.5553 17.9191 28.5348 23.3251 28.4887C31.1722 28.4215 37.4785 22.0058 37.4113 14.1587C37.3658 8.75265 34.3059 4.07813 29.8403 1.71484Z"
        fill="#EB0045"
      />
    </svg>
  )
}
