import type { IconsProps } from './IconTypes'

export const KeyIcon = ({
  width = '26',
  height = '26',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.73333',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 26 26"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="key">
        <path
          id="Icon"
          d="M16.7915 8.1224L20.5831 4.33073M22.7498 2.16406L20.5831 4.33073L22.7498 2.16406ZM12.339 12.5749C12.8983 13.1268 13.343 13.7839 13.6474 14.5084C13.9517 15.2329 14.1098 16.0104 14.1124 16.7962C14.1151 17.5821 13.9622 18.3606 13.6627 19.0871C13.3632 19.8136 12.923 20.4737 12.3673 21.0294C11.8116 21.585 11.1516 22.0253 10.4251 22.3248C9.69855 22.6243 8.91998 22.7771 8.13416 22.7745C7.34835 22.7719 6.57081 22.6138 5.84633 22.3095C5.12185 22.0051 4.46473 21.5604 3.91281 21.0011C2.82745 19.8773 2.22688 18.3722 2.24046 16.81C2.25404 15.2477 2.88067 13.7533 3.98539 12.6486C5.09011 11.5438 6.58454 10.9172 8.14679 10.9036C9.70905 10.8901 11.2141 11.4906 12.3379 12.576L12.339 12.5749ZM12.339 12.5749L16.7915 8.1224L12.339 12.5749ZM16.7915 8.1224L20.0415 11.3724L23.8331 7.58073L20.5831 4.33073L16.7915 8.1224Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export const CheckIcon = ({
  width = '19',
  height = '19',
  fill = 'none',
  stroke = '#ABEFC6',
  strokeWidth = '0.863636',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 19 19"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M9.5 18.5682C4.49178 18.5682 0.431818 14.5082 0.431818 9.5C0.431818 4.49178 4.49178 0.431818 9.5 0.431818C14.5082 0.431818 18.5682 4.49178 18.5682 9.5C18.5682 14.5082 14.5082 18.5682 9.5 18.5682Z"
        fill="#ECFDF3"
      />
      <path
        d="M9.5 18.5682C4.49178 18.5682 0.431818 14.5082 0.431818 9.5C0.431818 4.49178 4.49178 0.431818 9.5 0.431818C14.5082 0.431818 18.5682 4.49178 18.5682 9.5C18.5682 14.5082 14.5082 18.5682 9.5 18.5682Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <path
        d="M12.9545 6.90625L8.2045 11.6562L6.04541 9.49716"
        stroke="#027A48"
        strokeWidth="1.29545"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const LockIcon = ({
  width = '26',
  height = '26',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.73333',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 26 26"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="lock">
        <path
          id="Icon"
          d="M7.58333 11.9141V7.58073C7.58333 6.14414 8.15402 4.76639 9.16984 3.75057C10.1857 2.73475 11.5634 2.16406 13 2.16406C14.4366 2.16406 15.8143 2.73475 16.8302 3.75057C17.846 4.76639 18.4167 6.14414 18.4167 7.58073V11.9141M5.41667 11.9141H20.5833C21.78 11.9141 22.75 12.8841 22.75 14.0807V21.6641C22.75 22.8607 21.78 23.8307 20.5833 23.8307H5.41667C4.22005 23.8307 3.25 22.8607 3.25 21.6641V14.0807C3.25 12.8841 4.22005 11.9141 5.41667 11.9141Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export const XIcon = ({
  width = '11',
  height = '11',
  fill = 'none',
  stroke = '#F13C57',
  strokeWidth = '1.29545',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 11 11"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="x">
        <path
          id="Icon"
          d="M7.65949 3.34375L3.34131 7.66193M3.34131 3.34375L7.65949 7.66193"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}
