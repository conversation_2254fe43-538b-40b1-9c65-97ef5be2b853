export const ChargeConfigIcon = () => {
  return (
    <svg
      width="160"
      height="120"
      viewBox="0 0 160 120"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="81.5" cy="52" r="52" fill="#E4E7EC" />
      <g filter="url(#filter0_dd_7681_260266)">
        <path
          d="M47.2266 79.1088L80.4651 63.6094C82.1339 62.8313 82.8559 60.8476 82.0777 59.1787L62.4484 17.0835L49.1563 12.2456L21.9611 24.9269C20.2923 25.7051 19.5703 27.6888 20.3485 29.3576L42.7959 77.4962C43.574 79.165 45.5577 79.887 47.2266 79.1088Z"
          fill="url(#paint0_linear_7681_260266)"
        />
        <path
          d="M49.1567 12.2461L62.4489 17.084L53.3838 21.3111L49.1567 12.2461Z"
          fill="#D0D5DD"
        />
      </g>
      <g filter="url(#filter1_dd_7681_260266)">
        <path
          d="M63.1163 67.7831H99.7909C101.632 67.7831 103.125 66.2904 103.125 64.4491V18.0022L93.1228 8H63.1163C61.2749 8 59.7822 9.49271 59.7822 11.3341V64.4491C59.7822 66.2904 61.2749 67.7831 63.1163 67.7831Z"
          fill="url(#paint1_linear_7681_260266)"
        />
        <path d="M93.1226 8L103.125 18.0022H93.1226V8Z" fill="#D0D5DD" />
      </g>
      <g filter="url(#filter2_dd_7681_260266)">
        <path
          d="M81.9745 63.5909L115.213 79.0903C116.882 79.8685 118.866 79.1465 119.644 77.4777L139.273 35.3825L134.435 22.0903L107.24 9.40903C105.571 8.63085 103.587 9.35286 102.809 11.0217L80.3619 59.1602C79.5837 60.8291 80.3057 62.8128 81.9745 63.5909Z"
          fill="url(#paint2_linear_7681_260266)"
        />
        <path
          d="M134.436 22.0898L139.273 35.382L130.208 31.1549L134.436 22.0898Z"
          fill="#D0D5DD"
        />
      </g>
      <circle cx="26.5" cy="11" r="5" fill="#F2F4F7" />
      <circle cx="23.5" cy="109" r="7" fill="#F2F4F7" />
      <circle cx="150.5" cy="35" r="7" fill="#F2F4F7" />
      <circle cx="139.5" cy="8" r="4" fill="#F2F4F7" />
      <g filter="url(#filter3_b_7681_260266)">
        <path
          d="M57.5 86C57.5 72.7452 68.2452 62 81.5 62C94.7548 62 105.5 72.7452 105.5 86C105.5 99.2548 94.7548 110 81.5 110C68.2452 110 57.5 99.2548 57.5 86Z"
          fill="#344054"
          fill-opacity="0.4"
        />
        <path
          d="M90.5 95L86.15 90.65M80.5 80C83.2614 80 85.5 82.2386 85.5 85M88.5 85C88.5 89.4183 84.9183 93 80.5 93C76.0817 93 72.5 89.4183 72.5 85C72.5 80.5817 76.0817 77 80.5 77C84.9183 77 88.5 80.5817 88.5 85Z"
          stroke="white"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_dd_7681_260266"
          x="-1.06055"
          y="8.01953"
          width="104.547"
          height="112.5"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_7681_260266"
          />
          <feOffset dy="8" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_7681_260266"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_7681_260266"
          />
          <feOffset dy="20" />
          <feGaussianBlur stdDeviation="12" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_7681_260266"
            result="effect2_dropShadow_7681_260266"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_7681_260266"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_dd_7681_260266"
          x="39.7822"
          y="8"
          width="83.3428"
          height="99.7812"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_7681_260266"
          />
          <feOffset dy="8" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_7681_260266"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_7681_260266"
          />
          <feOffset dy="20" />
          <feGaussianBlur stdDeviation="12" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_7681_260266"
            result="effect2_dropShadow_7681_260266"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_7681_260266"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_dd_7681_260266"
          x="58.9526"
          y="8"
          width="104.547"
          height="112.5"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="erode"
            in="SourceAlpha"
            result="effect1_dropShadow_7681_260266"
          />
          <feOffset dy="8" />
          <feGaussianBlur stdDeviation="4" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_7681_260266"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="erode"
            in="SourceAlpha"
            result="effect2_dropShadow_7681_260266"
          />
          <feOffset dy="20" />
          <feGaussianBlur stdDeviation="12" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="effect1_dropShadow_7681_260266"
            result="effect2_dropShadow_7681_260266"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect2_dropShadow_7681_260266"
            result="shape"
          />
        </filter>
        <filter
          id="filter3_b_7681_260266"
          x="49.5"
          y="54"
          width="64"
          height="64"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="4" />
          <feComposite
            in2="SourceAlpha"
            operator="in"
            result="effect1_backgroundBlur_7681_260266"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_backgroundBlur_7681_260266"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_7681_260266"
          x1="45.2738"
          y1="79.2386"
          x2="18.7853"
          y2="31.4513"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#E4E7EC" />
          <stop offset="1" stop-color="#F9FAFB" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_7681_260266"
          x1="61.2916"
          y1="67.0755"
          x2="57.4807"
          y2="12.571"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#E4E7EC" />
          <stop offset="1" stop-color="#F9FAFB" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_7681_260266"
          x1="80.6199"
          y1="62.1785"
          x2="100.201"
          y2="11.17"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#E4E7EC" />
          <stop offset="1" stop-color="#F9FAFB" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export const PercentChargeIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="15"
      viewBox="0 0 13 12"
      fill="none"
    >
      <path
        d="M9.83789 2.44531L3.16455 9.11865M5.07122 3.39865C5.07122 3.92516 4.6444 4.35198 4.11788 4.35198C3.59137 4.35198 3.16455 3.92516 3.16455 3.39865C3.16455 2.87213 3.59137 2.44531 4.11788 2.44531C4.6444 2.44531 5.07122 2.87213 5.07122 3.39865ZM9.83789 8.16531C9.83789 8.69183 9.41106 9.11865 8.88455 9.11865C8.35804 9.11865 7.93122 8.69183 7.93122 8.16531C7.93122 7.6388 8.35804 7.21198 8.88455 7.21198C9.41106 7.21198 9.83789 7.6388 9.83789 8.16531Z"
        stroke="#2A3339"
        strokeWidth="0.762667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const LimitIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 13 12"
      fill="none"
    >
      <path
        d="M8.88466 5.30552V3.87552C8.88466 2.55924 7.8176 1.49219 6.50132 1.49219C5.18504 1.49219 4.11799 2.55924 4.11799 3.87552V5.30552M4.97599 10.0722H8.02666C8.82753 10.0722 9.22797 10.0722 9.53386 9.91633C9.80293 9.77923 10.0217 9.56047 10.1588 9.2914C10.3147 8.9855 10.3147 8.58506 10.3147 7.78419V7.59352C10.3147 6.79265 10.3147 6.39221 10.1588 6.08632C10.0217 5.81724 9.80293 5.59848 9.53386 5.46138C9.22797 5.30552 8.82753 5.30552 8.02666 5.30552H4.97599C4.17511 5.30552 3.77468 5.30552 3.46878 5.46138C3.19971 5.59848 2.98095 5.81724 2.84385 6.08632C2.68799 6.39221 2.68799 6.79265 2.68799 7.59352V7.78419C2.68799 8.58506 2.68799 8.9855 2.84385 9.2914C2.98095 9.56047 3.19971 9.77923 3.46878 9.91633C3.77468 10.0722 4.17511 10.0722 4.97599 10.0722Z"
        stroke="#2A3339"
        strokeWidth="0.762667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const TierChargeIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 13 12"
      fill="none"
    >
      <g clipPath="url(#clip0_7708_169064)">
        <path
          d="M10.3147 2.44563C10.3147 3.23539 8.60737 3.87563 6.50132 3.87563C4.39528 3.87563 2.68799 3.23539 2.68799 2.44563M10.3147 2.44563C10.3147 1.65586 8.60737 1.01562 6.50132 1.01562C4.39528 1.01562 2.68799 1.65586 2.68799 2.44563M10.3147 2.44563V9.11896C10.3147 9.90873 8.60737 10.549 6.50132 10.549C4.39528 10.549 2.68799 9.90873 2.68799 9.11896V2.44563M10.3147 4.67004C10.3147 5.45981 8.60737 6.10004 6.50132 6.10004C4.39528 6.10004 2.68799 5.45981 2.68799 4.67004M10.3147 6.89293C10.3147 7.68269 8.60737 8.32293 6.50132 8.32293C4.39528 8.32293 2.68799 7.68269 2.68799 6.89293"
          stroke="#2A3339"
          strokeWidth="0.762667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7708_169064">
          <rect
            width="11.44"
            height="11.44"
            fill="white"
            transform="translate(0.78125 0.0625)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ChargeIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 13 12"
      fill="none"
    >
      <g clipPath="url(#clip0_7679_224271)">
        <path
          d="M5.59586 1.96896C6.19767 1.37923 7.02189 1.01562 7.93104 1.01562C9.77383 1.01562 11.2677 2.5095 11.2677 4.35229C11.2677 5.26146 10.9041 6.08569 10.3143 6.6875M8.40771 7.21229C8.40771 9.05508 6.91383 10.549 5.07104 10.549C3.22825 10.549 1.73438 9.05508 1.73438 7.21229C1.73438 5.3695 3.22825 3.87563 5.07104 3.87563C6.91383 3.87563 8.40771 5.3695 8.40771 7.21229Z"
          stroke="#2A3339"
          strokeWidth="0.762667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7679_224271">
          <rect
            width="11.44"
            height="11.44"
            fill="white"
            transform="translate(0.78125 0.0625)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const CheckConfigIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
    >
      <path
        d="M10 3L4.5 8.5L2 6"
        stroke="#555C61"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const EditConfigIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M1.67767 10.5663C1.70447 10.3251 1.71787 10.2045 1.75436 10.0918C1.78674 9.99179 1.83248 9.89662 1.89035 9.80886C1.95558 9.70994 2.04138 9.62414 2.21299 9.45253L9.91666 1.74887C10.561 1.10454 11.6057 1.10454 12.25 1.74887C12.8943 2.39321 12.8943 3.43788 12.25 4.08221L4.54632 11.7859C4.37471 11.9575 4.28891 12.0433 4.19 12.1085C4.10224 12.1664 4.00707 12.2121 3.90706 12.2445C3.79433 12.281 3.67373 12.2944 3.43253 12.3212L1.45831 12.5405L1.67767 10.5663Z"
        stroke="#555C61"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const DeleteConfigIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        d="M5.25 1.75H8.75M1.75 3.5H12.25M11.0833 3.5L10.6743 9.63625C10.6129 10.5569 10.5822 11.0172 10.3833 11.3663C10.2083 11.6735 9.94423 11.9206 9.62597 12.0748C9.26448 12.25 8.80314 12.25 7.88045 12.25H6.11955C5.19686 12.25 4.73552 12.25 4.37403 12.0748C4.05578 11.9206 3.79172 11.6735 3.61666 11.3663C3.41781 11.0172 3.38713 10.5569 3.32575 9.63625L2.91667 3.5"
        stroke="#555C61"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
