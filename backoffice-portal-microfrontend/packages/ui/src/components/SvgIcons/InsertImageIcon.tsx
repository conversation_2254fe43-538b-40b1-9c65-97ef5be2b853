import type { IconsProps } from './IconTypes'

export const InsertImageIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill="none"
    >
      <path
        d="M25.5 23V9C25.5 7.9 24.6 7 23.5 7H9.5C8.4 7 7.5 7.9 7.5 9V23C7.5 24.1 8.4 25 9.5 25H23.5C24.6 25 25.5 24.1 25.5 23ZM13 17.5L15.5 20.51L19 16L23.5 22H9.5L13 17.5Z"
        fill={fill}
      />
    </svg>
  )
}
