import type { IconsProps } from './IconTypes'

export const UsersIcon = ({
  width = '21',
  height = '22',
  fill = 'none',
  stroke = '#5C6670',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 22"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M11.3262 10.9451C12.4585 10.9451 13.3764 11.863 13.3764 12.9953V14.533C13.3764 16.639 11.2359 18.1209 7.73824 18.1209C4.24059 18.1209 2.1001 16.639 2.1001 14.533V12.9953C2.1001 11.863 3.01802 10.9451 4.15033 10.9451H11.3262ZM16.4517 10.9451C17.5841 10.9451 18.502 11.863 18.502 12.9953V13.5079C18.502 15.6489 16.8943 17.0958 13.8889 17.0958C13.7441 17.0958 13.6025 17.0924 13.4641 17.0858C14.0171 16.4423 14.3439 15.6649 14.3946 14.7777L14.4015 14.533V12.9953C14.4015 12.286 14.1614 11.6328 13.758 11.1126L13.6176 10.9442L16.4517 10.9451ZM7.73824 2.74414C9.71979 2.74414 11.3262 4.3505 11.3262 6.33205C11.3262 8.3136 9.71979 9.91996 7.73824 9.91996C5.7567 9.91996 4.15033 8.3136 4.15033 6.33205C4.15033 4.3505 5.7567 2.74414 7.73824 2.74414ZM14.9141 4.79438C16.3295 4.79438 17.4769 5.94178 17.4769 7.35717C17.4769 8.77256 16.3295 9.91996 14.9141 9.91996C13.4987 9.91996 12.3513 8.77256 12.3513 7.35717C12.3513 5.94178 13.4987 4.79438 14.9141 4.79438Z"
        fill={fill}
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
    </svg>
  )
}

export const UserProfileIcon = ({
  width = '30',
  height = '31',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.97368',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 30 31"
      fill={fill}
      {...rest}
    >
      <path
        d="M25 26.75V24.25C25 22.9239 24.4732 21.6521 23.5355 20.7145C22.5979 19.7768 21.3261 19.25 20 19.25H10C8.67392 19.25 7.40215 19.7768 6.46447 20.7145C5.52678 21.6521 5 22.9239 5 24.25V26.75M20 9.25C20 12.0114 17.7614 14.25 15 14.25C12.2386 14.25 10 12.0114 10 9.25C10 6.48858 12.2386 4.25 15 4.25C17.7614 4.25 20 6.48858 20 9.25Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const CustomerSettingsIcon = ({
  width = '28',
  height = '28',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '0.967105',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 28 28"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <rect width="28" height="28" rx="2.947" fill="#E7E8E9" />
      <g clipPath="url(#clip0_3318_130321)">
        <path
          d="M17.4125 19.8609V18.6359C17.4125 17.9862 17.1544 17.363 16.695 16.9035C16.2355 16.4441 15.6123 16.1859 14.9625 16.1859H10.0625C9.41277 16.1859 8.7896 16.4441 8.33014 16.9035C7.87067 17.363 7.61255 17.9862 7.61255 18.6359V19.8609M21.0875 19.8609V18.6359C21.0871 18.0931 20.9065 17.5658 20.5739 17.1367C20.2413 16.7077 19.7757 16.4013 19.25 16.2656M16.8 8.91556C17.3271 9.0505 17.7942 9.35699 18.1277 9.78673C18.4613 10.2165 18.6424 10.745 18.6424 11.289C18.6424 11.833 18.4613 12.3615 18.1277 12.7913C17.7942 13.221 17.3271 13.5275 16.8 13.6624M14.9625 11.2859C14.9625 12.639 13.8656 13.7359 12.5125 13.7359C11.1595 13.7359 10.0625 12.639 10.0625 11.2859C10.0625 9.93284 11.1595 8.83594 12.5125 8.83594C13.8656 8.83594 14.9625 9.93284 14.9625 11.2859Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3318_130321">
          <rect
            width="14.7"
            height="14.7"
            fill="white"
            transform="translate(7 7)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const UserEditIcon = ({
  width = '44',
  height = '44',
  fill = '#00BC2D',
  stroke = 'white',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g filter="url(#filter0_dii_3065_1673)">
        <path
          d="M2 21C2 9.95431 10.9543 1 22 1C33.0457 1 42 9.95431 42 21C42 32.0457 33.0457 41 22 41C10.9543 41 2 32.0457 2 21Z"
          fill={fill}
        />
        <path
          d="M3 21C3 10.5066 11.5066 2 22 2C32.4934 2 41 10.5066 41 21C41 31.4934 32.4934 40 22 40C11.5066 40 3 31.4934 3 21Z"
          stroke="url(#paint0_linear_3065_1673)"
          strokeWidth="2"
        />
        <path
          d="M19.4998 23.9167H18.2498C17.0869 23.9167 16.5054 23.9167 16.0322 24.0602C14.9669 24.3834 14.1332 25.217 13.81 26.2824C13.6665 26.7555 13.6665 27.337 13.6665 28.5M24.0832 17.25C24.0832 19.3211 22.4042 21 20.3332 21C18.2621 21 16.5832 19.3211 16.5832 17.25C16.5832 15.1789 18.2621 13.5 20.3332 13.5C22.4042 13.5 24.0832 15.1789 24.0832 17.25ZM21.1665 28.5L23.751 27.7616C23.8747 27.7262 23.9366 27.7085 23.9943 27.682C24.0456 27.6585 24.0943 27.6298 24.1398 27.5965C24.191 27.5589 24.2365 27.5134 24.3275 27.4224L29.7082 22.0417C30.2835 21.4664 30.2835 20.5336 29.7082 19.9583C29.1329 19.383 28.2001 19.383 27.6249 19.9583L22.2441 25.339C22.1531 25.43 22.1076 25.4756 22.07 25.5267C22.0367 25.5722 22.008 25.6209 21.9845 25.6722C21.958 25.7299 21.9403 25.7918 21.9049 25.9155L21.1665 28.5Z"
          stroke={stroke}
          strokeWidth="1.66667"
          strokeLinecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_dii_3065_1673"
          x="0"
          y="0"
          width="44"
          height="44"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3065_1673"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_3065_1673"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="-2" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect2_innerShadow_3065_1673"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="1"
            operator="erode"
            in="SourceAlpha"
            result="effect3_innerShadow_3065_1673"
          />
          <feOffset />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="effect2_innerShadow_3065_1673"
            result="effect3_innerShadow_3065_1673"
          />
        </filter>
        <linearGradient
          id="paint0_linear_3065_1673"
          x1="22"
          y1="1"
          x2="22"
          y2="41"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" stop-opacity="0.12" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export const UserDeleteIcon = ({
  width = '44',
  height = '44',
  fill = '#D92D20',
  stroke = 'white',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g filter="url(#filter0_dii_3065_1676)">
        <path
          d="M2 21C2 9.95431 10.9543 1 22 1C33.0457 1 42 9.95431 42 21C42 32.0457 33.0457 41 22 41C10.9543 41 2 32.0457 2 21Z"
          fill={fill}
        />
        <path
          d="M3 21C3 10.5066 11.5066 2 22 2C32.4934 2 41 10.5066 41 21C41 31.4934 32.4934 40 22 40C11.5066 40 3 31.4934 3 21Z"
          stroke="url(#paint0_linear_3065_1676)"
          strokeWidth="2"
        />
        <path
          d="M25.3333 16V15.3333C25.3333 14.3999 25.3333 13.9332 25.1517 13.5766C24.9919 13.263 24.7369 13.0081 24.4233 12.8483C24.0668 12.6666 23.6001 12.6666 22.6667 12.6666H21.3333C20.3999 12.6666 19.9332 12.6666 19.5767 12.8483C19.2631 13.0081 19.0081 13.263 18.8483 13.5766C18.6667 13.9332 18.6667 14.3999 18.6667 15.3333V16M20.3333 20.5833V24.75M23.6667 20.5833V24.75M14.5 16H29.5M27.8333 16V25.3333C27.8333 26.7334 27.8333 27.4335 27.5608 27.9683C27.3212 28.4387 26.9387 28.8211 26.4683 29.0608C25.9335 29.3333 25.2335 29.3333 23.8333 29.3333H20.1667C18.7665 29.3333 18.0665 29.3333 17.5317 29.0608C17.0613 28.8211 16.6788 28.4387 16.4392 27.9683C16.1667 27.4335 16.1667 26.7334 16.1667 25.3333V16"
          stroke={stroke}
          strokeWidth="1.66667"
          strokeLinecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_dii_3065_1676"
          x="0"
          y="0"
          width="44"
          height="44"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3065_1676"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_3065_1676"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="-2" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect2_innerShadow_3065_1676"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="1"
            operator="erode"
            in="SourceAlpha"
            result="effect3_innerShadow_3065_1676"
          />
          <feOffset />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="effect2_innerShadow_3065_1676"
            result="effect3_innerShadow_3065_1676"
          />
        </filter>
        <linearGradient
          id="paint0_linear_3065_1676"
          x1="22"
          y1="1"
          x2="22"
          y2="41"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" stop-opacity="0.12" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
      </defs>
    </svg>
  )
}
