import type { IconsProps } from './IconTypes'

export const DeviceIcons = ({
  width = '30',
  height = '30',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.97368',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 30 30"
      fill={fill}
      {...rest}
    >
      <path
        d="M15 21.875H15.0125M10.25 27.5H19.75C21.1501 27.5 21.8502 27.5 22.385 27.2275C22.8554 26.9878 23.2378 26.6054 23.4775 26.135C23.75 25.6002 23.75 24.9001 23.75 23.5V6.5C23.75 5.09987 23.75 4.3998 23.4775 3.86502C23.2378 3.39462 22.8554 3.01217 22.385 2.77248C21.8502 2.5 21.1501 2.5 19.75 2.5H10.25C8.84987 2.5 8.1498 2.5 7.61502 2.77248C7.14462 3.01217 6.76217 3.39462 6.52248 3.86502C6.25 4.3998 6.25 5.09987 6.25 6.5V23.5C6.25 24.9001 6.25 25.6002 6.52248 26.135C6.76217 26.6054 7.14462 26.9878 7.61502 27.2275C8.1498 27.5 8.84987 27.5 10.25 27.5ZM15.625 21.875C15.625 22.2202 15.3452 22.5 15 22.5C14.6548 22.5 14.375 22.2202 14.375 21.875C14.375 21.5298 14.6548 21.25 15 21.25C15.3452 21.25 15.625 21.5298 15.625 21.875Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const AccountIcon = ({
  width = '66',
  height = '66',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2.60526',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 66 66"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <rect width="66" height="66" rx="15.84" fill="#E7E8E9" />
      <path
        d="M39.5002 39.5002C45.1475 38.7441 49.5029 33.9072 49.5029 28.0529C49.5029 21.674 44.3318 16.5029 37.9529 16.5029C32.0987 16.5029 27.2617 20.8584 26.5057 26.5057M39.6029 37.9529C39.6029 44.3318 34.4318 49.5029 28.0529 49.5029C21.674 49.5029 16.5029 44.3318 16.5029 37.9529C16.5029 31.574 21.674 26.4029 28.0529 26.4029C34.4318 26.4029 39.6029 31.574 39.6029 37.9529Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const AccountDetailsIcon = ({
  width = '26',
  height = '26',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '0.870743',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 26 26"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <rect width="26" height="26" rx="6.24" fill="#E7E8E9" />
      <path
        d="M15.1253 15.1311C16.9792 14.8829 18.409 13.2951 18.409 11.3732C18.409 9.27913 16.7114 7.58154 14.6173 7.58154C12.6955 7.58154 11.1076 9.01136 10.8594 10.8653M15.159 14.6232C15.159 16.7173 13.4614 18.4149 11.3673 18.4149C9.27327 18.4149 7.57568 16.7173 7.57568 14.6232C7.57568 12.5291 9.27327 10.8315 11.3673 10.8315C13.4614 10.8315 15.159 12.5291 15.159 14.6232Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const NotificationDetailsIcon = ({
  width = '26',
  height = '26',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '0.870743',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 26 26"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <rect width="26" height="26" rx="6.24" fill="#E7E8E9" />
      <path
        d="M14.0758 17.8732H11.9091M16.2424 10.8315C16.2424 9.96959 15.9 9.14294 15.2905 8.53345C14.681 7.92395 13.8544 7.58154 12.9924 7.58154C12.1305 7.58154 11.3038 7.92395 10.6943 8.53345C10.0848 9.14294 9.74243 9.96959 9.74243 10.8315C9.74243 12.5054 9.32019 13.6514 8.8485 14.4095C8.45063 15.0489 8.25169 15.3686 8.25898 15.4578C8.26706 15.5565 8.28798 15.5942 8.36756 15.6532C8.43943 15.7065 8.76342 15.7065 9.41139 15.7065H16.5735C17.2214 15.7065 17.5454 15.7065 17.6173 15.6532C17.6969 15.5942 17.7178 15.5565 17.7259 15.4578C17.7332 15.3686 17.5342 15.0489 17.1364 14.4095C16.6647 13.6514 16.2424 12.5054 16.2424 10.8315Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const StatementDetailsIcon = ({
  width = '26',
  height = '26',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '0.870743',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 26 26"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <rect width="26" height="26" rx="6.24" fill="#E7E8E9" />
      <path
        d="M16.2083 7.08154L18.375 9.24821M18.375 9.24821L16.2083 11.4149M18.375 9.24821H11.225C10.3149 9.24821 9.85987 9.24821 9.51227 9.42532C9.2065 9.58112 8.95791 9.82971 8.80211 10.1355C8.625 10.4831 8.625 10.9381 8.625 11.8482V11.9565M8.625 15.7482H15.775C16.6851 15.7482 17.1401 15.7482 17.4877 15.5711C17.7935 15.4153 18.0421 15.1667 18.1979 14.8609C18.375 14.5133 18.375 14.0583 18.375 13.1482V13.0399M8.625 15.7482L10.7917 17.9149M8.625 15.7482L10.7917 13.5815"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const SecurityDetailsIcon = ({
  width = '3',
  height = '16',
  fill = '#2A3339',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 3 16"
      fill={fill}
      {...rest}
    >
      <circle cx="1.5" cy="2" r="1.5" fill={fill} />
      <circle cx="1.5" cy="8" r="1.5" fill={fill} />
      <circle cx="1.5" cy="14" r="1.5" fill={fill} />
    </svg>
  )
}
