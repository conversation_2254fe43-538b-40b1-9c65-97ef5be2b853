import type { IconsProps } from './IconTypes';

export const BoldIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 33 32" fill="none" {...rest}>
      <path d="M20.1 14.79C21.07 14.12 21.75 13.02 21.75 12C21.75 9.74 20 8 17.75 8H11.5V22H18.54C20.63 22 22.25 20.3 22.25 18.21C22.25 16.69 21.39 15.39 20.1 14.79ZM14.5 10.5H17.5C18.33 10.5 19 11.17 19 12C19 12.83 18.33 13.5 17.5 13.5H14.5V10.5ZM18 19.5H14.5V16.5H18C18.83 16.5 19.5 17.17 19.5 18C19.5 18.83 18.83 19.5 18 19.5Z" fill={fill} />
    </svg>;
};