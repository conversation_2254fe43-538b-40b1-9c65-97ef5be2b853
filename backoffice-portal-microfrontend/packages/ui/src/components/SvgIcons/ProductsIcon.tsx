import type { IconsProps } from './IconTypes'

export const CustomProductsIcon = ({
  width = '32',
  height = '32',
  fill = 'none',
  stroke = 'black',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width || '32'}
      height={height || '32'}
      viewBox="0 0 32 32"
      fill={fill}
      {...rest}
    >
      <path
        d="M18.6666 3.02588V8.53327C18.6666 9.28 18.6666 9.65337 18.8119 9.93859C18.9397 10.1895 19.1437 10.3934 19.3946 10.5213C19.6798 10.6666 20.0532 10.6666 20.7999 10.6666H26.3073M18.6666 23.3332L21.9999 19.9998L18.6666 16.6665M13.3333 16.6665L9.99992 19.9998L13.3333 23.3332M26.6666 13.3175V22.9332C26.6666 25.1734 26.6666 26.2935 26.2306 27.1491C25.8471 27.9018 25.2352 28.5137 24.4825 28.8972C23.6269 29.3332 22.5068 29.3332 20.2666 29.3332H11.7333C9.49304 29.3332 8.37294 29.3332 7.51729 28.8972C6.76464 28.5137 6.15272 27.9018 5.76923 27.1491C5.33325 26.2935 5.33325 25.1734 5.33325 22.9332V9.0665C5.33325 6.82629 5.33325 5.70619 5.76923 4.85054C6.15272 4.09789 6.76464 3.48597 7.51729 3.10248C8.37294 2.6665 9.49304 2.6665 11.7333 2.6665H16.0156C16.994 2.6665 17.4832 2.6665 17.9435 2.77702C18.3517 2.87501 18.7418 3.03663 19.0997 3.25594C19.5034 3.50331 19.8493 3.84921 20.5411 4.54102L24.7921 8.79199C25.4839 9.48379 25.8298 9.8297 26.0771 10.2334C26.2965 10.5913 26.4581 10.9814 26.5561 11.3896C26.6666 11.8499 26.6666 12.3391 26.6666 13.3175Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
