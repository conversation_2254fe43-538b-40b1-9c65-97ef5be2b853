import type { IconsProps } from './IconTypes'

export const ReceiptIcon = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4 7.80012C4 6.11996 4 5.27989 4.32698 4.63815C4.6146 4.07366 5.07354 3.61472 5.63803 3.3271C6.27976 3.00012 7.11984 3.00012 8.8 3.00012H15.2C16.8802 3.00012 17.7202 3.00012 18.362 3.3271C18.9265 3.61472 19.3854 4.07366 19.673 4.63815C20 5.27989 20 6.11996 20 7.80012V21.0001L17.25 19.0001L14.75 21.0001L12 19.0001L9.25 21.0001L6.75 19.0001L4 21.0001V7.80012Z"
        stroke="#667085"
        strokeWidth="2"
        strokeLinecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
