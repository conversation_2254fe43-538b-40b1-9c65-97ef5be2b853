import type { IconsProps } from './IconTypes'

export const FolderDownloadIcon = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = 'white',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      {...rest}
    >
      <path
        d="M10.8333 5.83333L9.90372 3.9741C9.63617 3.439 9.50239 3.17144 9.30281 2.97597C9.12632 2.80311 8.91361 2.67164 8.68007 2.59109C8.41598 2.5 8.11684 2.5 7.51858 2.5H4.33334C3.39992 2.5 2.93321 2.5 2.57669 2.68166C2.26308 2.84144 2.00812 3.09641 1.84833 3.41002C1.66667 3.76654 1.66667 4.23325 1.66667 5.16667V5.83333M1.66667 5.83333H14.3333C15.7335 5.83333 16.4335 5.83333 16.9683 6.10582C17.4387 6.3455 17.8212 6.72795 18.0609 7.19836C18.3333 7.73314 18.3333 8.4332 18.3333 9.83333V13.5C18.3333 14.9001 18.3333 15.6002 18.0609 16.135C17.8212 16.6054 17.4387 16.9878 16.9683 17.2275C16.4335 17.5 15.7335 17.5 14.3333 17.5H5.66667C4.26654 17.5 3.56647 17.5 3.0317 17.2275C2.56129 16.9878 2.17884 16.6054 1.93916 16.135C1.66667 15.6002 1.66667 14.9001 1.66667 13.5V5.83333ZM7.50001 11.6667L10 14.1667M10 14.1667L12.5 11.6667M10 14.1667V9.16667"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
