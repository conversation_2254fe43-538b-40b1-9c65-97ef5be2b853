/**
 * <AUTHOR> on 18/10/2024
 */

import type { IconsProps } from './IconTypes'

export const SuccessIcon = ({
  width = '49',
  height = '48',
  fill = 'none',
  stroke = '#079455',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 49 48"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M0.5 24C0.5 10.7452 11.2452 0 24.5 0C37.7548 0 48.5 10.7452 48.5 24C48.5 37.2548 37.7548 48 24.5 48C11.2452 48 0.5 37.2548 0.5 24Z"
        fill="#DCFAE6"
      />
      <path
        d="M20 24L23 27L29 21M34.5 24C34.5 29.5228 30.0228 34 24.5 34C18.9772 34 14.5 29.5228 14.5 24C14.5 18.4772 18.9772 14 24.5 14C30.0228 14 34.5 18.4772 34.5 24Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ErrorIcon = ({
  width = '24',
  height = '24',
  fill = 'none',
  stroke = '#F04438',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      {...rest}
    >
      <path
        d="M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
        stroke="#F04438"
        strokeWidth="2"
        strokeLinecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
