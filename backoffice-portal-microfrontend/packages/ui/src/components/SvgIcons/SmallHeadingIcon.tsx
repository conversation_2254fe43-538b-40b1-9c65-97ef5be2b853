import type { IconsProps } from './IconTypes'

export const SmallHeadingIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill="none"
      {...rest}
    >
      <path
        d="M21.248 22H18.5215V17.25H14.4824V22H11.7402V10.625H14.4824V15.1406H18.5215V10.625H21.248V22Z"
        fill={fill}
      />
    </svg>
  )
}
