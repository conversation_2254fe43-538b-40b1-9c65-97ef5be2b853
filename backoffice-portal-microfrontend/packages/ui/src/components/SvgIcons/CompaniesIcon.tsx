import type { IconsProps } from './IconTypes'

export const CompaniesIcon = ({
  width = '24',
  height = '24',
  fill = 'none',
  stroke = '#667085',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M7.5 11.0001H4.6C4.03995 11.0001 3.75992 11.0001 3.54601 11.1091C3.35785 11.205 3.20487 11.358 3.10899 11.5461C3 11.76 3 12.0401 3 12.6001V21.0001M16.5 11.0001H19.4C19.9601 11.0001 20.2401 11.0001 20.454 11.1091C20.6422 11.205 20.7951 11.358 20.891 11.5461C21 11.76 21 12.0401 21 12.6001V21.0001M16.5 21.0001V6.20012C16.5 5.08002 16.5 4.51996 16.282 4.09214C16.0903 3.71582 15.7843 3.40986 15.408 3.21811C14.9802 3.00012 14.4201 3.00012 13.3 3.00012H10.7C9.57989 3.00012 9.01984 3.00012 8.59202 3.21811C8.21569 3.40986 7.90973 3.71582 7.71799 4.09214C7.5 4.51996 7.5 5.08002 7.5 6.20012V21.0001M22 21.0001H2M11 7.00012H13M11 11.0001H13M11 15.0001H13"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
