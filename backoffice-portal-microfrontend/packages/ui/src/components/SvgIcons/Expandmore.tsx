import type { IconsProps } from './IconTypes'

export const Expandmore = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#555C61',
  strokeWidth = '1.67',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 21 21"
      fill={fill}
      {...rest}
    >
      <path
        d="M5.64648 7.56445L10.6465 12.5645L15.6465 7.56445"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
