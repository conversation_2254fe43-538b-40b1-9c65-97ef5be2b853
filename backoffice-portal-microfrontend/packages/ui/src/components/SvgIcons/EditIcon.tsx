import type { IconsProps } from './IconTypes'

export const EditIcon = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#1570EF',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M9.99997 19.0002H19M1 19.0002H2.67454C3.16372 19.0002 3.40832 19.0002 3.63849 18.945C3.84256 18.896 4.03765 18.8152 4.2166 18.7055C4.41843 18.5818 4.59138 18.4089 4.93729 18.063L17.5 5.50023C18.3285 4.6718 18.3285 3.32865 17.5 2.50023C16.6716 1.6718 15.3285 1.6718 14.5 2.50023L1.93726 15.063C1.59136 15.4089 1.4184 15.5818 1.29472 15.7837C1.18506 15.9626 1.10425 16.1577 1.05526 16.3618C1 16.5919 1 16.8365 1 17.3257V19.0002Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
