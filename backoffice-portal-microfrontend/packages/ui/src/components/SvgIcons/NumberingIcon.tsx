import type { IconsProps } from './IconTypes'

export const NumberingIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill="none"
      {...rest}
    >
      <path
        d="M7.5 21H9.5V21.5H8.5V22.5H9.5V23H7.5V24H10.5V20H7.5V21ZM8.5 12H9.5V8H7.5V9H8.5V12ZM7.5 15H9.3L7.5 17.1V18H10.5V17H8.7L10.5 14.9V14H7.5V15ZM12.5 9V11H26.5V9H12.5ZM12.5 23H26.5V21H12.5V23ZM12.5 17H26.5V15H12.5V17Z"
        fill={fill}
      />
    </svg>
  )
}
