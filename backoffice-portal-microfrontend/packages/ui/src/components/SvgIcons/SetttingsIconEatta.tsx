import type { IconsProps } from './IconTypes'

export const SettingsIconEatta = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#667085',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M13.0505 7.00012H3.5C2.11929 7.00012 1 5.88083 1 4.50012C1 3.11941 2.11929 2.00012 3.5 2.00012H13.0505M6.94949 18.0001H16.5C17.8807 18.0001 19 16.8808 19 15.5001C19 14.1194 17.8807 13.0001 16.5 13.0001H6.94949M1 15.5001C1 17.4331 2.567 19.0001 4.5 19.0001C6.433 19.0001 8 17.4331 8 15.5001C8 13.5671 6.433 12.0001 4.5 12.0001C2.567 12.0001 1 13.5671 1 15.5001ZM19 4.50012C19 6.43312 17.433 8.00012 15.5 8.00012C13.567 8.00012 12 6.43312 12 4.50012C12 2.56713 13.567 1.00012 15.5 1.00012C17.433 1.00012 19 2.56713 19 4.50012Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
