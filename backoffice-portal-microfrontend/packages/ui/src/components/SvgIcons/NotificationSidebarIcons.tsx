import type { IconsProps } from './IconTypes'

export const MobileIcon = ({
  width = '21',
  height = '20',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.8001 1.66602V2.83268C12.8001 3.29939 12.8001 3.53275 12.7093 3.71101C12.6294 3.86781 12.5019 3.99529 12.3451 4.07519C12.1669 4.16602 11.9335 4.16602 11.4668 4.16602H9.13346C8.66675 4.16602 8.4334 4.16602 8.25514 4.07519C8.09834 3.99529 7.97085 3.86781 7.89096 3.71101C7.80013 3.53275 7.80013 3.29939 7.80013 2.83268V1.66602M7.13346 18.3327H13.4668C14.4002 18.3327 14.8669 18.3327 15.2234 18.151C15.5371 17.9912 15.792 17.7363 15.9518 17.4227C16.1335 17.0661 16.1335 16.5994 16.1335 15.666V4.33268C16.1335 3.39926 16.1335 2.93255 15.9518 2.57603C15.792 2.26243 15.5371 2.00746 15.2234 1.84767C14.8669 1.66602 14.4002 1.66602 13.4668 1.66602H7.13346C6.20004 1.66602 5.73333 1.66602 5.37681 1.84767C5.06321 2.00746 4.80824 2.26243 4.64845 2.57603C4.4668 2.93255 4.4668 3.39926 4.4668 4.33268V15.666C4.4668 16.5994 4.4668 17.0661 4.64845 17.4227C4.80824 17.7363 5.06321 17.9912 5.37681 18.151C5.73333 18.3327 6.20004 18.3327 7.13346 18.3327Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const LMSIcon = ({
  width = '21',
  height = '20',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4450_2643)">
        <path
          d="M8.7196 3.33268C9.77171 2.30169 11.2127 1.66602 12.8021 1.66602C16.0237 1.66602 18.6354 4.27769 18.6354 7.49935C18.6354 9.0888 17.9997 10.5298 16.9687 11.5819M13.6354 12.4993C13.6354 15.721 11.0237 18.3327 7.80208 18.3327C4.58042 18.3327 1.96875 15.721 1.96875 12.4993C1.96875 9.27769 4.58042 6.66602 7.80208 6.66602C11.0237 6.66602 13.6354 9.27769 13.6354 12.4993Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4450_2643">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.300781)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const ApprovalsIcon = ({
  width = '21',
  height = '20',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4450_853)">
        <path
          d="M19.2174 9.58333L17.5512 11.25L15.8841 9.58333M17.755 10.8333C17.7853 10.5597 17.8008 10.2817 17.8008 10C17.8008 5.85786 14.4429 2.5 10.3008 2.5C6.15865 2.5 2.80078 5.85786 2.80078 10C2.80078 14.1421 6.15865 17.5 10.3008 17.5C12.6568 17.5 14.7592 16.4136 16.1341 14.7144M10.3008 5.83333V10L12.8008 11.6667"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4450_853">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.300781)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
