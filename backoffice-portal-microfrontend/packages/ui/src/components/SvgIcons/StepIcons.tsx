/**
 * <AUTHOR> on 18/10/2024
 */

import type { IconsProps } from './IconTypes'

export const StepDoneIcon = ({
  width = '24',
  height = '24',
  fill = 'none',
  stroke = '#029326',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g clipPath="url(#clip0_607_337)">
        <path
          d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z"
          fill="#F9F5FF"
        />
        <path
          d="M0.75 12C0.75 5.7868 5.7868 0.75 12 0.75C18.2132 0.75 23.25 5.7868 23.25 12C23.25 18.2132 18.2132 23.25 12 23.25C5.7868 23.25 0.75 18.2132 0.75 12Z"
          fill="#029326"
        />
        <path
          d="M0.75 12C0.75 5.7868 5.7868 0.75 12 0.75C18.2132 0.75 23.25 5.7868 23.25 12C23.25 18.2132 18.2132 23.25 12 23.25C5.7868 23.25 0.75 18.2132 0.75 12Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.0965 7.39004L9.9365 14.3L8.0365 12.27C7.6865 11.94 7.1365 11.92 6.7365 12.2C6.3465 12.49 6.2365 13 6.4765 13.41L8.7265 17.07C8.9465 17.41 9.3265 17.62 9.7565 17.62C10.1665 17.62 10.5565 17.41 10.7765 17.07C11.1365 16.6 18.0065 8.41004 18.0065 8.41004C18.9065 7.49004 17.8165 6.68004 17.0965 7.38004V7.39004Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_607_337">
          <path
            d="M0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12Z"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const StepInactiveIcon = ({
  width = '24',
  height = '24',
  fill = 'none',
  stroke = '#EAECF0',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M0.75 12C0.75 5.7868 5.7868 0.75 12 0.75C18.2132 0.75 23.25 5.7868 23.25 12C23.25 18.2132 18.2132 23.25 12 23.25C5.7868 23.25 0.75 18.2132 0.75 12Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <circle cx={12} cy={12} r={4} fill="#D0D5DD" />
    </svg>
  )
}

export const StepActiveIcon = ({
  width = '24',
  height = '24',
  fill = 'none',
  stroke = '#029326',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M0.75 12C0.75 5.7868 5.7868 0.75 12 0.75C18.2132 0.75 23.25 5.7868 23.25 12C23.25 18.2132 18.2132 23.25 12 23.25C5.7868 23.25 0.75 18.2132 0.75 12Z"
        fill="#029326"
      />
      <path
        d="M0.75 12C0.75 5.7868 5.7868 0.75 12 0.75C18.2132 0.75 23.25 5.7868 23.25 12C23.25 18.2132 18.2132 23.25 12 23.25C5.7868 23.25 0.75 18.2132 0.75 12Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <circle cx={12} cy={12} r={4} fill="white" />
    </svg>
  )
}

export const CheckCompleteIcon = ({
  width = '33',
  height = '32',
  fill = 'none',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="Check icon">
        <path
          d="M0.5 16C0.5 7.16344 7.66344 0 16.5 0C25.3366 0 32.5 7.16344 32.5 16C32.5 24.8366 25.3366 32 16.5 32C7.66344 32 0.5 24.8366 0.5 16Z"
          fill="#EEFFF1"
        />
        <path
          id="Icon"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M23.2953 9.8534L13.7487 19.0667L11.2153 16.3601C10.7487 15.9201 10.0153 15.8934 9.482 16.2667C8.962 16.6534 8.81534 17.3334 9.13534 17.8801L12.1353 22.7601C12.4287 23.2134 12.9353 23.4934 13.5087 23.4934C14.0553 23.4934 14.5753 23.2134 14.8687 22.7601C15.3487 22.1334 24.5087 11.2134 24.5087 11.2134C25.7087 9.98674 24.2553 8.90674 23.2953 9.84007V9.8534Z"
          fill="#00BC2D"
        />
      </g>
    </svg>
  )
}
