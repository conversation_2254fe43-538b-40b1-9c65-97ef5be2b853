import type { IconsProps } from './IconTypes'

export const LargeHeadingIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill="none"
      {...rest}
    >
      <path
        d="M22.2832 22H19.3535V15.9062H13.6406V22H10.7109V7.78125H13.6406V13.543H19.3535V7.78125H22.2832V22Z"
        fill={fill}
      />
    </svg>
  )
}
