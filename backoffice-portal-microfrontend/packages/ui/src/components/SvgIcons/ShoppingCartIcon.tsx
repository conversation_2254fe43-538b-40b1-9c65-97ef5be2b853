import type { IconsProps } from './IconTypes'

export const ShoppingCartIcon = ({
  width = '22',
  height = '21',
  fill = 'none',
  stroke = '#808488',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 22 21"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M5.50014 16H16.3294C17.2793 16 17.7543 16 18.1414 15.8284C18.4827 15.6771 18.7748 15.4333 18.9847 15.1246C19.2228 14.7744 19.3078 14.3071 19.4777 13.3724L20.8285 5.94311C20.8874 5.61918 20.9169 5.45721 20.8714 5.33074C20.8315 5.21979 20.7536 5.12651 20.6516 5.06739C20.5353 5 20.3707 5 20.0414 5H4.00014M1 1H2.3164C2.55909 1 2.68044 1 2.77858 1.04433C2.86507 1.0834 2.93867 1.14628 2.99075 1.22563C3.04984 1.31565 3.06876 1.43551 3.10662 1.67523L5.89338 19.3248C5.93124 19.5645 5.95016 19.6843 6.00925 19.7744C6.06133 19.8537 6.13493 19.9166 6.22142 19.9557C6.31956 20 6.44091 20 6.6836 20H18"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
