import type { IconsProps } from './IconTypes'

export const <PERSON><PERSON><PERSON><PERSON> = ({
  width = '156',
  height = '88',
  fill = 'none',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 156 88"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M86.152 37.9914C87.2525 37.2534 88.5516 35.9099 88.5516 32.8288C88.5516 29.7339 87.3623 26.0577 81.7386 26.0577H66.3765V52.5542H81.5714C87.8984 52.5542 89.5763 47.188 89.5763 44.2667C89.5763 41.2332 88.4305 39.1469 86.152 37.9914ZM80.0995 47.188H71.9502V31.0225H79.6159C82.4273 31.0225 82.9885 31.9341 82.9885 33.4174C82.9885 35.5595 81.0967 36.0334 80.4402 36.0334H76.1528L73.4156 41.3511H80.7713C82.1681 41.3511 83.8371 41.8445 83.8371 44.165C83.8371 46.6963 82.4677 47.188 80.0995 47.188ZM29.7049 26.027C35.9253 26.027 40.2708 31.4037 40.2708 39.0903C40.2708 46.907 35.8074 52.555 29.1607 52.555H16.9524V36.5138L22.5285 33.3366V47.1565H27.65C31.8696 47.1565 34.2878 44.3144 34.2878 39.3495C34.2878 34.0278 32.069 30.9862 28.2047 30.9862H16.9524V26.0254H29.7049V26.027Z"
        fill="#EB0045"
      />
      <path
        d="M63.0364 26.0996H41.9634V31.3414H63.0364V26.0996Z"
        fill="#EB0045"
      />
      <path
        d="M55.2851 33.3359L49.7114 36.5132H49.7308V52.5438H55.2689V33.3359"
        fill="#EB0045"
      />
      <path
        d="M82.1746 65.4274L103.04 39.1893L96.8891 25.019H110.359L113.239 30.4691L122.815 24.3965L112.325 35.9192L108.412 28.2116H101.931L107.478 40.1816L82.1746 65.4274Z"
        fill="#EB0045"
      />
      <path
        d="M140.099 14.3535L119.233 40.5916L125.383 54.7611H111.915L109.034 49.311L99.457 55.3844L109.949 43.8609L113.861 51.5694H120.343L114.795 39.5985L140.099 14.3535Z"
        fill="#5C6670"
      />
    </svg>
  )
}

export const InternalNavLogo = ({
  width = '123',
  height = '51',
  fill = 'none',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 123 51"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g clipPath="url(#clip0_1712_79248)">
        <path
          d="M69.1527 18.9914C70.2532 18.2534 71.5524 16.9099 71.5524 13.8288C71.5524 10.7339 70.363 7.05769 64.7393 7.05769H49.3772V33.5542H64.5722C70.8992 33.5542 72.577 28.188 72.577 25.2667C72.577 22.2332 71.4313 20.1469 69.1527 18.9914ZM63.1002 28.188H54.9509V12.0225H62.6166C65.428 12.0225 65.9892 12.9341 65.9892 14.4174C65.9892 16.5595 64.0974 17.0334 63.441 17.0334H59.1535L56.4164 22.3511H63.772C65.1689 22.3511 66.8378 22.8445 66.8378 25.165C66.8378 27.6963 65.4684 28.188 63.1002 28.188ZM12.7056 7.02701C18.926 7.02701 23.2716 12.4037 23.2716 20.0903C23.2716 27.907 18.8081 33.555 12.1614 33.555H-0.046875V17.5138L5.5292 14.3366V28.1565H10.6507C14.8703 28.1565 17.2885 25.3144 17.2885 20.3495C17.2885 15.0278 15.0697 11.9862 11.2054 11.9862H-0.046875V7.02539H12.7056V7.02701Z"
          fill="#EB0045"
        />
        <path
          d="M46.0378 7.09961H24.9648V12.3414H46.0378V7.09961Z"
          fill="#EB0045"
        />
        <path
          d="M38.2865 14.3369L32.7129 17.5141H32.7323V33.5448H38.2704V14.3369"
          fill="#EB0045"
        />
        <path
          d="M65.1758 46.4274L86.0413 20.1893L79.8903 6.01901H93.3598L96.2407 11.4691L105.817 5.39648L95.3258 16.9192L91.4131 9.21157H84.9319L90.4789 21.1816L65.1758 46.4274Z"
          fill="#EB0045"
        />
        <path
          d="M123.099 -4.64648L102.233 21.5916L108.383 35.7611H94.9148L92.0339 30.311L82.457 36.3844L92.9487 24.8609L96.8607 32.5694H103.343L97.7949 20.5985L123.099 -4.64648Z"
          fill="#5C6670"
        />
      </g>
      <defs>
        <clipPath id="clip0_1712_79248">
          <rect width="123" height="51" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}
export const InternalCollapsedLogo = ({
  width = '44',
  height = '39',
  fill = 'none',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 44 39"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M0 38.7972L15.85 18.866L11.1776 8.10179H21.4094L23.5978 12.2419L30.872 7.62891L22.9029 16.3819L19.9306 10.527H15.0073L19.221 19.6198L0 38.7972Z"
        fill="#EB0045"
      />
      <path
        d="M43.9996 0L28.1496 19.9312L32.8214 30.6948H22.5902L20.4018 26.5547L13.127 31.1683L21.0967 22.4146L24.0684 28.2702H28.9923L24.778 19.1768L43.9996 0Z"
        fill="#5C6670"
      />
    </svg>
  )
}
