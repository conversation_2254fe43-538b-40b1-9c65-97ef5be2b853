import type { IconsProps } from './IconTypes'

export const ProductCategoriesIcon = ({
  width = '32',
  height = '32',
  fill = 'none',
  stroke = 'black',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 32 32"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M18.6666 3.02637V8.53376C18.6666 9.28049 18.6666 9.65386 18.8119 9.93907C18.9397 10.19 19.1437 10.3939 19.3946 10.5218C19.6798 10.6671 20.0532 10.6671 20.7999 10.6671H26.3073M18.6666 22.667H10.6666M21.3333 17.3337H10.6666M26.6666 13.318V22.9337C26.6666 25.1739 26.6666 26.294 26.2306 27.1496C25.8471 27.9023 25.2352 28.5142 24.4825 28.8977C23.6269 29.3337 22.5068 29.3337 20.2666 29.3337H11.7333C9.49304 29.3337 8.37294 29.3337 7.51729 28.8977C6.76464 28.5142 6.15272 27.9023 5.76923 27.1496C5.33325 26.294 5.33325 25.1739 5.33325 22.9337V9.06699C5.33325 6.82678 5.33325 5.70668 5.76923 4.85103C6.15272 4.09838 6.76464 3.48646 7.51729 3.10297C8.37294 2.66699 9.49304 2.66699 11.7333 2.66699H16.0156C16.994 2.66699 17.4832 2.66699 17.9435 2.77751C18.3517 2.8755 18.7418 3.03712 19.0997 3.25643C19.5034 3.5038 19.8493 3.8497 20.5411 4.54151L24.7921 8.79247C25.4839 9.48428 25.8298 9.83019 26.0771 10.2339C26.2965 10.5917 26.4581 10.9819 26.5561 11.3901C26.6666 11.8504 26.6666 12.3396 26.6666 13.318Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
