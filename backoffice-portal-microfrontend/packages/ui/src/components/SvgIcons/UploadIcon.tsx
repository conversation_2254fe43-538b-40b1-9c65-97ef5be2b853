import type { IconsProps } from './IconTypes'

export const UploadIcon = ({
  width = '45',
  height = '44',
  fill = 'none',
  stroke = '#344054',
  strokeWidth = '1.66667',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 45 44"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      aria-hidden="true"
      className={className}
      {...rest}
    >
      <path
        d="M19.1665 24.3333L22.4998 21M22.4998 21L25.8332 24.3333M22.4998 21V28.5M29.1665 24.9524C30.1844 24.1117 30.8332 22.8399 30.8332 21.4167C30.8332 18.8854 28.7811 16.8333 26.2498 16.8333C26.0677 16.8333 25.8974 16.7383 25.8049 16.5814C24.7182 14.7374 22.7119 13.5 20.4165 13.5C16.9647 13.5 14.1665 16.2982 14.1665 19.75C14.1665 21.4718 14.8627 23.0309 15.989 24.1613"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <filter
          id="filter0_d_2164_10752"
          x="0.5"
          y="0"
          width="44"
          height="44"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_2164_10752"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_2164_10752"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}

export const TrashIcon = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#F04438',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="trash-01">
        <path
          id="Icon"
          d="M13.3333 5.00002V4.33335C13.3333 3.39993 13.3333 2.93322 13.1517 2.5767C12.9919 2.2631 12.7369 2.00813 12.4233 1.84834C12.0668 1.66669 11.6001 1.66669 10.6667 1.66669H9.33333C8.39991 1.66669 7.9332 1.66669 7.57668 1.84834C7.26308 2.00813 7.00811 2.2631 6.84832 2.5767C6.66667 2.93322 6.66667 3.39993 6.66667 4.33335V5.00002M8.33333 9.58335V13.75M11.6667 9.58335V13.75M2.5 5.00002H17.5M15.8333 5.00002V14.3334C15.8333 15.7335 15.8333 16.4336 15.5608 16.9683C15.3212 17.4387 14.9387 17.8212 14.4683 18.0609C13.9335 18.3334 13.2335 18.3334 11.8333 18.3334H8.16667C6.76654 18.3334 6.06647 18.3334 5.53169 18.0609C5.06129 17.8212 4.67883 17.4387 4.43915 16.9683C4.16667 16.4336 4.16667 15.7335 4.16667 14.3334V5.00002"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export const AlertCircleIcon = ({
  width = '25',
  height = '24',
  fill = 'none',
  stroke = '#D92D20',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 25 24"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="alert-circle">
        <path
          id="Icon"
          d="M12.5 8V12M12.5 16H12.51M22.5 12C22.5 17.5228 18.0228 22 12.5 22C6.97715 22 2.5 17.5228 2.5 12C2.5 6.47715 6.97715 2 12.5 2C18.0228 2 22.5 6.47715 22.5 12Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}
