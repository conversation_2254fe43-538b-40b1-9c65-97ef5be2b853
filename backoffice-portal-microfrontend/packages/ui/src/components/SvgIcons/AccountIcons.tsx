import { Stack } from '@mui/material'
import { JSX } from 'react'

interface IconProps {
  height?: string
  width?: string
  stroke?: string
}

export const TariffIcon = ({
  height = '10',
  width = '10',
  stroke = '#2A3339',
}: IconProps): JSX.Element => {
  return (
    <Stack
      sx={{
        width,
        height,
        borderRadius: '50%',
        backgroundColor: '#e3e4e4',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2px',
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox="0 0 10 10"
        fill="none"
      >
        <path
          d="M1.68555 3.86086L6.10555 3.86086M6.10555 3.86086C6.10555 4.47113 6.60027 4.96586 7.21055 4.96586C7.82082 4.96586 8.31555 4.47113 8.31555 3.86086C8.31555 3.25058 7.82082 2.75586 7.21055 2.75586C6.60027 2.75586 6.10555 3.25059 6.10555 3.86086ZM3.89555 6.80753L8.31555 6.80753M3.89555 6.80753C3.89555 7.4178 3.40082 7.91253 2.79055 7.91253C2.18027 7.91253 1.68555 7.4178 1.68555 6.80753C1.68555 6.19725 2.18027 5.70253 2.79055 5.70253C3.40082 5.70253 3.89555 6.19725 3.89555 6.80753Z"
          stroke={stroke}
          strokeWidth="0.589333"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </Stack>
  )
}
export const NotificationsIcon = ({
  height = '10',
  width = '10',
  stroke = '#2A3339',
}: IconProps): JSX.Element => {
  return (
    <Stack
      sx={{
        width,
        height,
        borderRadius: '50%',
        backgroundColor: '#e3e4e4',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2px',
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox="0 0 10 10"
        fill="none"
      >
        <g clipPath="url(#clip0_6410_279063)">
          <path
            d="M4.02558 8.64873C4.28531 8.87796 4.62647 9.01706 5.00013 9.01706C5.37378 9.01706 5.71495 8.87796 5.97467 8.64873M7.21013 3.86039C7.21013 3.27426 6.97729 2.71214 6.56283 2.29768C6.14838 1.88323 5.58626 1.65039 5.00013 1.65039C4.414 1.65039 3.85188 1.88323 3.43742 2.29768C3.02297 2.71214 2.79013 3.27426 2.79013 3.86039C2.79013 4.99861 2.503 5.77792 2.18225 6.29339C1.9117 6.72819 1.77642 6.94559 1.78138 7.00624C1.78687 7.07339 1.8011 7.099 1.85521 7.13914C1.90409 7.17539 2.1244 7.17539 2.56502 7.17539H7.43523C7.87586 7.17539 8.09617 7.17539 8.14504 7.13914C8.19915 7.099 8.21338 7.07339 8.21887 7.00624C8.22383 6.94559 8.08856 6.72819 7.818 6.29339C7.49725 5.77792 7.21013 4.99861 7.21013 3.86039Z"
            stroke={stroke}
            strokeWidth="0.589333"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <clipPath id="clip0_6410_279063">
            <rect
              width="8.84"
              height="8.84"
              fill="white"
              transform="translate(0.580078 0.914062)"
            />
          </clipPath>
        </defs>
      </svg>
    </Stack>
  )
}

export const EstatementIcon = ({
  height = '10',
  width = '10',
  stroke = '#2A3339',
}: IconProps): JSX.Element => {
  return (
    <Stack
      sx={{
        width,
        height,
        borderRadius: '50%',
        backgroundColor: '#e3e4e4',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2px',
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox="0 0 10 10"
        fill="none"
      >
        <path
          d="M1.31641 3.4927L4.32382 5.59788C4.56735 5.76836 4.68912 5.85359 4.82156 5.88661C4.93856 5.91577 5.06092 5.91577 5.17792 5.88661C5.31036 5.85359 5.43213 5.76836 5.67566 5.59788L8.68307 3.4927M3.08441 8.28103H6.91507C7.53393 8.28103 7.84336 8.28103 8.07973 8.16059C8.28765 8.05465 8.4567 7.88561 8.56264 7.67769C8.68307 7.44132 8.68307 7.13189 8.68307 6.51303V4.1557C8.68307 3.53684 8.68307 3.22741 8.56264 2.99104C8.4567 2.78312 8.28765 2.61407 8.07973 2.50813C7.84336 2.3877 7.53393 2.3877 6.91507 2.3877H3.08441C2.46555 2.3877 2.15612 2.3877 1.91975 2.50813C1.71183 2.61407 1.54278 2.78312 1.43684 2.99104C1.31641 3.22741 1.31641 3.53684 1.31641 4.1557V6.51303C1.31641 7.13189 1.31641 7.44132 1.43684 7.67769C1.54278 7.88561 1.71183 8.05465 1.91975 8.16059C2.15612 8.28103 2.46555 8.28103 3.08441 8.28103Z"
          stroke={stroke}
          strokeWidth="0.589333"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </Stack>
  )
}
export const BalanceAlertIcon = ({
  height = '10',
  width = '10',
  stroke = '#2A3339',
}: IconProps): JSX.Element => {
  return (
    <Stack
      sx={{
        width,
        height,
        borderRadius: '50%',
        backgroundColor: '#e3e4e4',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '2px',
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox="0 0 10 10"
        fill="none"
      >
        <g clipPath="url(#clip0_6410_279077)">
          <path
            d="M6.84221 1.65039L8.31555 3.12372M8.31555 3.12372L6.84221 4.59706M8.31555 3.12372H3.45355C2.83469 3.12372 2.52526 3.12372 2.28889 3.24416C2.08097 3.3501 1.91192 3.51915 1.80598 3.72706C1.68555 3.96344 1.68555 4.27287 1.68555 4.89172V4.96539M1.68555 7.54373H6.54755C7.16641 7.54373 7.47584 7.54373 7.71221 7.42329C7.92013 7.31735 8.08917 7.1483 8.19511 6.94038C8.31555 6.70401 8.31555 6.39458 8.31555 5.77572V5.70206M1.68555 7.54373L3.15888 9.01706M1.68555 7.54373L3.15888 6.07039"
            stroke={stroke}
            strokeWidth="0.589333"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <clipPath id="clip0_6410_279077">
            <rect
              width="8.84"
              height="8.84"
              fill="white"
              transform="translate(0.580078 0.914062)"
            />
          </clipPath>
        </defs>
      </svg>
    </Stack>
  )
}
export const TariffCheckedIcon = ({
  height = '10',
  width = '10',
  stroke = '#555C61',
}: IconProps): JSX.Element => {
  return (
    <Stack
      sx={{
        width,
        height,
        borderRadius: '6px',
        backgroundColor: '#F9FAFB',
        border: '1px solid #EAECF0',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '0.4rem 0.5rem',
      }}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
      >
        <path
          d="M10 3L4.5 8.5L2 6"
          stroke={stroke}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </Stack>
  )
}

export const RightIcon = ({
  height = '10',
  width = '10',
  stroke = '#2A3339',
}: IconProps): JSX.Element => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 13 12"
      fill="none"
    >
      <path
        d="M5 9L8 6L5 3"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const LeftIcon = ({
  height = '10',
  width = '10',
  stroke = '#2A3339',
}: IconProps): JSX.Element => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 12 12"
      fill="none"
    >
      <path
        d="M7.5 9L4.5 6L7.5 3"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
