import type { IconsProps } from './IconTypes'

export const Expandless = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.67',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 21 21"
      fill={fill}
      {...rest}
    >
      <path
        d="M15.6465 12.6953L10.6465 7.69531L5.64648 12.6953"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
