import type { IconsProps } from './IconTypes'

export const GenerateReportsIcons = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = 'white',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      {...rest}
    >
      <path
        d="M13.3333 3.33366C14.1083 3.33366 14.4958 3.33366 14.8137 3.41884C15.6764 3.65001 16.3503 4.32388 16.5815 5.18661C16.6666 5.50453 16.6666 5.89202 16.6666 6.66699V14.3337C16.6666 15.7338 16.6666 16.4339 16.3942 16.9686C16.1545 17.439 15.772 17.8215 15.3016 18.0612C14.7668 18.3337 14.0668 18.3337 12.6666 18.3337H7.33331C5.93318 18.3337 5.23312 18.3337 4.69834 18.0612C4.22793 17.8215 3.84548 17.439 3.6058 16.9686C3.33331 16.4339 3.33331 15.7338 3.33331 14.3337V6.66699C3.33331 5.89202 3.33331 5.50453 3.4185 5.18661C3.64967 4.32388 4.32354 3.65001 5.18627 3.41884C5.50418 3.33366 5.89167 3.33366 6.66665 3.33366M9.99998 14.167V9.16699M7.49998 11.667H12.5M7.99998 5.00033H12C12.4667 5.00033 12.7 5.00033 12.8783 4.9095C13.0351 4.8296 13.1626 4.70212 13.2425 4.54532C13.3333 4.36706 13.3333 4.1337 13.3333 3.66699V3.00033C13.3333 2.53362 13.3333 2.30026 13.2425 2.122C13.1626 1.9652 13.0351 1.83771 12.8783 1.75782C12.7 1.66699 12.4667 1.66699 12 1.66699H7.99998C7.53327 1.66699 7.29991 1.66699 7.12165 1.75782C6.96485 1.83771 6.83737 1.9652 6.75747 2.122C6.66665 2.30026 6.66665 2.53362 6.66665 3.00033V3.66699C6.66665 4.1337 6.66665 4.36706 6.75747 4.54532C6.83737 4.70212 6.96485 4.8296 7.12165 4.9095C7.29991 5.00033 7.53327 5.00033 7.99998 5.00033Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const DownloadCloudIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = 'white',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      {...rest}
    >
      <path
        d="M7.16675 14.1667L10.5001 17.5M10.5001 17.5L13.8334 14.1667M10.5001 17.5V10M17.1667 13.9524C18.1847 13.1117 18.8334 11.8399 18.8334 10.4167C18.8334 7.88536 16.7814 5.83333 14.2501 5.83333C14.068 5.83333 13.8976 5.73833 13.8052 5.58145C12.7185 3.73736 10.7121 2.5 8.41675 2.5C4.96497 2.5 2.16675 5.29822 2.16675 8.75C2.16675 10.4718 2.86295 12.0309 3.98921 13.1613"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
