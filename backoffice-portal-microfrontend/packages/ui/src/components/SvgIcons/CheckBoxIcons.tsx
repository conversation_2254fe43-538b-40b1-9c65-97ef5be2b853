import type { IconsProps } from './IconTypes'

export const CheckBoxIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#D0D5DD',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M1 6C1 2.96243 3.46243 0.5 6.5 0.5H14.5C17.5376 0.5 20 2.96243 20 6V14C20 17.0376 17.5376 19.5 14.5 19.5H6.5C3.46243 19.5 1 17.0376 1 14V6Z"
        stroke={stroke}
      />
    </svg>
  )
}
