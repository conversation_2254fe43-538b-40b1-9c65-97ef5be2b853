import type { IconsProps } from './IconTypes'

export const NotificationIcon = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#101828',
  strokeWidth = '1.66667',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="bell">
        <path
          id="Icon"
          d="M11.8264 18.0001C11.6799 18.2526 11.4696 18.4623 11.2166 18.608C10.9636 18.7538 10.6767 18.8305 10.3848 18.8305C10.0928 18.8305 9.80592 18.7538 9.55291 18.608C9.2999 18.4623 9.08961 18.2526 8.9431 18.0001M15.3848 7.16675C15.3848 5.84067 14.858 4.5689 13.9203 3.63121C12.9826 2.69353 11.7108 2.16675 10.3848 2.16675C9.05868 2.16675 7.78691 2.69353 6.84923 3.63121C5.91155 4.5689 5.38477 5.84067 5.38477 7.16675C5.38477 13.0001 2.88477 14.6667 2.88477 14.6667H17.8848C17.8848 14.6667 15.3848 13.0001 15.3848 7.16675Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export const CardsIcon = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#101828',
  strokeWidth = '1.66667',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      {...rest}
    >
      <path
        d="M18.3337 8.33268H1.66699M9.16699 11.666H5.00033M1.66699 6.83268L1.66699 13.166C1.66699 14.0994 1.66699 14.5661 1.84865 14.9227C2.00844 15.2363 2.2634 15.4912 2.57701 15.651C2.93353 15.8327 3.40024 15.8327 4.33366 15.8327L15.667 15.8327C16.6004 15.8327 17.0671 15.8327 17.4236 15.651C17.7372 15.4912 17.9922 15.2363 18.152 14.9227C18.3337 14.5661 18.3337 14.0994 18.3337 13.166V6.83268C18.3337 5.89926 18.3337 5.43255 18.152 5.07603C17.9922 4.76243 17.7372 4.50746 17.4236 4.34767C17.0671 4.16602 16.6004 4.16602 15.667 4.16602L4.33366 4.16602C3.40024 4.16602 2.93353 4.16602 2.57701 4.34767C2.2634 4.50746 2.00844 4.76243 1.84865 5.07603C1.66699 5.43255 1.66699 5.89926 1.66699 6.83268Z"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const BackIcon = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#334155',
  strokeWidth = '1.66667',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      {...rest}
    >
      <path
        d="M12.5 15L7.5 10L12.5 5"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
