import type { IconsProps } from './IconTypes'

export const NotificationTarrifIcon = ({
  width = '52',
  height = '52',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 52 52"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g filter="url(#filter0_d_8476_5349)">
        <path
          d="M2.5 11C2.5 5.75329 6.75329 1.5 12 1.5H40C45.2467 1.5 49.5 5.75329 49.5 11V39C49.5 44.2467 45.2467 48.5 40 48.5H12C6.75329 48.5 2.5 44.2467 2.5 39V11Z"
          stroke="#EAECF0"
          shape-rendering="crispEdges"
        />
        <path
          d="M34 18C34 19.6569 30.4183 21 26 21C21.5817 21 18 19.6569 18 18M34 18C34 16.3431 30.4183 15 26 15C21.5817 15 18 16.3431 18 18M34 18V32C34 33.6569 30.4183 35 26 35C21.5817 35 18 33.6569 18 32V18M34 22.6666C34 24.3235 30.4183 25.6666 26 25.6666C21.5817 25.6666 18 24.3235 18 22.6666M34 27.33C34 28.9869 30.4183 30.33 26 30.33C21.5817 30.33 18 28.9869 18 27.33"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_8476_5349"
          x="0"
          y="0"
          width="52"
          height="52"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_8476_5349"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_8476_5349"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
