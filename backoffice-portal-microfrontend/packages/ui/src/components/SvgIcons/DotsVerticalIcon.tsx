import type { IconsProps } from './IconTypes'

export const DotsVerticalIcon = ({
  width = '36',
  height = '36',
  fill = 'none',
  stroke = '#475467',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 36 36"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M18.0003 18.8335C18.4606 18.8335 18.8337 18.4604 18.8337 18.0002C18.8337 17.5399 18.4606 17.1668 18.0003 17.1668C17.5401 17.1668 17.167 17.5399 17.167 18.0002C17.167 18.4604 17.5401 18.8335 18.0003 18.8335Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.0003 13.0002C18.4606 13.0002 18.8337 12.6271 18.8337 12.1668C18.8337 11.7066 18.4606 11.3335 18.0003 11.3335C17.5401 11.3335 17.167 11.7066 17.167 12.1668C17.167 12.6271 17.5401 13.0002 18.0003 13.0002Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.0003 24.6668C18.4606 24.6668 18.8337 24.2937 18.8337 23.8335C18.8337 23.3733 18.4606 23.0002 18.0003 23.0002C17.5401 23.0002 17.167 23.3733 17.167 23.8335C17.167 24.2937 17.5401 24.6668 18.0003 24.6668Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
