import type { IconsProps } from './IconTypes'

export const ChevronLeftIcon = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#FFFFFF',
  strokeWidth = '1.66667',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M12.5 15L7.5 10L12.5 5"
        stroke={stroke}
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
