'use client'
import { styled } from '@mui/material/styles'
import { Dialog, DialogProps } from '@mui/material'
import { StyledComponent } from '@emotion/styled'

export const CustomDialog: StyledComponent<DialogProps> = styled(Dialog)(
  ({ theme }) => ({
    '& .MuiDialogContent-root': {
      padding: theme.spacing(2),
    },
    '& .MuiDialogActions-root': {
      padding: theme.spacing(1),
    },
  })
)
