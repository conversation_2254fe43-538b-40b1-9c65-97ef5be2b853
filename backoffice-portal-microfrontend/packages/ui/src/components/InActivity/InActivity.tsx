'use client'
import React, { ReactNode, useEffect, useRef, useState } from 'react'
import {
  Box,
  Button,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Typography,
} from '@mui/material'
import { CloseRounded, ErrorOutlineOutlined } from '@mui/icons-material'

export const InActivity = ({
  children,
  isLoggedIn,
  timeout = 20 * 60 * 1000, // 20 minutes
  onLogout, // Optional callback to override default logout behavior,
}: {
  children: ReactNode
  isLoggedIn: () => void | boolean
  timeout?: number
  onLogout?: () => void
}) => {
  const authenticated = isLoggedIn()
  const timer = useRef<NodeJS.Timeout | null>(null)
  const [openPopup, setOpenPopup] = useState(false)

  const resetTimer = () => {
    if (timer.current) clearTimeout(timer.current)
    timer.current = setTimeout(showPopup, timeout)
  }

  const showPopup = () => {
    authenticated && setOpenPopup(true)
    timer.current = setTimeout(handleLogout, 10000)
  }

  const handleStayLoggedIn = () => {
    setOpenPopup(false)
    resetTimer()
  }

  const handleLogout = async () => {
    setOpenPopup(false)
    if (timer.current) clearTimeout(timer.current)

    // override default logout behavior,
    if (typeof onLogout === 'function') {
      onLogout()
      return
    }

    if (formRef.current) {
      formRef.current.submit()
    }
    localStorage.clear()
    sessionStorage.clear()
  }

  useEffect(() => {
    const events = [
      'load',
      'mousemove',
      'mousedown',
      'click',
      'scroll',
      'keypress',
      'touchstart',
      'touchmove',
      'touchend',
      'touchcancel',
    ]
    const resetTimerOnEvent = () => resetTimer()
    events.forEach((event) => window.addEventListener(event, resetTimerOnEvent))
    resetTimer()

    return () => {
      events.forEach((event) =>
        window.removeEventListener(event, resetTimerOnEvent)
      )
      if (timer.current) clearTimeout(timer.current)
    }
  }, [])
  const formRef = useRef<HTMLFormElement>(null)

  return (
    <>
      <form
        method="POST"
        ref={formRef}
        style={{ display: 'none' }}
        action={`${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/users/logout`}
      >
        <input
          type="hidden"
          name="redirectUrl"
          value={`${process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL}`}
        />
      </form>
      <Dialog open={openPopup}>
        <DialogTitle
          id="alert-dialog-title"
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
            }}
          >
            <ErrorOutlineOutlined
              fontSize="medium"
              sx={{
                color: '#D92D20',
                background: '#FEE4E2',
                borderRadius: 'var(--radius-full, 9999px)',
                display: 'flex',
                width: '48px',
                height: '48px',
                padding: '12px',
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: '16px',
              }}
            />
            <Typography variant="subtitle1">
              {'Are you still there?'}
            </Typography>
          </Box>

          <IconButton
            aria-label="close"
            onClick={handleStayLoggedIn}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseRounded />
          </IconButton>
        </DialogTitle>

        <DialogContent
          sx={{
            width: '400px',
          }}
        >
          <DialogContentText
            id="alert-dialog-description"
            sx={{
              color: '#475467',
              fontWeight: '400',
              fontSize: '14px',
              textAlign: 'center',
            }}
          >
            {
              "It looks like you've been inactive for a while. For your security, you’ll be logged out shortly. If you’re still here, click ‘Stay Logged In.'"
            }
          </DialogContentText>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: 'space-between',
          }}
        >
          <Button variant="outlined" onClick={handleLogout}>
            Logout
          </Button>
          <Button variant="contained" onClick={handleStayLoggedIn} autoFocus>
            Stay Logged In
          </Button>
        </DialogActions>
      </Dialog>

      {children}
    </>
  )
}
