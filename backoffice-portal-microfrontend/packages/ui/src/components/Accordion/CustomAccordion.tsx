'use client'
import { useState } from 'react'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  FormControl,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'

export interface IAccordionProps {
  title?: string
  variant?: string
  color?: string
  fontWeight?: string
  categories: ICategory[]
  onChange: (
    newValue: { id: number; name: string },
    selectedOption: string | number
  ) => void
}
export interface IDropDownProps {
  label: string
  options: { id: number; name: string; description: string }[]
  isOccupation?: boolean
  onChange: (
    newValue: { id: number; name: string },
    categoryId: string | number
  ) => void
}

export interface ICategory {
  id: string | number
  label: string
  options: { id: number; name: string; description?: string }[]
}

export const CustomAccordionWithDropdown = ({
  title,
  variant,
  color,
  fontWeight,
  categories,
  onChange,
}: IAccordionProps) => {
  return (
    <Stack>
      <Accordion>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1-content"
          id="panel1-header"
        >
          <Typography sx={{ color, fontWeight, variant }}>{title}</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {categories.map((category) => (
            <DropDownComponent
              key={category.id}
              label={category.label}
              options={category.options.map((option) => ({
                ...option,
                description: option.description ?? '',
              }))}
              isOccupation={category.id === 'occupation'}
              onChange={(newValue) => onChange(newValue, category.id)}
            />
          ))}
        </AccordionDetails>
      </Accordion>
    </Stack>
  )
}

export const DropDownComponent = ({
  label,
  options,
  isOccupation = false,
  onChange,
}: IDropDownProps) => {
  const [selectedOption, setSelectedOption] = useState<null | {
    id: number
    name: string
    description: string
  }>(null)

  const handleChange = (
    event: React.SyntheticEvent<Element, Event>,
    value: { id: number; name: string; description: string } | null
  ) => {
    setSelectedOption(value)
    if (value) {
      onChange(value, value?.id)
    }
  }

  return (
    <FormControl fullWidth margin={'normal'}>
      <Autocomplete
        value={selectedOption}
        onChange={handleChange}
        options={options}
        getOptionLabel={(option) =>
          isOccupation ? option.description : option.name
        }
        renderOption={(props, option) => (
          <li
            {...props}
            title={isOccupation ? option.description : option.name}
          >
            <span
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '13vw',
                display: 'block',
              }}
            >
              {isOccupation ? option.description : option.name}
            </span>
          </li>
        )}
        renderInput={(params) => (
          <TextField
            {...params}
            label={label}
            placeholder={`Select ${label}`}
            variant="outlined"
          />
        )}
      />
    </FormControl>
  )
}
