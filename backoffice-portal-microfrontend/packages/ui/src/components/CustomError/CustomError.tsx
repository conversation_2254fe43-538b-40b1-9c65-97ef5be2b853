/**
 * <AUTHOR> on 19/09/2024
 */
import { Box, Button } from '@mui/material'

export function CustomError({ reset }: { error: unknown; reset: () => void }) {
  return (
    <Box
      sx={{
        display: 'flex',
        flex: 'auto',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        height: '100vh',
      }}
    >
      <h2>Something went wrong!</h2>
      <Button onClick={() => reset()}>Try again</Button>
    </Box>
  )
}
