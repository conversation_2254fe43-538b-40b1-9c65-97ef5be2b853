/**
 * <AUTHOR> on 19/09/2024
 */

'use client'

import { Box } from '@mui/material'

import { CustomSkeleton } from './CustomSkeleton'

export const LoadingListsSkeleton = () => {
  return (
    <Box>
      <CustomSkeleton
        animation="wave"
        variant="text"
        width="100%"
        height="15vh"
      />
      <CustomSkeleton
        animation="wave"
        variant="rectangular"
        width="100%"
        height="60vh"
      />
    </Box>
  )
}

export const LoadingCardListSkeleton = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        gap: '20px',
        py: '3%',
        px: '3%',
      }}
    >
      <CustomSkeleton
        animation="wave"
        variant="rectangular"
        width="33%"
        height="30vh"
      />
      <CustomSkeleton
        animation="wave"
        variant="rectangular"
        width="33%"
        height="30vh"
      />
      <CustomSkeleton
        animation="wave"
        variant="rectangular"
        width="33%"
        height="30vh"
      />
    </Box>
  )
}

export const LoadingFullScreen = () => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: '20px',
        py: '1%',
        px: '2%',
      }}
    >
      <CustomSkeleton
        animation="wave"
        variant="rectangular"
        width="100%"
        height="10vh"
      />
      <CustomSkeleton
        animation="wave"
        variant="rectangular"
        width="100%"
        height="5vh"
      />
      <CustomSkeleton
        animation="wave"
        variant="rectangular"
        width="100%"
        height="80vh"
      />
    </Box>
  )
}
