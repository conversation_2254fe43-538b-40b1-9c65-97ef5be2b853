import { Button, CircularProgress } from '@mui/material'

interface LoadingButtonProps {
  width?: string
  height?: string
  size?: 'small' | 'medium' | 'large'
}
export const LoadingButton = ({ width = "auto", height = "auto", size = 'medium', ...rest}: LoadingButtonProps) => {
  return (
    <Button
      variant="contained"
      size={size}
      disabled
      fullWidth
      sx={{
        py: '1%',
        background: '#EAECF0',
        minWidth: 'auto',
        width: width,
        height: height,
        borderRadius: '6px',
        padding:
          size === 'small'
            ? '4px 10px'
            : size === 'medium'
              ? '6px 4rem'
              : size === 'large'
                ? '8px 22px'
                : '6px 16px',
      }}
      {...rest}
    >
      <CircularProgress color="primary" size={20} thickness={3.0} />
    </Button>
  )
}
