import { Stack, Typography } from '@mui/material'
import { ReactNode } from 'react'

export type EmptyPageProps = {
  title?: string
  message?: string
  action?: ReactNode
  bgUrl?: string
}

export const EmptyPage: React.FC<EmptyPageProps> = ({
  title = 'No records found',
  message = 'Refresh page to reload data',
  action,
  bgUrl = '/combo.svg',
}) => {
  return (
    <Stack justifyContent="center" alignItems="center" padding={3}>
      <Stack
        sx={{
          width: '480px',
          height: '300px',
          justifyContent: 'flex-end',
        }}
      >
        <Stack
          justifyContent="center"
          paddingBottom={4}
          alignItems="center"
          sx={{ gap: '32px' }}
        >
          {/* Header */}
          <Stack
            justifyContent="center"
            alignItems="center"
            sx={{
              gap: '8px',
              width: '80%',
            }}
          >
            <Typography fontWeight="600" variant="subtitle1">
              {title}
            </Typography>

            {message && (
              <Typography
                variant="subtitle3"
                sx={{
                  textAlign: 'center',
                }}
              >
                {message}
              </Typography>
            )}
          </Stack>

          {/* Buttons */}
          {action && action}
        </Stack>
      </Stack>
    </Stack>
  )
}
