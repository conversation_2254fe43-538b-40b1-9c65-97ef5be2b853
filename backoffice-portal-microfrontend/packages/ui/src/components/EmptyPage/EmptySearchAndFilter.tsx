import { Button, Stack, Typography } from '@mui/material'
import { FC } from 'react'

export type EmptySearchAndFilterProps = {
  message?: string
  onClick?: () => void
  additionalText?: string
}

export const EmptySearchAndFilter: FC<EmptySearchAndFilterProps> = ({
  message = 'No records found',
  onClick,
  additionalText,
}) => {
  return (
    <Stack
      sx={{
        height: '100%',
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '10px',
      }}
    >
      <Stack
        sx={{
          width: '480px',
          height: '480px',
          backgroundImage: 'url(/dashboard/combo.svg)',
          justifyContent: 'flex-end',
          gap: '45px',
        }}
      >
        <Stack
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
          }}
        ></Stack>
        <Stack
          sx={{
            justifyContent: 'center',
            alignItems: 'center',
            gap: '32px',
          }}
        >
          {/* Header */}
          <Stack
            sx={{
              justifyContent: 'center',
              alignItems: 'center',
              gap: '8px',
              width: '65%',
            }}
          >
            <Typography variant="subtitle1">{message}</Typography>
            {additionalText && (
              <Typography
                variant="subtitle3"
                sx={{
                  textAlign: 'center',
                }}
              >
                {additionalText}
              </Typography>
            )}
          </Stack>

          {/* Buttons */}
          {onClick && (
            <Stack
              sx={{
                justifyContent: 'center',
                flexDirection: 'row',
                gap: '20px',
                marginTop: '20px',
              }}
            >
              <Button variant="outlined" onClick={onClick}>
                Clear search
              </Button>
            </Stack>
          )}
        </Stack>
      </Stack>
    </Stack>
  )
}
