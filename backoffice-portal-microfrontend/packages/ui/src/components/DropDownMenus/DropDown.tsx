/**
 * <AUTHOR> on 19/09/2024
 */
'use client'
import { KeyboardArrowDownRounded, SearchRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Chip,
  ClickAwayListener,
  FormControlLabel,
  Grow,
  IconButton,
  List,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Radio,
  Stack,
  Typography,
} from '@mui/material'
import React, {
  FC,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { sentenceCase } from 'tiny-case'

import { CustomCheckBox } from '../CheckBox'
import { CustomStatusChip } from '../Chip'
import { CustomSearchInput } from '../Input'
import { CustomSkeleton } from '../Loading'
import { SecurityDetailsIcon } from '../SvgIcons'

export interface IFilterOption {
  key: string
  value: string
  label: string
}
interface IDropDownMenuRadio {
  menuItems: string[]
  onClick: (item: string) => void
  buttonVariant?: 'text' | 'outlined' | 'contained'
  buttonText: string
}

interface ITableDropDownMenu {
  menuItems: {
    label: string
    onClick: () => void
  }[]
  disabled: boolean
  buttonVariant?: 'text' | 'outlined' | 'contained'
  buttonText?: string
}

interface IDotsDropDownMenu {
  menuItems: {
    label: string
    onClick: () => void
  }[]
  // buttonVariant?: 'text' | 'outlined' | 'contained'
  // buttonText?: string
}

export const TableDropDownMenu: React.FC<ITableDropDownMenu> = (params) => {
  const { menuItems, buttonText, buttonVariant, disabled } = params
  const [open, setOpen] = React.useState(false)
  const anchorRef = React.useRef<HTMLButtonElement>(null)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }
  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }
    setOpen(false)
  }
  const handleListKeyDown = () => {}
  const prevOpen = React.useRef(open)

  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current!.focus()
    }

    prevOpen.current = open
  }, [open])
  return (
    <Box>
      <Button
        variant={buttonVariant || 'outlined'}
        sx={{
          height: '40px',
          width: '107px',
          textWrap: 'nowrap',
          padding: '9px 28px',
          borderRadius: '6px',
          border: '1px solid  #AAADB0',
          background: buttonVariant === 'contained' ? '#000A12' : '#FFF',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          gap: 0,
        }}
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        endIcon={<KeyboardArrowDownRounded />}
        disabled={disabled}
      >
        <Typography sx={{}}>{buttonText || 'Action'}</Typography>
      </Button>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                minWidth: '156px',
                marginTop: '22px',
                borderRadius: '8px',
                border: '1px solid #ECECEC',
                background: '#FFF',
                boxShadow:
                  '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList
                  autoFocusItem={open}
                  id="composition-menu"
                  aria-labelledby="composition-button"
                  onKeyDown={handleListKeyDown}
                >
                  {menuItems.map((item) => (
                    <MenuItem key={item.label} onClick={item.onClick}>
                      {item.label}
                    </MenuItem>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </Box>
  )
}

export const DropDownMenuRadio: React.FC<IDropDownMenuRadio> = ({
  menuItems,
  onClick,
  buttonVariant,
  buttonText,
}) => {
  const [open, setOpen] = React.useState(false)
  const anchorRef = React.useRef<HTMLButtonElement>(null)
  const [selectedIndex, setSelectedIndex] = React.useState(0)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }

  const handleClose = () => {
    setOpen(false)
    onClick('')
  }

  const handleMenuItemClick = (index: number) => {
    setSelectedIndex(index)
  }

  const prevOpen = React.useRef(open)

  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current!.focus()
    }

    prevOpen.current = open
  }, [open])

  return (
    <Box>
      <Button
        sx={{
          height: '40px',
          width: '156px',
          textWrap: 'nowrap',
          padding: '9px 28px',
          borderRadius: '4px',
          border: '1px solid  #AAADB0',
          background: buttonVariant === 'contained' ? '#000A12' : '#FFF',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        variant={buttonVariant}
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        endIcon={<KeyboardArrowDownRounded />}
      >
        <Typography
          variant="label1"
          sx={{
            color: buttonVariant === 'contained' ? '#FFF' : '#000A12',
          }}
        >
          {buttonText}
        </Typography>
      </Button>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                minWidth: '156px',
                marginTop: '22px',
                padding: '0px 0px 12px 0px',
                borderRadius: '8px',
                border: '1px solid #ECECEC',
                background: '#FFF',
                boxShadow:
                  '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setOpen(false)
                }}
              >
                <Stack>
                  {' '}
                  <MenuList
                    autoFocusItem={open}
                    id="composition-menu"
                    aria-labelledby="composition-button"
                  >
                    {menuItems.map((item, index) => (
                      <MenuItem
                        key={item}
                        onClick={() => handleMenuItemClick(index)}
                      >
                        <Radio
                          checked={index === selectedIndex}
                          onChange={() => {
                            setSelectedIndex(index)
                          }}
                          value={item}
                          name="radio-buttons-group"
                        />
                        <Chip label={item} />
                      </MenuItem>
                    ))}
                  </MenuList>
                </Stack>
              </ClickAwayListener>

              <Box
                sx={{
                  padding: '0px 20px',
                  display: 'flex',
                  gap: '13px',
                }}
              >
                <Button
                  variant="outlined"
                  sx={{
                    height: '34px',
                    width: '96px',
                  }}
                  onClick={handleClose}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    height: '34px',
                    width: '96px',
                  }}
                  onClick={() => {
                    onClick(menuItems[selectedIndex])
                    setOpen(false)
                  }}
                >
                  Apply
                </Button>
              </Box>
            </Paper>
          </Grow>
        )}
      </Popper>
    </Box>
  )
}

interface IDropDownCheckBoxWithSearch {
  label: string
  key: string
  onClick: (selectedFilters: IFilterOption[]) => void
  filters: IFilterOption[]
  selectedFilter?: IFilterOption[]
  open: boolean
  handleOpenMenu: () => void
  onClear?: boolean
  setOpen: (open: boolean) => void
}

const useDropdownMenuCheckBoxWithSearch = (
  filters: IFilterOption[],
  initialSelectedFilters: IFilterOption[]
) => {
  const [search, setSearch] = useState('')
  const [selectedFilters, setSelectedFilters] = useState<IFilterOption[]>(
    initialSelectedFilters
  )

  const filtered = useMemo(() => {
    if (search === '') return filters
    return filters.filter((option) =>
      option.label.toLowerCase().includes(search.toLowerCase())
    )
  }, [filters, search])

  const handleSelect = useCallback((selectedFilter: IFilterOption) => {
    setSelectedFilters((prev) =>
      prev.some((filter) => filter.key === selectedFilter.key)
        ? prev.filter((filter) => filter.key !== selectedFilter.key)
        : [...prev, selectedFilter]
    )
  }, [])

  const handleSearch = useCallback((search: string) => {
    setSearch(search)
  }, [])

  return {
    search,
    selectedFilters,
    filtered,
    setSelectedFilters,
    handleSelect,
    handleSearch,
  }
}

const SearchInput: React.FC<{
  value: string
  onChange: (search: string) => void
}> = ({ value, onChange }) => (
  <CustomSearchInput
    startAdornment={<SearchRounded sx={{ color: 'black' }} />}
    placeholder="Search"
    sx={{
      width: '100%',
      '&.Mui-focused': {
        width: '100%',
        boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
      },
    }}
    value={value}
    onChange={(e) => onChange(e.target.value)}
  />
)

const FilterList: React.FC<{
  filters: IFilterOption[]
  selectedFilters: IFilterOption[]
  onSelect: (filter: IFilterOption) => void
}> = ({ filters, selectedFilters, onSelect }) => (
  <MenuList
    sx={{
      padding: 0,
      margin: 0,
      width: '100%',
      height: '100%',
      overflowY: 'auto',
      '&::-webkit-scrollbar': {
        width: '6px',
        marginLeft: 2,
      },
      '&::-webkit-scrollbar-track': {
        backgroundColor: 'lightgray transparent',
        padding: '0px 4px',
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: 'darkgray',
        borderRadius: '10px',
      },
    }}
  >
    {filters.map((option) => (
      <MenuItem
        key={option.key}
        sx={{ margin: 0, padding: '6px', width: '100%' }}
      >
        <FormControlLabel
          control={
            <CustomCheckBox
              sx={{ minWidth: '40px', height: '30px', padding: '2px 8px' }}
              checked={selectedFilters.some(
                (filter) => filter.key === option.key
              )}
              onChange={() => onSelect(option)}
            />
          }
          label={
            <CustomStatusChip
              sx={{ height: '30px' }}
              label={<Typography variant="label2">{option.label}</Typography>}
            />
          }
        />
      </MenuItem>
    ))}
  </MenuList>
)

export const DropdownMenuCheckBoxWithSearch: React.FC<
  IDropDownCheckBoxWithSearch
> = ({
  label,
  onClick,
  filters,
  selectedFilter = [],
  open,
  handleOpenMenu,
  setOpen,
}) => {
  const anchorRef = useRef<HTMLButtonElement>(null)
  const {
    search,
    selectedFilters,
    filtered,
    setSelectedFilters,
    handleSelect,
    handleSearch,
  } = useDropdownMenuCheckBoxWithSearch(filters, selectedFilter)

  const handleApply = () => {
    onClick(selectedFilters)
    handleOpenMenu()
  }
  useEffect(() => {
    setSelectedFilters(selectedFilter || [])
  }, [selectedFilter])
  const handleCancel = () => {
    setSelectedFilters([])
    onClick([])
    handleOpenMenu()
  }

  return (
    <Box key={label}>
      <Button
        variant="outlined"
        endIcon={<KeyboardArrowDownRounded />}
        ref={anchorRef}
        onClick={handleOpenMenu}
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        id="composition-button"
      >
        <Typography variant="label1" sx={{ textWrap: 'nowrap' }}>
          {selectedFilters.length > 0
            ? `${label}: ${selectedFilters
                .slice(0, 2)
                .map((filt) => filt.label)
                .join(', ')}`
            : label}
        </Typography>
        {selectedFilters.length > 2 && (
          <Chip
            label={`+ ${selectedFilters.length - 2}`}
            // sx={{ minWidth: '29px', height: '20px' }}
          />
        )}
      </Button>
      <Popper open={open} anchorEl={anchorRef.current} placement="bottom-start">
        <Paper
          sx={{
            minWidth: '259px',
            maxHeight: '50vh',
            padding: '5% 4% 4% 4%',
            background: '#FFF',
            borderRadius: '6px',
            display: 'flex',
            flexDirection: 'column',
            gap: '14px',
            justifyContent: 'flex-start',
            alignItems: 'flex-start',
          }}
        >
          <SearchInput value={search} onChange={handleSearch} />
          <FilterList
            filters={filtered}
            selectedFilters={selectedFilters}
            onSelect={handleSelect}
          />
          <ClickAwayListener
            onClickAway={() => {
              // setOpen(false)
            }}
          >
            <Stack>
              <Stack
                sx={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  width: '100%',
                  margin: 0,
                  padding: 0,
                  gap: '5%',
                }}
              >
                <Button
                  variant="outlined"
                  sx={{
                    width: '100%',
                    height: '34px',
                    border: '1px solid #AAADB0',
                  }}
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  variant="contained"
                  sx={{ width: '100%', height: '34px' }}
                  onClick={handleApply}
                >
                  Apply
                </Button>
              </Stack>
            </Stack>
          </ClickAwayListener>
        </Paper>
      </Popper>
    </Box>
  )
}

interface IDropMenu {
  menuItems: Array<{
    label: string
    id: string
  }>
  onSelect: (item: string) => void
  buttonVariant?: 'text' | 'outlined' | 'contained'
  onButtonClick?: (setOpen: React.Dispatch<SetStateAction<boolean>>) => void
  buttonText: string
  loading?: boolean
}

export const DropDownMenu: FC<IDropMenu> = ({
  menuItems,
  onSelect,
  onButtonClick,
  buttonVariant,
  buttonText,
  loading,
}) => {
  const [open, setOpen] = React.useState(false)
  const anchorRef = React.useRef<HTMLButtonElement>(null)

  const handleToggle = () => {
    onButtonClick && onButtonClick(setOpen)
    // setOpen((prevOpen) => !prevOpen)
  }

  const handleMenuItemClick = (item: string) => {
    onSelect && onSelect(item)
    setOpen(false)
  }

  const prevOpen = React.useRef(open)

  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current!.focus()
    }

    prevOpen.current = open
  }, [open])
  return (
    <Box>
      <Button
        sx={{
          height: '40px',
          width: '156px',
          textWrap: 'nowrap',
          padding: '9px 28px',
          borderRadius: '4px',
          border: '1px solid  #AAADB0',
          background: buttonVariant === 'contained' ? '#000A12' : '#FFF',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        variant={buttonVariant}
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        endIcon={<KeyboardArrowDownRounded />}
      >
        <Typography
          variant="label1"
          sx={{
            color: buttonVariant === 'contained' ? '#FFF' : '#000A12',
          }}
        >
          {buttonText}
        </Typography>
      </Button>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                minWidth: '156px',
                marginTop: '6px',
                padding: '0px 0px 12px 0px',
                borderRadius: '8px',
                border: '1px solid #ECECEC',
                background: '#FFF',
                boxShadow:
                  '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setOpen(false)
                }}
              >
                <Stack>
                  {' '}
                  <List
                    id="composition-menu"
                    aria-labelledby="composition-button"
                    sx={{
                      overflowY: 'auto',
                      maxHeight: '40vh',
                      display: 'flex',
                      flexDirection: 'column',
                      padding: '6px',
                    }}
                  >
                    {(!loading || loading === undefined) &&
                      menuItems.map((item, index) => (
                        <Button
                          key={item.id || index}
                          sx={{
                            height: '40px',
                            textWrap: 'nowrap',
                            padding: '10px',
                          }}
                          onClick={() => handleMenuItemClick(item.id)}
                        >
                          <Typography>{sentenceCase(item.label)}</Typography>
                        </Button>
                      ))}
                    {loading &&
                      Array.from({ length: 5 }).map((_, index) => (
                        <CustomSkeleton
                          key={index}
                          variant="rectangular"
                          sx={{
                            height: '40px',
                            width: '200px',
                            marginTop: '10px',
                          }}
                        />
                      ))}
                  </List>
                </Stack>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </Box>
  )
}

export const DotsDropdown: React.FC<IDotsDropDownMenu> = ({ menuItems }) => {
  const [open, setOpen] = React.useState(false)
  const anchorRef = React.useRef<HTMLButtonElement>(null)

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen)
  }
  const handleClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorRef.current &&
      anchorRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }
    setOpen(false)
  }
  const handleListKeyDown = () => {}
  const prevOpen = React.useRef(open)

  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current!.focus()
    }

    prevOpen.current = open
  }, [open])
  return (
    <Box>
      <IconButton
        sx={{
          '&:hover': { backgroundColor: '#fff' },
        }}
        ref={anchorRef}
        id="composition-button"
        aria-controls={open ? 'composition-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
      >
        <SecurityDetailsIcon />
      </IconButton>
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              elevation={0}
              sx={{
                minWidth: '2vw',
                marginTop: '0.5rem',
                padding: '0',
                borderRadius: '8px',
                border: '1px solid #ECECEC',
                background: '#FFF',
                boxShadow:
                  '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <MenuList
                  autoFocusItem={open}
                  id="composition-menu"
                  aria-labelledby="composition-button"
                  onKeyDown={handleListKeyDown}
                >
                  {menuItems.map((item) => (
                    <MenuItem key={item.label} onClick={item.onClick}>
                      {item.label}
                    </MenuItem>
                  ))}
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </Box>
  )
}
