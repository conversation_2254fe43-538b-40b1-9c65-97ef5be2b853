import {
  ChevronLeftRounded,
  ChevronRightRounded,
  KeyboardArrowDownRounded,
} from '@mui/icons-material'
import {
  Box,
  Button,
  ClickAwayListener,
  Fade,
  IconButton,
  Paper,
  Popper,
  Stack,
  SxProps,
  TextField,
  Typography,
} from '@mui/material'
import { CalendarIcon } from '@mui/x-date-pickers'
import dayjs, { Dayjs } from 'dayjs'
import React, { ReactNode, useState } from 'react'

interface IDatePicker {
  buttonText?: string
  buttonIcon?: ReactNode
  onApplyDateRange: (date: { start: Dayjs; end: Dayjs }) => void
  size?: 'small' | 'medium' | 'large'
  currentStartDate?: Dayjs
  currentEndDate?: Dayjs
}

export const DateRangePicker: React.FC<IDatePicker> = ({
  buttonText,
  buttonIcon,
  onApplyDateRange,
  size,
  currentStartDate,
  currentEndDate,
}) => {
  const anchorRef = React.useRef<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState<boolean>(false)
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs())
  const [startDate, setstartDate] = useState<Dayjs | null>(
    currentStartDate || dayjs().subtract(1, 'month')
  )
  const [endDate, setEndDate] = useState<Dayjs | null>(
    currentEndDate || dayjs()
  )

  const [calenderView, setCalenderView] = useState<boolean>(true)
  const [monthView, setMonthView] = useState<boolean>(false)

  const isStartOrEndDate = (day: Dayjs) => {
    return day.isSame(startDate, 'day') || day.isSame(endDate, 'day')
  }

  const isDateBetween = (day: Dayjs) => {
    if (!startDate || !endDate) return false
    // Check if the day is after startDate and before endDate
    return day.isAfter(startDate, 'day') && day.isBefore(endDate, 'day')
  }
  const handleDateClicked = (clickedDate: Dayjs) => {
    if (!startDate || (startDate && endDate)) {
      setstartDate(clickedDate.startOf('day'))
      setEndDate(null)
    } else if (startDate && !endDate) {
      if (clickedDate.isBefore(startDate, 'day')) {
        setEndDate(startDate.endOf('day'))
        setstartDate(clickedDate.startOf('day'))
      } else {
        setEndDate(clickedDate.endOf('day'))
      }
    }
  }
  // month state setter
  const setNextMonth = () => {
    setCurrentDate(currentDate.clone().add(1, 'month'))
  }

  const setPrevMonth = () => {
    setCurrentDate(currentDate.clone().subtract(1, 'month'))
  }

  const handleClick = () => {
    setOpen((prev) => !prev)
  }

  const CalenderView = () => {
    //
    return (
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(7, 0fr)',
          gap: '0px',
          padding: '0px',
          width: '100%',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
          <Box
            sx={{
              textAlign: 'center',
              fontWeight: 'bold',
            }}
            key={day}
          >
            {day}
          </Box>
        ))}
        {generateCalendar()}
      </Box>
    )
  }

  const MonthView = () => {
    return (
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 0fr)',
          gap: '0px',
          padding: '3px',
          width: '100%',
          height: '200px',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {[
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ].map((month, index) => (
          <Button
            sx={{
              textAlign: 'center',
              fontWeight: 'bold',
              color: 'black',
            }}
            key={month}
            variant="text"
            onClick={() => {
              setCurrentDate(currentDate.month(index))
              setMonthView(false)
              setCalenderView(true)
            }}
          >
            {month}
          </Button>
        ))}
      </Box>
    )
  }

  const YearView = () => {
    return (
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 0fr)',
          gap: '0px',
          padding: '3px',
          width: '100%',
          height: '200px',
          justifyContent: 'space-between',
          alignItems: 'center',
          overflowY: 'auto',
          overflowX: 'hidden',
          marginLeft: '2px',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'lightgray transparent',
            padding: '0px 4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'darkgray',
            borderRadius: '10px',
          },
        }}
      >
        {/* creaate years date from current year to the last 70yrs */}
        {Array.from({ length: 70 }, (_, k) => dayjs().year() - k).map(
          (year) => (
            <Button
              sx={{
                textAlign: 'center',
                fontWeight: 'bold',
                color: 'black',
              }}
              key={year}
              variant="text"
              onClick={() => {
                setCurrentDate(currentDate.year(year))
                setMonthView(true)
                setCalenderView(false)
              }}
            >
              {year}
            </Button>
          )
        )}
      </Box>
    )
  }
  const DateBox = ({
    key,
    sx,
    date,
    onClick,
  }: {
    key: string
    sx: SxProps
    date: Dayjs
    onClick?: () => void
  }) => {
    return (
      <Box sx={sx} key={key} onClick={onClick}>
        <Box
          style={{
            padding: 0,
            height: '100%',
            width: '100%',
            backgroundColor: isStartOrEndDate(date) ? 'black' : 'inherit',
            borderRadius: isStartOrEndDate(date) ? '50%' : 'none',
            color: isStartOrEndDate(date) ? 'white' : 'inherit',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {date.format('D')}
        </Box>
      </Box>
    )
  }

  const generateCalendar = () => {
    const startOfMonth = currentDate.startOf('month').startOf('week')
    const endOfMonth = currentDate.endOf('month').endOf('week')
    const days = []
    let day = startOfMonth

    while (day.isBefore(endOfMonth.add(1, 'day'))) {
      days.push(day)
      day = day.add(1, 'day')
    }

    return days.map((day) => (
      <DateBox
        key={day.format('YYYY-MM-DD')}
        sx={{
          textAlign: 'center',
          padding: isStartOrEndDate(day) ? 0 : '10px',
          width: '50px',
          height: '50px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: '#f5f5f5',
          },
          // borderRadius: '5px solid',
          borderRadiusLeft: day.isAfter(startDate, 'D') ? '50%' : 'none',
          borderRadiusRight: day.isBefore(endDate, 'D') ? '50%' : 'none',
          color: isStartOrEndDate(day) ? 'white' : 'inherit',
          backgroundColor: isStartOrEndDate(day)
            ? '#f5f5f5'
            : isDateBetween(day)
              ? '#f5f5f5'
              : 'inherit',
        }}
        onClick={() => handleDateClicked(day)}
        date={day}
      />
    ))
  }

  return (
    <>
      <Button
        variant="outlined"
        endIcon={
          buttonText ? (
            buttonIcon ? (
              buttonIcon
            ) : (
              <KeyboardArrowDownRounded />
            )
          ) : (
            <></>
          )
        }
        ref={anchorRef}
        aria-controls={open ? 'date-range-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        id="date-range-button"
        onClick={handleClick}
        sx={{
          height: '42px',
          minWidth: '130px',
          backgroundColor: '#FFF',
          display: 'flex',
          borderRadius: '6px',
          border: '1.5px solid #D0D5DD',
        }}
      >
        {buttonText ? (
          <Typography
            sx={{
              color: 'black',
              fontWeight: '400',
            }}
          >
            {buttonText}{' '}
          </Typography>
        ) : (
          <Box
            sx={{
              display: 'flex',
              gap: '4px',
              alignItems: 'center',
            }}
          >
            <CalendarIcon />{' '}
            <Typography
              sx={{
                textWrap: 'nowrap',
              }}
            >
              {startDate && endDate
                ? `${startDate.format('MMM D, YYYY')} - ${endDate.format(
                    'MMM D, YYYY'
                  )}`
                : 'Select Date Range'}
            </Typography>
          </Box>
        )}
      </Button>
      <Popper
        id="date-range-menu"
        placement="bottom-start"
        anchorEl={anchorRef.current}
        open={open}
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={350}>
            <Paper
              sx={{
                maxWidth: size === 'small' ? '300px' : '400px',
                marginTop: '-150px',
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setOpen(false)
                }}
              >
                <Stack>
                  {' '}
                  <Box
                    component={'div'}
                    sx={{
                      padding: '20px 28px',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '16px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: '4px',
                      }}
                    >
                      <TextField
                        size="small"
                        sx={{
                          width: '100%',
                        }}
                        value={startDate ? startDate.format('MMMM D, YYYY') : ''}
                        inputProps={{ readonly: true }}
                        InputLabelProps={{ shrink: true }}
                      />{' '}
                      -
                      <TextField
                        size="small"
                        sx={{
                          width: '100%',
                        }}
                        value={endDate ? endDate.format('MMMM D, YYYY') : ''}
                        inputProps={{ readonly: true }}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Box>

                    {/* Month selector */}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: '4px',
                      }}
                    >
                      <IconButton
                        onClick={setPrevMonth}
                        sx={{
                          padding: '8px',
                          width: '28px',
                          height: '28px',
                        }}
                      >
                        {/*  set previous month of displayed date */}
                        <ChevronLeftRounded
                          sx={{
                            fontSize: '20px',
                          }}
                        />
                      </IconButton>

                      <Box
                        sx={{
                          display: 'flex',
                          gap: '4px',
                          color: '#000000',
                        }}
                      >
                        <Button
                          sx={{
                            paddingRight: '4px',
                            color: 'black',
                          }}
                          onClick={() => {
                            setMonthView(true)
                            setCalenderView(false)
                          }}
                        >
                          {currentDate.format('MMMM D,')}
                        </Button>
                        <Button
                          sx={{
                            paddingLeft: '0',
                            color: 'black',
                          }}
                          onClick={() => {
                            setMonthView(false)
                            setCalenderView(false)
                          }}
                        >
                          {currentDate.format('YYYY')}
                        </Button>
                      </Box>

                      <IconButton
                        onClick={setNextMonth}
                        sx={{
                          padding: '8px',
                          width: '28px',
                          height: '28px',
                        }}
                      >
                        {/*  set next month of displayed date */}
                        <ChevronRightRounded
                          sx={{
                            fontSize: '20px',
                          }}
                        />
                      </IconButton>
                    </Box>
                    {/* calender view grid */}
                    {calenderView ? (
                      <CalenderView />
                    ) : monthView ? (
                      <MonthView />
                    ) : (
                      <YearView />
                    )}
                  </Box>
                </Stack>
              </ClickAwayListener>

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  padding: '16px',
                  gap: '16px',
                }}
              >
                <Button
                  variant="outlined"
                  sx={{
                    padding: '8px 16px',
                    textTransform: 'none',
                    width: '100%',
                    border: '1px solid ',
                  }}
                  onClick={() => {
                    setEndDate(null)
                    setstartDate(null)
                    setOpen(false)
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    padding: '8px 16px',
                    textTransform: 'none',
                    width: '100%',
                  }}
                  onClick={() => {
                    if (
                      startDate !== null &&
                      endDate !== null &&
                      endDate.isAfter(startDate)
                    ) {
                      const dateRange = {
                        start: startDate,
                        end: endDate,
                      }
                      onApplyDateRange(dateRange)
                      setOpen(false)
                    }
                  }}
                >
                  Apply
                </Button>
              </Box>
            </Paper>
          </Fade>
        )}
      </Popper>
    </>
  )
}
