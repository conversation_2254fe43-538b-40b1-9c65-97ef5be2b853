/**
 * <AUTHOR> on 07/10/2024
 */
'use client'
import { Breadcrumbs, Typography } from '@mui/material'
import { HomeIcon } from '../SvgIcons'
import NavigateNextIcon from '@mui/icons-material/NavigateNext'
import { usePathname } from 'next/navigation'
import Link from 'next/link'


{/* App Breadcrum */}
export const AppBreadcrumb = () => {
  const pathName = usePathname()
  const pathNames = pathName.split('/')
  //Remove the first empty path
  pathNames.shift()

  return (
    <Breadcrumbs
      aria-label="breadcrumb"
      sx={{ textTransform: 'capitalize' }}
      separator={<NavigateNextIcon color={'disabled'} fontSize="small" />}
    >
      {pathNames.map((name, index) => {
        const first = index === 0
        const last = index === pathNames.length - 1
        const to = `/${pathNames.slice(0, index + 1).join('/')}`
        const home = `/${pathNames.slice(0, index + 1).join('/')}`

        return first ? (
          <Link
            key={index}
            color="inherit"
            href={home}
            style={{ display: 'flex' }}
          >
            <HomeIcon />
          </Link>
        ) : last ? (
          <Typography key={index} sx={{ color: 'primary.main' }}>
            {name}
          </Typography>
        ) : (
          <Link key={index} href={to} style={{ textDecoration: 'none' }}>
            <Typography sx={{ color: 'text.main' }}>{name}</Typography>
          </Link>
        )
      })}
    </Breadcrumbs>
  )
}

