import { Close } from '@mui/icons-material'
import { Box, Grow, IconButton } from '@mui/material'
import Image from 'next/image'
import React from 'react'

export const IDView = (
  {
    open,
    setDocumentViewer
  }: {
    open: boolean
    setDocumentViewer: (props: {open: boolean, imageUrl: string}) => void
  }
) => {
  return (
    <>
      {open && (
        <Box
          sx={{
            width: '100vw',
            height: '100vh',
            zIndex: 1000,
            backgroundColor: 'rgba(49, 45, 45, 0.5)',
            position: 'fixed',
            top: 0,
            color: 'whitesmoke',
          }}
        >
          <IconButton
            onClick={() => {
              setDocumentViewer({ open: false, imageUrl: '' })
            }}
            sx={{
              background: '#F1F5F9',
              position: 'absolute',
              top: '85px',
              right: '63px',
              height: '57px',
              width: '57px',
              '&:hover': {
                color: 'white',
                border: '1px solid white',
              },

              zIndex: 1010,
            }}
          >
            <Close
              sx={{
                width: '32px',
                height: '32px',
              }}
            />
          </IconButton>
          {/* document space */}
          <Box
            sx={{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Box
              sx={{
                width: 'auto',
                height: 'auto',
                borderRadius: '10px',
                display: 'flex',
                padding: '10%',
                maxWidth: '80%',
                maxHeight: '80%',
                background: 'transparent',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Grow
                in={open}
                style={{ transformOrigin: '0 0 0' }}
                {...(open ? { timeout: 1000 } : {})}
              >
                <Image
                  src={'https://www.ohmancorp.com/images/ss/Color_TN.jpg'}
                  alt={'image-viewer'}
                  layout="fill"
                  style={{
                    padding: '7%',
                  }}
                  objectFit="contain"
                />
              </Grow>
            </Box>
          </Box>
        </Box>
      )}
    </>
  )
}
