'use client'
import { CloseRounded } from '@mui/icons-material'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { SetStateAction, useEffect, useState } from 'react'

import { ConfirmCancelSave } from './ConfirmCancelSave'
import { CustomCheckBox } from '../../CheckBox'
import { LoadingButton } from '../../Loading'

export interface IDialog {
  open: boolean
  setOpen: React.Dispatch<SetStateAction<boolean>>
  title: string
  subtitle?: string
  buttonText: string
  buttonProps?: {
    color: string
  }
  isLoading?: boolean
  descriptionText: string
  onClick?: (reasons: string[]) => void
  reasons?: string[] // Add reasons as a prop
  concatReason?: boolean
}

export const CustomDialog = ({
  open,
  setOpen,
  onClick,
  subtitle,
  buttonText,
  buttonProps,
  isLoading,
  descriptionText,
  title,
  reasons = [], // Use reasons prop
  concatReason = false,
}: IDialog) => {
  const [reason, setReason] = useState<string>('')
  const [isOther, setIsOther] = useState(false)
  const [isConfirmCancelOpen, setIsConfirmCancelOpen] = useState(false)
  const [hasTouched, setHasTouched] = useState(false)
  const [error, setError] = useState<string>('')
  const handleKeyDown = (event: React.KeyboardEvent) => {
    event.stopPropagation() // Stop the event from propagating up to the parent elements
  }
  const [selectedReasons, setSelectedReasons] = useState<boolean[]>(
    new Array(reasons.length).fill(false) // Initialize based on the reasons prop length
  )
  const [isAnySelected, setIsAnySelected] = useState(false)

  const handleSelection = (index: number, reason: string) => {
    const updatedSelection = [...selectedReasons]
    updatedSelection[index] = !updatedSelection[index]
    setSelectedReasons(updatedSelection)
    const anySelected = updatedSelection.some((selected) => selected)
    setIsAnySelected(anySelected)
    setIsOther(reason === 'Other' && updatedSelection[index])
  }

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const input = event.target.value
    setReason(input)
    setHasTouched(true)
    if (!validateReason(input)) {
      setError(
        'Reason must be at least 10 characters and contain only valid characters.'
      )
    } else {
      setError('')
    }
  }
  const validateReason = (reason: string) => {
    const trimmedReason = reason.trim()
    return trimmedReason.length >= 10 && /^[\s\S]*$/.test(trimmedReason)
  }

  let selectedReasonsList = reasons.filter(
    (_reason, index) => selectedReasons[index]
  )
  if (reason) {
    selectedReasonsList = selectedReasonsList.filter(
      (reason) => reason !== 'Other'
    )
    selectedReasonsList.push(reason)
  }

  const handleCancel = () => {
    if (reason || isAnySelected) {
      setIsConfirmCancelOpen(true)
    } else {
      setReason('')
      setIsOther(false)
      setIsConfirmCancelOpen(false)
      setHasTouched(false)
      setError('')
      setSelectedReasons(new Array(reasons.length).fill(false))
      setIsAnySelected(false)
      setOpen(false)
    }
  }
  useEffect(() => {
    if (!open) {
      setReason('')
      setIsOther(false)
      setHasTouched(false)
      setError('')
      setSelectedReasons(new Array(reasons.length).fill(false))
      setIsAnySelected(false)
    }
  }, [open, reasons.length])

  const isActionDisabled =
    !isAnySelected || (isOther && (!reason || !validateReason(reason)))

  return (
    <Dialog open={open} maxWidth={'sm'}>
      <Stack
        direction="row"
        sx={{
          justifyContent: 'space-between',
          alignItems: 'center',
          px: '5%',
          py: '1%',
          borderBottom: '2px solid #F2F4F7',
          background: '#F9FAFB',
        }}
      >
        <Stack direction="column">
          <Typography sx={{ fontWeight: 700 }}>{title} </Typography>
          {subtitle && (
            <Typography sx={{ fontSize: '14px' }}>{subtitle}</Typography>
          )}
        </Stack>
        <IconButton
          sx={{
            border: '0.8px solid #CBD5E1',
          }}
          onClick={handleCancel}
        >
          <CloseRounded fontSize="small" />
        </IconButton>
      </Stack>
      <DialogContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '20px',
        }}
      >
        <Typography
          sx={{
            textWrap: 'wrap',
          }}
        >
          {descriptionText}
        </Typography>
        {!isAnySelected &&
          reasons &&
          reasons.map((reason, index) => (
            <Stack
              key={reason}
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              sx={{
                border: selectedReasons[index]
                  ? '2px solid #555C61'
                  : '2px solid #D0D5DD',
                borderRadius: '6px',
                padding: '1px',
              }}
            >
              <Typography sx={{ padding: '0 4px' }}>{reason}</Typography>
              <CustomCheckBox
                checked={selectedReasons[index]}
                onChange={() => handleSelection(index, reason)}
                disabled={!selectedReasons[index] && isAnySelected}
              />
            </Stack>
          ))}
        {isAnySelected && (
          <TextField
            fullWidth
            onKeyDown={handleKeyDown}
            multiline
            rows={4}
            placeholder="Type your reason here"
            value={reason}
            onChange={handleChange}
            error={Boolean(hasTouched && error)}
            helperText={hasTouched && error ? error : ''}
          />
        )}
        <Stack justifyContent={'space-between'} direction="row">
          <Button
            variant="outlined"
            sx={{
              width: '45%',
            }}
            onClick={handleCancel}
          >
            Cancel
          </Button>
          {isLoading ? (
            <LoadingButton />
          ) : (
            <Button
              variant="contained"
              disabled={isActionDisabled}
              sx={{
                background: (buttonProps && buttonProps.color) || '#EB0045',
                border: isActionDisabled
                  ? ''
                  : `1.5px solid ${(buttonProps && buttonProps.color) || '#EB0045'}`,
                '&:hover': {
                  background: (buttonProps && buttonProps.color) || '#EB0045 ',
                  opacity: '0.5',
                },
                width: '45%',
                textWrap: 'nowrap',
              }}
              onClick={() => {
                if (onClick) {
                }
                onClick && onClick(selectedReasonsList)
                setOpen(false)
              }}
            >
              {buttonText}
            </Button>
          )}
        </Stack>
      </DialogContent>
      <ConfirmCancelSave
        open={isConfirmCancelOpen}
        onConfirmCancel={() => {
          setIsConfirmCancelOpen(false)
          setOpen(false)
        }}
        onClose={() => {
          setIsConfirmCancelOpen(false)
        }}
        onConfirmSubmit={() => {
          setIsConfirmCancelOpen(false)
          onClick && onClick(selectedReasonsList)
          setOpen(false)
        }}
      />
    </Dialog>
  )
}
