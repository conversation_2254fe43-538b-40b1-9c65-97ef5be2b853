import {
  Box,
  TableCell,
  TableHead,
  TableRow,
  TableSortLabel,
} from '@mui/material'
import { visuallyHidden } from '@mui/utils'
import React from 'react'
import { CustomCheckBox } from '../CheckBox'
import { CustomTableCell } from './CustomTableCell'

export type TableHeaderProps = {
  order: 'asc' | 'desc'
  orderBy?: string
  rowCount: number
  headLabel: IHeadCell[]
  numSelected: number
  onRequestSort?: (event: React.MouseEvent<unknown>, property: string) => void
  onSelectAllClick?: (
    event: React.ChangeEvent<HTMLInputElement>,
    checked: boolean
  ) => void
  showCheckbox?: boolean
}
interface IHeadCell {
  id?: string
  label?: string
  alignRight?: boolean
  alignCenter?: boolean
}

export const CustomTableHeader = ({
  order,
  orderBy,
  rowCount,
  headLabel,
  numSelected,
  onSelectAllClick,
  showCheckbox,
  onRequestSort,
}: TableHeaderProps) => {
  const createSortHandler =
    (property: string) => (event: React.MouseEvent<unknown>) => {
      onRequestSort?.(event, property)
    }

  return (
    <TableHead sx={{ background: '#F9FAFB' }}>
      <TableRow>
        {showCheckbox && (
          <TableCell padding="checkbox">
            <CustomCheckBox
              indeterminate={numSelected > 0 && numSelected < rowCount}
              checked={rowCount > 0 && numSelected === rowCount}
              onChange={onSelectAllClick}
              style={{
                padding: '5px',
              }}
            />
          </TableCell>
        )}
        {headLabel.map((headCell) => (
          <CustomTableCell
            key={headCell.id}
            align={
              headCell.alignRight
                ? 'right'
                : headCell.alignCenter
                  ? 'center'
                  : 'left'
            }
            sortDirection={orderBy === headCell.id ? order : false}
          >
            <TableSortLabel
              hideSortIcon
              active={orderBy === headCell.id}
              direction={orderBy === headCell.id ? order : 'asc'}
              onClick={createSortHandler(headCell.id ?? '')}
            >
              {headCell.label}
              {orderBy === headCell.id ? (
                <Box sx={{ ...visuallyHidden }}>
                  {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                </Box>
              ) : null}
            </TableSortLabel>
          </CustomTableCell>
        ))}
      </TableRow>
    </TableHead>
  )
}
