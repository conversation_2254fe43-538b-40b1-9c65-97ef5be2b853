'use client'
import React, { FC } from 'react'
import PropTypes from 'prop-types'
import Tabs, { TabsOwnProps } from '@mui/material/Tabs'
import Tab, { TabOwnProps } from '@mui/material/Tab'
import { styled } from '@mui/material/styles'



export function CustomTabPanel({ children, value, index, ...other }: {
  [x: string]: unknown
  children: React.ReactNode
  value: number
  index: number
}) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`scrollable-auto-tabpanel-${index}`}
      aria-labelledby={`scrollable-auto-tab-${index}`}
      {...other}
    >
      {value === index && <div>{children}</div>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
}

export const CustomToggleTabs: FC<TabsOwnProps> = styled(Tabs)(({ theme }) => ({
  marginLeft: theme.spacing(0),
  marginRight: theme.spacing(0),
  marginBottom: theme.spacing(0),
  '& .MuiTabs-indicator': {
    display: 'none',
  },
  border: '2px solid #D0D5DD',
  borderRadius: '6px',
  height: 40,
}))

export const CustomAntTab: FC<TabOwnProps> = styled((props: TabOwnProps) => (
  <Tab disableRipple {...props} />
))(({ theme }) => ({
  textTransform: 'none',
  minWidth: 0,
  [theme.breakpoints.up('sm')]: {
    minWidth: 0,
  },
  fontWeight: '500',
  textWrap: 'nowrap',
  width: '50%',
  height: 40,
  color: '#667085',
  fontSize: '14px',
  lineHeight: '10px',
  marginRight: theme.spacing(0),

  '&:hover': {
    color: '#667085',
    opacity: 1,
    // backgroundColor: 'red',
  },
  '&.Mui-selected': {
    color: '#2A3339',
    fontWeight: theme.typography.fontWeightBold,
    borderRadius: '5px',
    border: '1px solid #D0D5DD',
    backgroundColor: '#F9FAFB',
  },
  '&.Mui-focusVisible': {
    backgroundColor: '#d1eaff',
  },
}))
