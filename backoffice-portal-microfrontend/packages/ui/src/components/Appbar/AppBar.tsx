/**
 * <AUTHOR> on 20/09/2024
 */

'use client'
import {
  AppBar,
  Box,
  Chip,
  IconButton,
  Link,
  Menu,
  MenuItem,
  Paper,
  Toolbar,
  Tooltip,
} from '@mui/material'
import React, { useEffect, useRef, useState } from 'react'
import AccountCircleIcon from '@mui/icons-material/AccountCircle'

import { LogoPlain } from '../SvgIcons'
interface IDecodeToken {
  last_name: string
  first_name: string
  user_id: string
  authorities: string[]
  sub: string
  iat: number
  exp: number
  resources?: IResource[]
}

interface IResource {
  resourceType: string
  resourceIds: string[]
}

export interface INavbarProps {
  profile: IDecodeToken
  refreshToken: () => void
}

export const Navbar = ({ profile, refreshToken }: INavbarProps) => {
  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget)
  }
  const formRef = useRef<HTMLFormElement>(null)

  const handleCloseUserMenu = () => {
    setAnchorElUser(null)
  }
  const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null)
  useEffect(() => {
    const refresh = setInterval(refreshToken, 1000 * 60 * 3)
    return () => clearInterval(refresh)
  }, [])

  const handleLogout = (
    e: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLLIElement>
  ) => {
    e.preventDefault()
    formRef.current?.submit()
    //clear storage
    localStorage.clear()
    sessionStorage.clear()
  }
  return (
    <AppBar
      position="static"
      sx={{
        boxShadow: 'none',
        backgroundColor: '#FCFCFC',
        borderBottom: '1px solid #E3E4E4',
      }}
    >
      <Paper
        elevation={0}
        sx={{
          paddingLeft: '1%',
          paddingRight: '2%',
          borderBottom: '1px solid #E3E4E4',
        }}
      >
        <Toolbar
          disableGutters
          sx={{
            display: 'flex',
            flexDirection: 'column',
            border: 'none',
            boxShadow: 'none',
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              width: '100%',
              height: '100%',
              gap: '10px',
            }}
          >
            <Box
              sx={{
                width: '243px',
                height: '100%',
                display: 'flex',
              }}
            >
              <Link href={'/landing/'}>
                <LogoPlain />
              </Link>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'flex-end',
                gap: '10px',
                width: '50%',
                height: '100%',
                alignItems: 'center',
                marginTop: '10px',
              }}
            >
              <Tooltip title="Open profile">
                <IconButton
                  onClick={handleOpenUserMenu}
                  sx={{
                    p: 0,
                    borderRadius: '16px',
                  }}
                >
                  <Chip
                    icon={
                      <AccountCircleIcon
                        sx={{
                          color: '#000A12',
                          fontSize: 32,
                          marginLeft: '0px !important',
                        }}
                      />
                    }
                    label={`${(profile && profile.first_name) || ''} ${
                      (profile && profile.last_name) || ''
                    }`}
                    sx={{
                      fontWeight: '500',
                      fontSize: '14px',
                      backgroundColor: '#EAECF0',
                      gap: '8px',
                    }}
                  />
                </IconButton>
              </Tooltip>
              <form
                method="POST"
                ref={formRef}
                action={`${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/users/logout`}
              >
                <input
                  type="hidden"
                  name="redirectUrl"
                  value={`${process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL}`}
                />
                <Menu
                  sx={{
                    mt: '45px',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                  id="menu-appbar"
                  anchorEl={anchorElUser}
                  anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  keepMounted
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                  }}
                  open={Boolean(anchorElUser)}
                  onClose={handleCloseUserMenu}
                >
                  <MenuItem
                    component="button"
                    type="submit"
                    onClick={(e) => handleLogout(e)}
                  >
                    Logout
                  </MenuItem>
                </Menu>
              </form>
            </Box>
          </Box>
        </Toolbar>
      </Paper>
    </AppBar>
  )
}
