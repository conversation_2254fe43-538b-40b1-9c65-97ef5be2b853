'use client'

import { KeyboardArrowDownRounded } from '@mui/icons-material'
import {
  Box,
  Button,
  Chip,
  ClickAwayListener,
  Grow,
  InputBase,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Typography,
} from '@mui/material'
import React from 'react'

interface CustomSearchByInputProps<T> {
  searchByDropDownItems: { label: string; value: Array<keyof T> }[]
  width: string
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
  value?: string
  searchByValue: { label: string; value: Array<keyof T> }
  onChange?: (searchText: string) => void
  onSearchButtonClick?: () => void
  onSearchBySelect?: (value: { label: string; value: Array<keyof T> }) => void
  placeholder?: string
}

export const CustomSearchByInput = <T,>({
  searchByDropDownItems,
  width,
  onChange,
  value,
  onKeyDown,
  onSearchBySelect,
  placeholder,
}: CustomSearchByInputProps<T>) => {
  const [openSub, setOpenSub] = React.useState(false)
  const anchorSubRef = React.useRef<HTMLButtonElement>(null)
  const [searchBy, setSearchBy] = React.useState<{
    label: string
    value: Array<keyof T>
  }>({
    label: searchByDropDownItems[0]?.label,
    value: searchByDropDownItems[0]?.value,
  })
  const [buttonWidth, setButtonWidth] = React.useState(0)

  const handleToggleSub = (e: React.MouseEvent<HTMLButtonElement>) => {
    setOpenSub((prevOpenSub) => !prevOpenSub)
    setButtonWidth(e.currentTarget.offsetWidth)
  }

  const handleStatusClose = (event: Event | React.SyntheticEvent) => {
    if (
      anchorSubRef.current &&
      anchorSubRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }

    setOpenSub(false)
  }

  function handleListKeyDown(event: React.KeyboardEvent) {
    if (event.key === 'Tab') {
      event.preventDefault()
      setOpenSub(false)
    } else if (event.key === 'Escape') {
      setOpenSub(false)
    }
  }
  const prevOpenSub = React.useRef(openSub)
  React.useEffect(() => {
    if (prevOpenSub.current === true && openSub === false) {
      anchorSubRef.current!.focus()
    }

    prevOpenSub.current = openSub
  }, [openSub])

  React.useEffect(() => {
    onSearchBySelect && onSearchBySelect(searchBy)
  }, [searchBy])
  return (
    <Paper
      component="form"
      elevation={0}
      sx={{
        p: '0px',
        display: 'flex',
        alignItems: 'center',
        width,
        gap: '19px',
        height: '42px',
        justifyContent: 'space-between',
        border: '1px solid #D0D5DD !important',
        borderRadius: '4px',
        boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        backgroundColor: '#FFFFFF',
      }}
    >
      {/* dropdown */}
      <Box>
        <Button
          sx={{
            height: '42px',
            minWidth: '156px',
            textWrap: 'nowrap',
            padding: '9px 28px',
            borderRadius: '4px 0px  0px 4px',
            border: '1px solid  #AAADB0',
            background: ' #FFF',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            '&.active': {
              backgroundColor: '#EDEEEE',
            },
          }}
          variant="outlined"
          ref={anchorSubRef}
          id="composition-button"
          aria-controls={openSub ? 'composition-menu' : undefined}
          aria-expanded={openSub ? 'true' : undefined}
          aria-haspopup="true"
          onClick={handleToggleSub}
          endIcon={<KeyboardArrowDownRounded />}
        >
          <Typography variant="label1">{`Search by ${searchBy.label.split(' ')[0]}`}</Typography>
        </Button>
        <Popper
          open={openSub}
          anchorEl={anchorSubRef.current}
          role={undefined}
          placement="bottom-start"
          transition
          disablePortal
          sx={{
            zIndex: '2000',
          }}
        >
          {({ TransitionProps, placement }) => (
            <Grow
              {...TransitionProps}
              style={{
                transformOrigin:
                  placement === 'bottom-start' ? 'left top' : 'left bottom',
              }}
            >
              <Paper
                sx={{
                  width: buttonWidth,
                  marginTop: '22px',
                  padding: '0px 0px 12px 0px',
                  borderRadius: '8px',
                  border: '1px solid #ECECEC',
                  background: ' #FFF',
                  boxShadow:
                    '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
                }}
              >
                <ClickAwayListener onClickAway={handleStatusClose}>
                  <MenuList
                    autoFocusItem={openSub}
                    id="composition-menu"
                    aria-labelledby="composition-button"
                    onKeyDown={handleListKeyDown}
                  >
                    {searchByDropDownItems.map((item, index) => (
                      <MenuItem
                        key={index}
                        onClick={(e) => {
                          handleStatusClose(e)
                          const value = {
                            label: item.label,
                            value: item.value,
                          }
                          setSearchBy(value)
                        }}
                      >
                        <Chip
                          label={item.label}
                          sx={{
                            padding: '2px 8px',
                            background: '#E3E4E4',
                            height: '20px',
                          }}
                        />
                      </MenuItem>
                    ))}
                  </MenuList>
                </ClickAwayListener>
              </Paper>
            </Grow>
          )}
        </Popper>
      </Box>
      <InputBase
        id="search-box"
        sx={{ border: 'none', width: '100%', boxShadow: 'none' }}
        placeholder={placeholder}
        inputProps={{ 'aria-label': 'search component' }}
        value={value}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
          onChange && onChange(e.target.value)
        }}
        onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
          if (e.key === 'Enter') {
            e.preventDefault()
            onKeyDown && onKeyDown(e)
          }
        }}
      />
    </Paper>
  )
}
