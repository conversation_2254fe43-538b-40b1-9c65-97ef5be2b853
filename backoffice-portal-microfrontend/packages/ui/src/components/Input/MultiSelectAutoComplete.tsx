import React from 'react'
import {
  Autocomplete,
  FormControl,
  IconButton,
  InputAdornment,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { SearchRounded } from '@mui/icons-material'
import { IconClose } from '../SvgIcons'



interface MultiSelectAutocompleteProps<T> {
  label: string
  options: T[]
  selectedItems: T[]
  getOptionLabel: (option: T) => string
  onChange: (event: unknown, newValue: T[]) => void
  onDelete: (item: T) => void
  isOptionEqualToValue: (option: T, value: T) => boolean
  renderOption?: (
    props: React.HTMLAttributes<HTMLLIElement>,
    option: T
  ) => React.ReactNode
}

export const MultiSelectAutocomplete = <T,>({
  label,
  options,
  selectedItems,
  getOptionLabel,
  onChange,
  onDelete,
  isOptionEqualToValue,
  renderOption,
}: MultiSelectAutocompleteProps<T>) => {
  return (
    <FormControl fullWidth margin="normal">
      <Typography variant="label3" sx={{ mt: 2 }}>
        {label}
      </Typography>
      &nbsp;
      <Autocomplete
        multiple
        options={options}
        value={selectedItems}
        getOptionLabel={getOptionLabel}
        isOptionEqualToValue={isOptionEqualToValue}
        onChange={onChange}
        renderTags={(value, getTagProps) =>
          value.map((option, index) => (
            <Stack direction={'row'} key={index}>
              <Stack
                key={index}
                direction="row"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  height: '1.25rem',
                  minWidth: '50%',
                  marginRight: '4px',
                  marginBottom: '4px',
                }}
              >
                <Stack
                  direction="row"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '2px',
                    borderRadius: '4px',
                    border: '1px solid #D0D5DD',
                    height: '1.25rem',
                    padding: '2px 11px 2px 7px',
                  }}
                >
                  <IconButton
                    size="small"
                    {...getTagProps({ index })}
                    onClick={() => onDelete(option)}
                  >
                    <IconClose />
                  </IconButton>
                  <Typography>{getOptionLabel(option)}</Typography>
                </Stack>
              </Stack>
            </Stack>
          ))
        }
        renderOption={renderOption}
        renderInput={(params) => (
          <TextField
            {...params}
            inputProps={{
              ...params.inputProps,
              startAdornment: (
                <InputAdornment position="start">
                  <SearchRounded />
                </InputAdornment>
              ),
            }}
          />
        )}
      />
    </FormControl>
  )
}
