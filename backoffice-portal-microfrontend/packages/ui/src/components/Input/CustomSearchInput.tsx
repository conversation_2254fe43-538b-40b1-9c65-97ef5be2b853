'use client'

import {
  Box,
  InputBase,
  OutlinedInput,
  OutlinedInputProps,
  SxProps,
  Theme,
  styled,
} from '@mui/material'
import { StyledComponent } from '@emotion/styled'
import React from 'react'

interface CustomSearchInputProps extends OutlinedInputProps {
  width?: string
  height?: string | number
  bgColor?: string
}

export const CustomSearchInput: StyledComponent<CustomSearchInputProps> =
  styled(OutlinedInput)<CustomSearchInputProps>(
    ({ theme, width = '400px', bgColor, height = 40 }) => ({
      width: width,
      height: height,
      transition: theme.transitions.create(['box-shadow', 'width'], {
        easing: theme.transitions.easing.easeInOut,
        duration: theme.transitions.duration.shorter,
      }),
      boxShadow: '0px 1px 2px 0px #1018280D',
      '&.Mui-focused': {
        boxShadow: '0px 0px 0px 2px rgba(211, 207, 220, 0.15)',
      },
      '& fieldset': {
        borderRadius: '8px',
        border: `2px solid ${bgColor || '#D0D5DD'} !important`,
      },
    })
  )

CustomSearchInput.displayName = 'CustomSearchInput'

interface CustomSearchBoxProps<T> {
  startAdornment?: React.ReactNode
  endAdornment?: React.ReactNode
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
  value?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
  onSearchButtonClick?: () => void
  placeholder?: string
  sx?: SxProps<Theme>
}

export const CustomSearchBox = <T,>({
  startAdornment,
  endAdornment,
  onKeyDown,
  value,
  onChange,
  placeholder,
  sx,
}: CustomSearchBoxProps<T>) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: '#FFFFFF',
        border: '1px solid #D0D5DD',
        borderRadius: '4px',
        padding: '4px 8px',
        ...sx,
      }}
    >
      {startAdornment && <Box sx={{ marginRight: 1 }}>{startAdornment}</Box>}
      <InputBase
        fullWidth
        placeholder={placeholder}
        value={value}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange?.(e)}
        onKeyDown={onKeyDown}
        sx={{ flex: 1 }}
      />
      {endAdornment && <Box sx={{ marginLeft: 1 }}>{endAdornment}</Box>}
    </Box>
  )
}
