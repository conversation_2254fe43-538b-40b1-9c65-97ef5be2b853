'use client'
import { FormControlLabel, FormControlLabelProps, styled } from '@mui/material'
import { StyledComponent } from '@emotion/styled'

export const CustomFormControlLabel: StyledComponent<FormControlLabelProps> =
  styled((props: FormControlLabelProps) => <FormControlLabel {...props} />)(
    ({ theme }) => ({
      '&.MuiFormControlLabel-root': {
        justifyContent: 'space-between',
        borderRadius: '4px',
        border: '1px solid #E3E4E4',
        margin: 0,
        paddingRight: '0.8rem',
      },
      '.MuiFormControlLabel-label': {
        color: theme.palette.primary.main,
        fontWeight: 400,
        marginLeft: '10px',
        fontSize: '15px',
      },
    })
  )
