'use client'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import {
  Button,
  Chip,
  ClickAwayListener,
  Grow,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Radio,
  Stack,
  Typography,
} from '@mui/material'
import React from 'react'
import { MouseEvent, useRef, useState } from 'react'

interface SearchDropdownProps {
  setIsLabelSelected: (value: boolean) => void
}

export const SearchDropdown = ({ setIsLabelSelected }: SearchDropdownProps) => {
  const dropDownItems = [
    { label: 'search for profiles', value: ['profiles'] },
    { label: 'search for accounts', value: ['accountNumber'] },
  ]

  const [openSub, setOpenSub] = useState(false)
  const anchorSubRef = useRef<HTMLButtonElement>(null)
  const [buttonWidth, setButtonWidth] = useState(0)
  const [searchLabel, setSearchLabel] = useState(dropDownItems[0])
  const [selectedItem, setSelectedItem] = useState(dropDownItems[0])

  const handleToggleSub = (e: React.MouseEvent<HTMLButtonElement>) => {
    setOpenSub((prevOpenSub) => !prevOpenSub)
    setButtonWidth(e.currentTarget.offsetWidth)
  }

  const handleStatusClose = (event: Event | React.SyntheticEvent) => {
    event.stopPropagation()
    event.preventDefault()
    if (
      anchorSubRef.current &&
      anchorSubRef.current.contains(event.target as HTMLElement)
    ) {
      return
    }

    setOpenSub(false)
  }

  const handleCancel = (e: MouseEvent<HTMLButtonElement>) => {
    setSelectedItem(searchLabel)
    setOpenSub(false)
    handleToggleSub(e)
  }

  const handleApply = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    setSearchLabel(selectedItem)

    // Trigger the label selection status based on the selected item
    if (selectedItem && selectedItem.value[0] === 'accountNumber') {
      setIsLabelSelected(true)
      setOpenSub(false)
    } else {
      setIsLabelSelected(false)
      setOpenSub(false)
    }

    setOpenSub(false)
  }

  function handleListKeyDown(event: React.KeyboardEvent) {
    if (event.key === 'Tab') {
      event.preventDefault()
      setOpenSub(false)
    } else if (event.key === 'Escape') {
      setOpenSub(false)
    }
  }

  return (
    <Button
      sx={{
        height: '42px',
        minWidth: buttonWidth,
        textWrap: 'nowrap',
        borderRadius: '0px 4px  4px 0px',
        border: '1px solid  #AAADB0',
        background: ' #FFF',
        backgroundColor: '#EDEEEE',
        boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        '&.active': {
          backgroundColor: '#EDEEEE',
        },
      }}
      variant="outlined"
      ref={anchorSubRef}
      id="composition-button"
      aria-controls={openSub ? 'composition-menu' : undefined}
      aria-expanded={openSub ? 'true' : undefined}
      aria-haspopup="true"
      onClick={handleToggleSub}
      endIcon={<KeyboardArrowDownRounded />}
    >
      <Typography variant="label1">{`Search for ${searchLabel && searchLabel.label.split(' ').slice(2).join()}`}</Typography>
      <Popper
        open={openSub}
        anchorEl={anchorSubRef.current}
        role={undefined}
        placement="bottom-start"
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps, placement }) => (
          <Grow
            {...TransitionProps}
            style={{
              transformOrigin:
                placement === 'bottom-start' ? 'left top' : 'left bottom',
            }}
          >
            <Paper
              sx={{
                width: {
                  xs: '36vw',
                  sm: '31vw',
                  md: '21vw',
                  lg: '16vw',
                },
                marginTop: '2%',
                padding: '0px 0px 12px 0px',
                borderRadius: '8px',
                border: '1px solid #ECECEC',
                background: ' #FFF',
                boxShadow:
                  '0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)',
              }}
            >
              <ClickAwayListener onClickAway={handleStatusClose}>
                <MenuList
                  autoFocusItem={openSub}
                  id="composition-menu"
                  aria-labelledby="composition-button"
                  onKeyDown={handleListKeyDown}
                >
                  {dropDownItems.map((item, index) => (
                    <MenuItem
                      key={index}
                      onClick={(e) => {
                        e.stopPropagation()
                        const value = {
                          label: item.label,
                          value: item.value,
                        }
                        setSelectedItem(value)
                      }}
                    >
                      <Radio
                        checked={
                          selectedItem && selectedItem.label === item.label
                        }
                        value={item.label}
                        sx={{ padding: '0 8px 0 0' }}
                      />
                      <Chip
                        label={item.label}
                        sx={{
                          padding: '2px 8px',
                          background: '#E3E4E4',
                          height: '20px',
                        }}
                      />
                    </MenuItem>
                  ))}
                  <Stack
                    direction="row"
                    justifyContent={'space-between'}
                    padding={'0 8px'}
                  >
                    <Button
                      variant="outlined"
                      sx={{
                        textWrap: 'noWrap',
                        width: '6vw',
                        padding: '0px',
                      }}
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      sx={{
                        textWrap: 'noWrap',
                        width: '6vw',
                      }}
                      onClick={(e) => handleApply(e)}
                    >
                      Apply
                    </Button>
                  </Stack>
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </Button>
  )
}
