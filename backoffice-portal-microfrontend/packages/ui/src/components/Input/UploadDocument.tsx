'use client'

import { DeleteOutlineOutlined, VisibilityOutlined } from '@mui/icons-material'
import CloseIcon from '@mui/icons-material/Close'
import {
  <PERSON>ton,
  DialogContent,
  DialogTitle,
  IconButton,
  Paper,
  Stack,
  Typography,
} from '@mui/material'
import React, { useCallback, useState } from 'react'

import { PdfIcon } from '../SvgIcons'
import { UploadIcon } from '../SvgIcons'
import { CustomDialog } from '../Dialogs'

interface FileUploadProps {
  name: string
  initialFileName?: string
  initialFile?: File | string
  onFileUpload: (file: File | null) => void
}

export const UploadDocumentForm = ({
  name,
  initialFileName,
  initialFile,
  onFileUpload,
}: FileUploadProps) => {
  const [fileName, setFileName] = useState(initialFileName || '')
  const [file, setFile] = useState<File | null | string>(
    initialFile || null || ''
  )
  const [dragOver, setDragOver] = useState(false)

  const handleDragOver = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault()
    setDragOver(false)
  }, [])

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLElement>) => {
      event.preventDefault()
      setDragOver(false)
      if (event.dataTransfer.files && event.dataTransfer.files[0]) {
        setFileName(event.dataTransfer.files[0].name)
        setFile(event.dataTransfer.files[0])
        onFileUpload(event.dataTransfer.files[0])
      }
    },
    [onFileUpload]
  )

  const handleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.files && event.target.files[0]) {
        setFileName(event.target.files[0].name)
        setFile(event.target.files[0])
        onFileUpload(event.target.files[0])
      }
    },
    [onFileUpload]
  )
  const handleRemoveFile = () => {
    setFileName('')
    setFile(null)
    onFileUpload(null)
  }
  return (
    <Paper
      onDragOver={handleDragOver}
      elevation={dragOver ? 4 : 0}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      sx={{
        borderRadius: '12px',
        display: 'flex',
        border: '1px solid #EAECF0',
        background: '#FFF',
        cursor: 'pointer',
        px: '4%',
        py: '2%',
        alignItems: 'center',
        flexDirection: 'row',
        alignContent: 'center',
        justifyContent: fileName || file ? 'space-between' : 'center',
      }}
    >
      <input
        accept="application/pdf"
        style={{ display: 'none', cursor: 'pointer' }}
        id={`file-upload-${name}`}
        type="file"
        name={name}
        onChange={handleChange}
      />
      <label htmlFor={`file-upload-${name}`}>
        {fileName || file ? (
          <Stack
            flexDirection="row"
            sx={{
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <PdfIcon />
            <Typography>{name}</Typography>
          </Stack>
        ) : (
          <Stack
            sx={{
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <UploadIcon />
            <Typography variant="subtitle2" color="text.primary">
              <span
                style={{
                  fontWeight: 600,
                }}
              >
                Click to Upload
              </span>{' '}
              or Drag and drop
            </Typography>
            <Typography variant="subtitle2">PDF (max size 10MB)</Typography>
          </Stack>
        )}
      </label>
      {(fileName || file) && (
        <Stack direction="row">
          <DeleteDocument handleDelete={handleRemoveFile} />
          <PreviewDocument
            file={file as File}
            isExisting={!!initialFile}
          />
        </Stack>
      )}
    </Paper>
  )
}
interface IPreviewProps {
  file: File | string
  isExisting: boolean
}
const PreviewDocument = (props: IPreviewProps) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [srcUrl, setSrcUrl] = useState('')
  const handlePreview = () => {
    if (isPreviewOpen) {
      setSrcUrl('')
      setIsPreviewOpen(false)
    } else {
      setIsPreviewOpen(true)
      if (props.file) {
        if (props.isExisting) {
          setSrcUrl(props.file as string)
        } else {
          const reader = new FileReader()
          reader.onload = (e) => {
            e.target && setSrcUrl(e.target.result as string)
          }
          reader.readAsDataURL(props.file as File)
        }
      }
    }
  }
  return (
    <>
      <IconButton onClick={handlePreview}>
        <VisibilityOutlined color="primary" />
      </IconButton>
      <CustomDialog
        open={isPreviewOpen}
        onClose={handlePreview}
        maxWidth={'lg'}
        fullWidth
      >
        <Stack
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <DialogTitle
            sx={{
              fontSize: '16px',
              fontWeight: 600,
              py: '1%',
            }}
          >
            {'File'}
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={handlePreview}
            sx={{
              position: 'absolute',
              right: 8,
              top: 4,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <DialogContent>
          <iframe
            src={srcUrl}
            style={{ width: '100%', height: '80vh', border: 'none' }}
          />
        </DialogContent>
      </CustomDialog>
    </>
  )
}

const DeleteDocument = (props: { handleDelete: () => void }) => {
  const [open, setOpen] = useState<boolean>(false)
  const handleClose = (reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  return (
    <>
      <IconButton onClick={() => setOpen(!open)}>
        <DeleteOutlineOutlined color="primary" />
      </IconButton>
      <CustomDialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
        <Stack
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
            flexDirection: 'row',
          }}
        >
          <DialogTitle
            sx={{
              fontWeight: 600,
              py: '1%',
            }}
          >
            <DeleteOutlineOutlined color="error" />
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={() => handleClose('close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 4,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
        <DialogContent>
          <Typography
            variant="subtitle1"
            sx={{
              color: 'text.primary',
              fontSize: '18px',
              fontWeight: 600,
            }}
          >
            Delete Document
          </Typography>
          <Typography variant="subtitle2">
            Are you sure you want to delete this uploaded form? This action
            cannot be undone.
          </Typography>
        </DialogContent>
        <Stack
          sx={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            gap: '2%',
            py: '2%',
            px: '4%',
          }}
        >
          <Button
            variant="outlined"
            fullWidth
            onClick={() => handleClose('close')}
          >
            Cancel
          </Button>
          <Button variant="contained" fullWidth onClick={props.handleDelete}>
            Confirm
          </Button>
        </Stack>
      </CustomDialog>
    </>
  )
}
