'use client'
import {
  Button,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
} from '@mui/material'
import React, { FC, useState } from 'react'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import CloseIcon from '@mui/icons-material/Close'
import {
  FilterListOffRounded,
  FilterListRounded,
  KeyboardArrowDownRounded,
} from '@mui/icons-material'
import { CustomSearchInput } from './CustomSearchInput'
import { DropdownMenuCheckBoxWithSearch } from '../DropDownMenus'

type FilterType = 'select' | 'dropdown/checkbox' | 'dropdown/single' | 'date'
interface IFilterOptions {
  key: string
  value: string
  label: string
}
export interface IFilterOption {
  key: string
  value: string
  label: string
}
interface IFilter {
  filterName: string
  options: IFilterOptions[]
  type: FilterType
}
export interface CustomFilterBoxProps {
  openFilter: boolean
  setOpenFilter: (value: boolean) => void
  searchValue: string
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  filters: IFilter[]
  searchByValues?: Array<{
    label: string
    value: string
  }>
  onFilterChange: (filters: Record<string, string | string[]>) => void
  setSearchByValue?: (value: string) => void
}
export const CustomerModuleSearchFilterBox = (props: CustomFilterBoxProps) => {
  const {
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    searchByValues,
    onFilterChange,
    setSearchByValue,
  } = props
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string | string[]>
  >({})
  const [checkBoxValue, setCheckBoxValue] = useState<IFilterOption[]>([])
  const [openDropDownCheckbox, setOpenDropDownCheckbox] = useState<
    string | null
  >(null)

  const handleFilterChange = (
    filterName: string,
    selectedOption: string | string[]
  ) => {
    const newFilters = {
      ...selectedFilters,
      [filterName]: selectedOption,
    }
    setSelectedFilters(newFilters)
    onFilterChange(newFilters)
  }
  const handleFilterOpen = () => {
    setOpenFilter(!openFilter)
    // setSelectedFilters({})
    // onFilterChange({})
  }
  const handleClearAll = () => {
    setOpenFilter(false)
    setSelectedFilters({})
    onFilterChange({})
    setCheckBoxValue([])
    setOpenDropDownCheckbox(null)
  }
  const handleDropdownCheckboxOpen = (filterName: string) => {
    setOpenDropDownCheckbox((prev) => (prev === filterName ? null : filterName))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          gap: '10px',
        }}
      >
        {searchByValues && setSearchByValue ? (
          <SearchByValuesBox
            searchValue={searchValue}
            handleSearch={handleSearch}
            searchByValues={searchByValues}
            setSearchByValue={setSearchByValue}
          />
        ) : (
          <CustomSearchInput
            value={searchValue}
            onChange={handleSearch}
            placeholder="Search"
            startAdornment={
              <InputAdornment position="start">
                <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            }
          />
        )}

        <Button
          variant="outlined"
          sx={{
            height: '40px',
            gap: 0,
            boxShadow: openFilter
              ? '0px 1px 2px 0px rgba(16, 24, 40, 0.05)'
              : '0px 1px 2px 0px #1018280D',
            border: openFilter ? '1.5px solid #555C61' : '1.5px solid #D0D5DD',
          }}
          startIcon={
            !openFilter ? <FilterListOffRounded /> : <FilterListRounded />
          }
          onClick={handleFilterOpen}
        >
          <Typography
            sx={{
              textWrap: 'nowrap',
            }}
          >
            {openFilter ? 'Hide Filters' : 'Show Filters'}
          </Typography>
        </Button>
      </Stack>
      <Stack
        sx={{
          display: openFilter ? 'flex' : 'none',
          flexDirection: 'row',
          gap: '10px',
          mt: '10px',
        }}
      >
        <Button
          variant="text"
          sx={{
            width: '6vw',
            color: '#555C61',
            textWrap: 'nowrap',
            height: '36px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            borderRadius: '6px',
            gap: 1,
            fontSize: '15px',
            fontWeight: 500,
          }}
          onClick={handleClearAll}
          endIcon={<CloseIcon />}
        >
          Clear All
        </Button>
        {filters.map((filter) => {
          return filter.type === 'select' ? (
            <FormControl
              key={filter.filterName}
              sx={{
                width: '40%',
              }}
            >
              <InputLabel
                id="demo-simple-select-outlined-label"
                sx={{
                  background: '#fcfcfc',
                  padding: '0 4px',
                  marginLeft: '-4px',
                }}
              >
                {filter.filterName}
              </InputLabel>
              <Select
                labelId="demo-simple-select-outlined-label"
                id="demo-simple-select-outlined"
                key={filter.filterName}
                sx={{
                  border: '1px solid #AAADB0',
                  height: '40px',
                }}
                value={selectedFilters[filter.filterName] || ''}
                onChange={(e) =>
                  handleFilterChange(
                    filter.filterName,
                    e.target.value as string | string[]
                  )
                }
              >
                {filter.options.map((option) => (
                  <MenuItem key={option.key} value={option.label}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          ) : filter.type === 'dropdown/checkbox' ? (
            <DropdownMenuCheckBoxWithSearch
              label={filter.filterName}
              key={filter.filterName}
              onClick={(selectedFilters: IFilterOption[]) => {
                if (selectedFilters.length >= 0) {
                  handleFilterChange(
                    filter.filterName,
                    selectedFilters.map((filt) => filt.key)
                  )
                  setCheckBoxValue(selectedFilters)
                }
              }}
              filters={filter.options}
              selectedFilter={checkBoxValue}
              open={openDropDownCheckbox === filter.filterName}
              handleOpenMenu={() =>
                handleDropdownCheckboxOpen(filter.filterName)
              }
              setOpen={(open) => {
                if (!open) handleDropdownCheckboxOpen('')
              }}
            />
          ) : filter.type === 'dropdown/single' ? (
            <DropdownMenu
              filter={filter}
              onSelected={(str: string) => {
                handleFilterChange(filter.filterName, str)
              }}
              selectedFilterValue={selectedFilters[filter.filterName] as string}
            />
          ) : (
            <></>
          )
        })}
      </Stack>
    </Stack>
  )
}

interface IDropdownMenu {
  filter: IFilter
  onSelected: (str: string) => void
  selectedFilterValue?: string
}
const DropdownMenu: FC<IDropdownMenu> = ({
  filter,
  onSelected,
  selectedFilterValue,
}) => {
  const disabledValue = 'none'
  return (
    <FormControl
      sx={{
        width: '20%',
      }}
      size="small"
    >
      <InputLabel
        id={filter.filterName}
        sx={{
          background: '#FFFFFF',
        }}
      >
        {filter.filterName}
      </InputLabel>
      <Select
        sx={{
          px: '5%',
          justifyContent: 'center',
        }}
        labelId={filter.filterName}
        value={selectedFilterValue ?? disabledValue}
        onChange={(e) => onSelected(e.target.value as string)}
        IconComponent={() => <KeyboardArrowDownRounded />}
      >
        <MenuItem disabled value={disabledValue}>
          All
        </MenuItem>
        {filter.options.map((opt, index) => (
          <MenuItem key={index} value={opt.value}>
            {opt.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}
interface SearchByValuesBoxProps {
  searchValue: string
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  searchByValues: Array<{
    label: string
    value: string
  }>
  setSearchByValue: (value: string) => void
}
const SearchByValuesBox = ({
  searchValue,
  handleSearch,
  searchByValues,
  setSearchByValue,
}: SearchByValuesBoxProps) => {
  const [selectedSearchBy, setSelectedSearchBy] = useState<string>(
    searchByValues[0]?.value ?? ''
  )
  const handleSelect = (e: SelectChangeEvent) => {
    setSelectedSearchBy(e.target.value)
    setSearchByValue(e.target.value)
  }
  return (
    <Stack direction="row">
      <Select
        fullWidth
        size="small"
        onChange={handleSelect}
        SelectDisplayProps={{
          style: { display: 'flex', justifyContent: 'space-between' },
        }}
        IconComponent={() => <KeyboardArrowDownRounded />}
        value={selectedSearchBy}
        sx={{
          width: '10vw',
          '.MuiInputBase-input.MuiOutlinedInput-input ': {
            py: '2px !important',
          },
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
          borderRight: 'none',
        }}
      >
        {searchByValues.map((item) => (
          <MenuItem key={item.value} value={item.value}>
            {`Search By ${item.label}`}
          </MenuItem>
        ))}
      </Select>
      <CustomSearchInput
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
        sx={{
          '& fieldset': {
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
            borderLeft: 'none !important',
          },
        }}
        endAdornment={
          <InputAdornment position="start">
            <SearchOutlinedIcon sx={{ color: '#555C61' }} />
          </InputAdornment>
        }
      />
    </Stack>
  )
}
// export const DateFilterBox = () => {
//   return (
//     <DateRangePicker
//       buttonText="Date created"
//       onApplyDateRange={(date: { start: Dayjs; end: Dayjs }) => {
//         getCustomerDevices({
//           dispatch,
//           params: {
//             profileID: selectedCustomer.customer
//               ? selectedCustomer?.customer.id
//               : '',
//             page: 0,
//             size: 7,
//             dateCreatedFrom: date.start.format('YYYY-MM-DD'),
//             dateCreatedTo: date.end.format('YYYY-MM-DD'),
//             status: devices === 'Active' ? 'ACTIVE' : 'INACTIVE',
//           },
//         })
//       }}
//     />
//   )
// }
