'use client'
import { InputBase, InputBaseProps, styled } from '@mui/material'
import { StyledComponent } from '@emotion/styled'

export const InputField: StyledComponent<InputBaseProps> = styled(InputBase)(
  () => ({
    padding: '12px 8px',
    height: '40px',
    width: '381px',
    display: 'flex',
    alignItems: 'center',
    border: '1px solid var(--Color-Stroke-Stroke-2, #E3E4E4)',
    borderRadius: '4px',
    background: '#FFF',
    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
  })
)
