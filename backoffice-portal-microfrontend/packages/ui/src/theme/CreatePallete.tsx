export const brandThemePalette = {
  primary: {
    main: '#e6452f',
    primary2: '#f92406',
    primary3: '#ff2e0d',
    primary4: '#ff5d39',
    primary5: '#ff7e5d',
    primary6: '#ffa48c',
    primary7: '#ffc9b9',
    contrastText: '#ffe8e6',
  },
  secondary: {
    main: '#000A12',
    secondary2: '#2A3339',
    secondary3: '#555C61',
    secondary4: '#808488',
    secondary5: '#AAADB0',
    contrastText: '#FFFFFF',
  },
}

export const greenThemePalette = {
  primary: {
    main: '#00BC2D',
    primary2: '#26b43b',
    primary3: '#50c05b',
    primary4: '#73cb79',
    primary5: '#9dd9a0',
    primary6: '#c4e8c5',
    primary7: '#e6f6e7',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#000A12',
    secondary2: '#2A3339',
    secondary3: '#555C61',
    secondary4: '#808488',
    secondary5: '#AAADB0',
    contrastText: '#FFFFFF',
  },
}

export function createPalette() {
  return {
    primary: {
      main: '#000A12',
      primary2: '#2A3339',
      primary3: '#555C61',
      primary4: '#808488',
      primary5: '#AAADB0',
      primary6: '#BDC0C3',
      primary7: '#E7E8E9',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#EB0045',
      secondary2: '#F13C57',
      secondary3: '#FA727B',
      secondary4: '#FF9DA0',
      secondary5: '#FFC5C5',
      contrastText: '#FFFFFF',
    },
    neutral: {
      main: '#E3E4E4',
      neutral2: '#F1F1F2',
      neutral3: '#F7F7F7',
      neutral4: '#FCFCFC',
      neutral5: '#FFFFFF',
    },
    text: {
      primary: '#000A12',
      secondary: '#2A3339',
      tertiary: '#475467',
      disabled: '#BDC0C3',
    },
    error: {
      main: '#EB0045',
    },
    success: {
      lighter: '#ECFDF3',
      light: '#12B76A',
      main: '#027A48',
    },
    warning: {
      main: '#FDB022',
    },
    info: {
      main: '#0000FF',
    },
  }
}
