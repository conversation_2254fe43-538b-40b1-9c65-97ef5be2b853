// declare module '@mui/material/Button' {
//   interface ButtonPropsVariantOverrides {
//     contDisabled: true
//     outDisabled: true
//   }
// }
import { Theme } from '@mui/material/styles'

export function createComponents(theme: Theme) {
  return {
    // MuiInputBase: {
    //   styleOverrides: {
    //     root: {
    //       borderRadius: '4px',
    //       border: '1px solid #E3E4E4',
    //       boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
    //     },
    //   },
    // },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          variants: [],
        },
        containedPrimary: {
          borderRadius: '6px',
          boxShadow: 'none',
          maxHeight: '40px',
          background: theme.palette.primary.main,
          '&:hover': {
            background: theme.palette.primary.primary2,
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          },
        },
        containedSecondary: {
          borderRadius: '6px',
          boxShadow: 'none',
          maxHeight: '40px',
          background: theme.palette.secondary.main,
          '&:hover': {
            background: theme.palette.secondary.secondary2,
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          },
        },
        outlinedPrimary: {
          color: theme.palette.primary.primary3,
          borderRadius: '6px',
          maxHeight: '40px',
          border: `2px solid ${theme.palette.primary.primary3}`,
          background: '#FFFFFF',
          '&:hover': {
            border: `1.5px solid ${theme.palette.primary.main}`,
            color: theme.palette.primary.main,
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          },
        },

        outlinedSecondary: {
          color: theme.palette.secondary.secondary3,
          borderRadius: '6px',
          maxHeight: '40px',
          border: `2px solid ${theme.palette.secondary.secondary3}`,
          background: '#FFFFFF',
          '&:hover': {
            border: `1.5px solid ${theme.palette.secondary.main}`,
            color: theme.palette.secondary.main,
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          },
        },

        textPrimary: {
          color: theme.palette.primary.main,
          gap: '8px',
        },
        sizeSmall: {
          padding: '8px 28px',
          gap: '8px',
        },
        sizeMedium: {
          padding: '10px 42px',
          gap: '10px',
        },
        sizeLarge: {
          padding: '16px 64px',
          gap: '14px',
        },
      },
    },
    // MuiTextField: {
    //   styleOverrides: {
    //     root: {},
    //   },
    // },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          // marginBottom: '5px',
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: '#E3E4E4',
          },
          '&.Mui-disabled': {
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: '#E3E4E4',
            },
          },
        },
      },
    },
  }
}
