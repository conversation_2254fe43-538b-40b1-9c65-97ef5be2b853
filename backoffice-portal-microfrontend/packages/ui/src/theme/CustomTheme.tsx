/**
 * <AUTHOR> on 19/09/2024
 */

'use client'
import { createTheme, ThemeProvider } from '@mui/material/styles'
import React from 'react'
import { CssBaseline } from '@mui/material'

import {
  createPalette,
  greenThemePalette,
  brandThemePalette,
} from './CreatePallete'
import { createComponents } from './CreateComponents'
// import { createShadows } from './CreateShadows'
import { Typography } from './CreateTypography'
import '../font/font.css'

export type ThemeType = 'main' | 'green' | 'brand'

const BREAKPOINTS = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1440,
}

function getPalette(themeType: ThemeType) {
  let palette = createPalette()
  if (themeType === 'green') {
    palette = {
      ...palette,
      primary: greenThemePalette.primary,
      secondary: greenThemePalette.secondary,
    }
  } else if (themeType === 'brand') {
    palette = {
      ...palette,
      primary: brandThem<PERSON>.primary,
      secondary: brandThemePalette.secondary,
    }
  }
  return palette
}

function createCustomTheme(themeType: ThemeType) {
  const palette = getPalette(themeType)
  const theme = createTheme({ palette })
  const components = createComponents(theme)
  const typography = Typography(theme, themeType)

  return createTheme({
    breakpoints: { values: BREAKPOINTS },
    components,
    palette,
    // shadows,
    typography,
  })
}

export function ThemeConfig({
  children,
  themeType = 'main',
}: {
  children: React.ReactNode
  themeType?: ThemeType
}) {
  const theme = createCustomTheme(themeType)

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  )
}
