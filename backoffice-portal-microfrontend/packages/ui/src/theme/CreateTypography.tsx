import { Theme } from '@mui/material/styles'
import { ThemeType } from './CustomTheme'

function getFontFamily(themeType: ThemeType) {
  let fontFamily = 'BlissPro'
  if (themeType === 'masterLink') {
    fontFamily = 'Work Sans'
  }
  return fontFamily
}

export const Typography = (theme: Theme, themeType: ThemeType) => {
  return {
    fontFamily: getFontFamily(themeType),
    h1: {
      fontWeight: 500,
      lineHeight: '64px',
      fontSize: '57px',
      color: theme.palette.text.primary,
    },
    h2: {
      fontWeight: 500,
      lineHeight: '52px',
      fontSize: '45px',
      color: theme.palette.text.primary,
    },
    h3: {
      fontWeight: 500,
      fontSize: '36px',
      lineHeight: '44px',
      color: theme.palette.text.primary,
    },
    h4: {
      fontWeight: 500,
      lineHeight: '40px',
      fontSize: '32px',
      color: theme.palette.text.primary,
    },
    h5: {
      fontWeight: 500,
      lineHeight: '36px',
      fontSize: '28px',
      color: theme.palette.text.primary,
    },
    h6: {
      lineHeight: '32px',
      fontSize: '24px',
      fontWeight: 500,
      color: theme.palette.text.primary,
    },
    subtitle1: {
      lineHeight: '28px',
      fontSize: '22px',
      fontWeight: 500,
      color: theme.palette.text.primary,
    },
    subtitle2: {
      lineHeight: '24px',
      fontSize: '16px',
      fontWeight: 500,
      color: theme.palette.text.secondary,
    },
    subtitle3: {
      lineHeight: '20px',
      fontSize: '14px',
      fontWeight: 500,
      color: theme.palette.text.secondary,
    },
    body1: {
      lineHeight: '24px',
      fontSize: '16px',
      fontWeight: 500,
      color: '#475467',
    },
    body2: {
      lineHeight: '20px',
      fontSize: '14px',
      fontWeight: 500,
      color: theme.palette.text.secondary,
    },
    body3: {
      lineHeight: '16px',
      fontSize: '12px',
      fontWeight: 500,
      color: theme.palette.text.secondary,
    },
    label1: {
      lineHeight: '20px',
      fontSize: '14px',
      fontWeight: 500,
      color: theme.palette.text.primary,
    },
    label2: {
      lineHeight: '16px',
      fontSize: '12px',
      fontWeight: 500,
      color: theme.palette.text.primary,
    },
    label3: {
      lineHeight: '16px',
      fontSize: '11px',
      fontWeight: 500,
      color: theme.palette.text.primary,
    },
    button: {
      lineHeight: '24px',
      fontSize: '16px',
      fontWeight: 700,
      color: theme.palette.text.primary,
    },
    caption: {
      letterSpacing: '0.4px',
      color: theme.palette.text.secondary,
    },
  }
}
