/**
 * <AUTHOR> on 12/06/2025
 */

import '@mui/material/Typography'
import '@mui/material/styles'

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    subtitle1: true
    subtitle2: true
    subtitle3: true
    body1: true
    body2: true
    body3: true
    label1: true
    label2: true
    label3: true
    button: true
    h3: true
  }
}

declare module '@mui/material/styles' {
  interface Theme {
    palette: {
      primary: {
        main: string
        primary2: string
        primary3: string
        primary4: string
        primary5: string
        primary6: string
        primary7: string
        contrastText: string
      }
      secondary: {
        main: string
        secondary2: string
        secondary3: string
        secondary4: string
        secondary5: string
        contrastText: string
      }
      neutral: {
        main: string
        neutral2: string
        neutral3: string
        neutral4: string
        neutral5: string
      }
      text: {
        primary: string
        secondary: string
        tertiary: string
        disabled: string
      }
      error: {
        main: string
      }
      success: {
        main: string
        light: string
        lighter: string
      }
      warning: {
        main: string
      }
      info: {
        main: string
      }
    }
  }
}
