import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/components/Chip/CustomChips.tsx
import {
  Chip,
  Stack,
  styled
} from "@mui/material";
import Typography from "@mui/material/Typography";
import CircleIcon from "@mui/icons-material/Circle";
import CheckCircleOutlinedIcon from "@mui/icons-material/CheckCircleOutlined";
import ErrorOutlineOutlinedIcon from "@mui/icons-material/ErrorOutlineOutlined";
import RestoreIcon from "@mui/icons-material/Restore";

// src/const/ChipColors.ts
var CHIPCOLORS = {
  ACTIVE: {
    background: "#ECFDF3",
    color: "#12B76A"
  },
  APPROVED: {
    background: "#ECFDF3",
    color: "#12B76A"
  },
  DEACTIVATED: {
    background: "#F2F4F7",
    color: "#667085"
  },
  PENDING: {
    background: "#FFF6ED",
    color: "#FB6514"
  },
  NEW: {
    background: "#EFF8FF",
    color: "#175CD3"
  },
  REJECTED: {
    background: "#FEF3F2",
    color: "#F04438"
  },
  INACTIVE: {
    background: "#FEF3F2",
    color: "#F04438"
  },
  RESTRICTED: {
    background: "#FFF6ED",
    color: "#C4320A"
  }
};

// src/components/Chip/CustomChips.tsx
import { jsx, jsxs } from "react/jsx-runtime";
var CustomStatusChip = styled(
  Chip
)(() => ({
  border: "none",
  fontWeight: "600",
  padding: "2px 8px 2px 6px",
  justifyContent: "center",
  alignItems: "center",
  gap: "6px"
}));
CustomStatusChip.displayName = "CustomStatusChip";
var CustomSuccessChip = (props) => {
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      icon: /* @__PURE__ */ jsx(CircleIcon, { sx: { fontSize: "8px" }, color: "success" }),
      variant: "outlined",
      sx: { color: "#027A48", backgroundColor: "#ECFDF3" }
    }, props)
  );
};
var CustomBlockedChip = styled(
  Chip
)(() => ({
  backgroundColor: "#FFFF",
  color: "#D92D20",
  border: `2px solid #D92D20`,
  fontWeight: 500
}));
CustomBlockedChip.displayName = "CustomBlockedChip";
var CustomPendingOrgApprovalChip = styled(
  Chip
)(() => ({
  backgroundColor: "#FFFF",
  color: "#D92D20",
  border: `2px solid #D92D20`,
  fontWeight: 500
}));
CustomPendingOrgApprovalChip.displayName = "CustomPendingOrgApprovalChip";
var CustomActiveBrokerChip = styled(
  Chip
)(() => ({
  backgroundColor: "#FFFF",
  color: "#079455",
  border: `2px solid #079455`,
  fontWeight: 500
}));
CustomActiveBrokerChip.displayName = "CustomActiveBrokerChip";
var CustomInactiveBrokerChip = styled(
  Chip
)(() => ({
  backgroundColor: "#FFFF",
  color: "#1570EF",
  border: `2px solid #1570EF`,
  fontWeight: 500
}));
CustomInactiveBrokerChip.displayName = "CustomInactiveBrokerChip";
var CustomPendingApprovalChip = styled(
  Chip
)(() => ({
  backgroundColor: "#FFFF",
  color: "#E04F16",
  border: `2px solid #E04F16`,
  fontWeight: 500
}));
CustomPendingApprovalChip.displayName = "CustomPendingApprovalChip";
var StatusChip = (_a) => {
  var _b = _a, {
    status = "success",
    sx
  } = _b, props = __objRest(_b, [
    "status",
    "sx"
  ]);
  const styles = {
    warn: {
      color: "#B93815",
      backgroundColor: "#FEF6EE",
      border: "1px solid #F9DBAF"
    },
    info: {
      color: "#1570EF",
      backgroundColor: "rgba(21,112,239,0.1)",
      border: "1px solid #1570EF"
    },
    success: {
      color: "#067647",
      backgroundColor: "#ECFDF3",
      border: "1px solid #ABEFC6"
    },
    error: {
      color: "#B93815",
      backgroundColor: "#FEF6EE",
      border: "1px solid #F9DBAF"
    },
    neutral: {
      color: "#000000",
      backgroundColor: "#FFFFFF",
      border: "1px solid #CCCCCC"
    },
    processing: {
      color: "#5925DC",
      backgroundColor: "#F4F3FF",
      border: "1px solid #D9D6FE"
    },
    default: {
      color: "#344054",
      backgroundColor: "#F9FAFB",
      border: "1px solid #E4E7EC"
    }
  };
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      variant: "outlined",
      sx: __spreadValues(__spreadValues({
        borderRadius: "6px",
        fontSize: "12px",
        height: "25px"
      }, sx), styles[status])
    }, props)
  );
};
var CustomActiveChip = (props) => {
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      icon: /* @__PURE__ */ jsx(CircleIcon, { sx: { fontSize: "8px", color: "#12B76A !important" } }),
      variant: "outlined",
      sx: {
        color: "#027A48",
        backgroundColor: "#ECFDF3",
        fontWeight: 500,
        fontSize: "12px",
        height: "20px"
      }
    }, props)
  );
};
var CustomPaymentStatusChip = (props) => {
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      icon: /* @__PURE__ */ jsx(CircleIcon, { sx: { fontSize: "8px" }, color: "success" }),
      variant: "outlined",
      sx: {
        border: "1px solid #D0D5DD",
        borderRadius: "8px",
        fontWeight: "500"
      }
    }, props)
  );
};
var CustomWarningChip = (props) => {
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      icon: /* @__PURE__ */ jsx(CircleIcon, { sx: { fontSize: "8px", color: "#E16012 !important" } }),
      variant: "outlined",
      sx: {
        color: "#E16012",
        backgroundColor: "#FFF6ED",
        fontWeight: 500,
        fontSize: "12px",
        height: "20px !important"
      }
    }, props)
  );
};
var CustomErrorChip = (props) => {
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      icon: /* @__PURE__ */ jsx(CircleIcon, { sx: { fontSize: "8px" }, color: "error" }),
      variant: "outlined",
      sx: {
        color: "#B42318",
        backgroundColor: "#FEE4E2",
        fontWeight: 500,
        fontSize: "12px",
        height: "20px !important"
      }
    }, props)
  );
};
var CustomLoanSuccessChip = (props) => {
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      icon: /* @__PURE__ */ jsx(CheckCircleOutlinedIcon, { sx: { fontSize: "12px" }, color: "info" }),
      variant: "outlined",
      sx: { color: "#175CD3", backgroundColor: "#EFF8FF" }
    }, props)
  );
};
var CustomLoanFailedChip = (props) => {
  return /* @__PURE__ */ jsx(
    CustomStatusChip,
    __spreadValues({
      icon: /* @__PURE__ */ jsx(ErrorOutlineOutlinedIcon, { sx: { fontSize: "12px" }, color: "error" }),
      variant: "outlined",
      sx: { color: "#B42318", backgroundColor: "#FEF3F2" }
    }, props)
  );
};
var CustomDrawerChip = styled(Chip)(() => ({
  border: "1px solid #EAECF0",
  borderRadius: "6px",
  fontWeight: "500",
  padding: "2px 4px 2px 6px",
  justifyContent: "center",
  alignItems: "center",
  gap: "6px",
  background: "#FFF",
  color: "##344054",
  fontSize: "12px",
  fontStyle: "normal",
  textAlign: "center",
  minWidth: "56px",
  height: "22px"
}));
CustomDrawerChip.displayName = "CustomDrawerChip";
var CustomerStatusChip = styled(
  (_a) => {
    var other = __objRest(_a, []);
    const label = other.label;
    const defaultStyles = {
      background: "grey",
      color: "white"
    };
    const chipStyles = CHIPCOLORS[label] || defaultStyles;
    return /* @__PURE__ */ jsx(
      Chip,
      __spreadProps(__spreadValues({}, other), {
        sx: {
          background: chipStyles.background,
          padding: "2px 6px 2px 8px",
          maxHeight: "20px",
          minWidth: "52px"
        },
        label: /* @__PURE__ */ jsxs(
          Stack,
          {
            sx: {
              gap: "6px",
              flexDirection: "row",
              justifyContent: "flex-start",
              alignItems: "center"
            },
            children: [
              " ",
              /* @__PURE__ */ jsx(
                Stack,
                {
                  sx: {
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    background: chipStyles.color
                  }
                }
              ),
              /* @__PURE__ */ jsx(
                Typography,
                {
                  variant: "label2",
                  sx: {
                    color: chipStyles.color,
                    fontSize: "12px",
                    fontStyle: "normal",
                    fontWeight: 500,
                    lineHeight: "16px"
                  },
                  children: other.label && other.label.charAt(0) + other.label.slice(1).toLowerCase()
                }
              )
            ]
          }
        )
      })
    );
  }
)(() => ({
  padding: 0,
  textAlign: "center",
  fontSize: "12px",
  fontStyle: "normal",
  fontWeight: 500,
  lineHeight: "16px"
}));
CustomerStatusChip.displayName = "CustomerStatusChip";
var CustomerInfoChip = ({
  label,
  requests
}) => {
  return /* @__PURE__ */ jsx(
    Chip,
    {
      icon: /* @__PURE__ */ jsx(RestoreIcon, { sx: { fontSize: "16px", color: "#555C61 !important" } }),
      variant: "outlined",
      sx: {
        color: "#555C61",
        backgroundColor: "#F8F9FC",
        border: "1px solid #D5D9EB",
        padding: "6px 6px 6px 8px",
        maxHeight: "30px",
        minWidth: "52px"
      },
      label: /* @__PURE__ */ jsxs(
        Stack,
        {
          sx: {
            gap: "6px",
            flexDirection: "row",
            justifyContent: "flex-start",
            alignItems: "center"
          },
          children: [
            /* @__PURE__ */ jsx(
              Typography,
              {
                variant: "label2",
                sx: {
                  fontSize: "14px",
                  fontStyle: "normal",
                  fontWeight: 500,
                  lineHeight: "16px",
                  color: "#555C61"
                },
                children: label && label.charAt(0) + label.slice(1).toLowerCase()
              }
            ),
            requests.map((item, index) => /* @__PURE__ */ jsx(
              Typography,
              {
                variant: "label2",
                sx: {
                  fontSize: "14px",
                  fontStyle: "normal",
                  fontWeight: 500,
                  lineHeight: "16px",
                  textDecoration: "underline",
                  color: "#555C61"
                },
                children: item
              },
              index
            ))
          ]
        }
      )
    }
  );
};
var CustomChip = styled(Chip)(
  () => {
    return {
      padding: "2px 6px",
      display: "flex",
      alignItems: "center",
      borderRadius: "6px",
      border: "1px solid #EAECF0",
      height: "20px",
      fontSize: "12px",
      fontWeight: 400,
      color: "#344054",
      backgroundColor: "#FFFFFF"
    };
  }
);
CustomChip.displayName = "CustomChip";

export {
  CustomStatusChip,
  CustomSuccessChip,
  CustomBlockedChip,
  CustomPendingOrgApprovalChip,
  CustomActiveBrokerChip,
  CustomInactiveBrokerChip,
  CustomPendingApprovalChip,
  StatusChip,
  CustomActiveChip,
  CustomPaymentStatusChip,
  CustomWarningChip,
  CustomErrorChip,
  CustomLoanSuccessChip,
  CustomLoanFailedChip,
  CustomDrawerChip,
  CustomerStatusChip,
  CustomerInfoChip,
  CustomChip
};
