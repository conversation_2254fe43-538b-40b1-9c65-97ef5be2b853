import {
  CustomCheckBox
} from "../../chunk-7KHKZLSB.js";
import {
  LoadingButton
} from "../../chunk-QY6BZGO5.js";
import {
  __spreadProps,
  __spreadValues
} from "../../chunk-BBZEL7EG.js";

// src/components/Overlay/IDView.tsx
import { Close } from "@mui/icons-material";
import { Box, Grow, IconButton } from "@mui/material";
import Image from "next/image";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
var IDView = ({
  open,
  setDocumentViewer
}) => {
  return /* @__PURE__ */ jsx(Fragment, { children: open && /* @__PURE__ */ jsxs(
    Box,
    {
      sx: {
        width: "100vw",
        height: "100vh",
        zIndex: 1e3,
        backgroundColor: "rgba(49, 45, 45, 0.5)",
        position: "fixed",
        top: 0,
        color: "whitesmoke"
      },
      children: [
        /* @__PURE__ */ jsx(
          IconButton,
          {
            onClick: () => {
              setDocumentViewer({ open: false, imageUrl: "" });
            },
            sx: {
              background: "#F1F5F9",
              position: "absolute",
              top: "85px",
              right: "63px",
              height: "57px",
              width: "57px",
              "&:hover": {
                color: "white",
                border: "1px solid white"
              },
              zIndex: 1010
            },
            children: /* @__PURE__ */ jsx(
              Close,
              {
                sx: {
                  width: "32px",
                  height: "32px"
                }
              }
            )
          }
        ),
        /* @__PURE__ */ jsx(
          Box,
          {
            sx: {
              width: "100%",
              height: "100%",
              display: "flex",
              justifyContent: "center",
              alignItems: "center"
            },
            children: /* @__PURE__ */ jsx(
              Box,
              {
                sx: {
                  width: "auto",
                  height: "auto",
                  borderRadius: "10px",
                  display: "flex",
                  padding: "10%",
                  maxWidth: "80%",
                  maxHeight: "80%",
                  background: "transparent",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center"
                },
                children: /* @__PURE__ */ jsx(
                  Grow,
                  __spreadProps(__spreadValues({
                    in: open,
                    style: { transformOrigin: "0 0 0" }
                  }, open ? { timeout: 1e3 } : {}), {
                    children: /* @__PURE__ */ jsx(
                      Image,
                      {
                        src: "https://www.ohmancorp.com/images/ss/Color_TN.jpg",
                        alt: "image-viewer",
                        layout: "fill",
                        style: {
                          padding: "7%"
                        },
                        objectFit: "contain"
                      }
                    )
                  })
                )
              }
            )
          }
        )
      ]
    }
  ) });
};

// src/components/Overlay/CustomDialog/Dialog.tsx
import { CloseRounded as CloseRounded2 } from "@mui/icons-material";
import {
  Button as Button2,
  Dialog as Dialog2,
  DialogContent as DialogContent2,
  IconButton as IconButton3,
  Stack,
  TextField,
  Typography as Typography2
} from "@mui/material";
import { useEffect, useState } from "react";

// src/components/Overlay/CustomDialog/ConfirmCancelSave.tsx
import { CloseRounded } from "@mui/icons-material";
import {
  Box as Box2,
  Button,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton as IconButton2,
  Typography
} from "@mui/material";
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
var ConfirmCancelSave = ({
  open,
  onClose,
  onConfirmCancel,
  onConfirmSubmit
}) => {
  return /* @__PURE__ */ jsx2(Dialog, { open, onClose, sx: {}, children: /* @__PURE__ */ jsxs2(Box2, { sx: { width: "383px" }, children: [
    /* @__PURE__ */ jsxs2(
      DialogTitle,
      {
        sx: {
          borderBottom: "1px solid #CBD5E1",
          padding: "16px 20px 8px 24px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          backgroundColor: "#F9FAFB"
        },
        children: [
          /* @__PURE__ */ jsx2(Typography, { sx: { color: "#000A12", fontWeight: 600 }, children: "Discard changes?" }),
          /* @__PURE__ */ jsx2(
            IconButton2,
            {
              onClick: onClose,
              sx: {
                width: "36px",
                height: "36px",
                backgroundColor: " #F1F5F9",
                border: "1px solid #CBD5E1)",
                borderRadius: "50%"
              },
              children: /* @__PURE__ */ jsx2(CloseRounded, {})
            }
          )
        ]
      }
    ),
    /* @__PURE__ */ jsxs2(
      DialogContent,
      {
        sx: {
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          padding: "0px 20px 20px 20px"
        },
        children: [
          /* @__PURE__ */ jsx2(
            DialogContentText,
            {
              sx: {
                textAlign: "center"
              },
              children: "You have unsaved changes, are you sure you want to discard these changes?"
            }
          ),
          /* @__PURE__ */ jsxs2(
            Box2,
            {
              sx: {
                display: "flex",
                justifyContent: "center",
                gap: "20px"
              },
              children: [
                /* @__PURE__ */ jsx2(
                  Button,
                  {
                    variant: "outlined",
                    sx: {
                      backgroundColor: "#FFFFFF",
                      border: "1.5px solid  #AAADB0",
                      borderRadius: "4px",
                      boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
                      color: "#555C61",
                      fontSize: "16px",
                      fontStyle: "normal",
                      fontWeight: "500",
                      lineHeight: "24px",
                      textAlign: "center",
                      width: "153px",
                      height: "40px",
                      textWrap: "nowrap"
                    },
                    onClick: onConfirmCancel,
                    children: "Yes, I'm sure."
                  }
                ),
                /* @__PURE__ */ jsx2(
                  Button,
                  {
                    variant: "contained",
                    sx: {
                      border: "1.5px solid  #AAADB0",
                      borderRadius: "4px",
                      boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
                      fontSize: "16px",
                      fontStyle: "normal",
                      fontWeight: "500",
                      lineHeight: "24px",
                      textAlign: "center",
                      width: "153px",
                      height: "40px",
                      textWrap: "nowrap"
                    },
                    onClick: onConfirmSubmit,
                    children: "Save changes"
                  }
                )
              ]
            }
          )
        ]
      }
    )
  ] }) });
};

// src/components/Overlay/CustomDialog/Dialog.tsx
import { jsx as jsx3, jsxs as jsxs3 } from "react/jsx-runtime";
var CustomDialog = ({
  open,
  setOpen,
  onClick,
  subtitle,
  buttonText,
  buttonProps,
  isLoading,
  descriptionText,
  title,
  reasons = [],
  // Use reasons prop
  concatReason = false
}) => {
  const [reason, setReason] = useState("");
  const [isOther, setIsOther] = useState(false);
  const [isConfirmCancelOpen, setIsConfirmCancelOpen] = useState(false);
  const [hasTouched, setHasTouched] = useState(false);
  const [error, setError] = useState("");
  const handleKeyDown = (event) => {
    event.stopPropagation();
  };
  const [selectedReasons, setSelectedReasons] = useState(
    new Array(reasons.length).fill(false)
    // Initialize based on the reasons prop length
  );
  const [isAnySelected, setIsAnySelected] = useState(false);
  const handleSelection = (index, reason2) => {
    const updatedSelection = [...selectedReasons];
    updatedSelection[index] = !updatedSelection[index];
    setSelectedReasons(updatedSelection);
    const anySelected = updatedSelection.some((selected) => selected);
    setIsAnySelected(anySelected);
    setIsOther(reason2 === "Other" && updatedSelection[index]);
  };
  const handleChange = (event) => {
    const input = event.target.value;
    setReason(input);
    setHasTouched(true);
    if (!validateReason(input)) {
      setError(
        "Reason must be at least 10 characters and contain only valid characters."
      );
    } else {
      setError("");
    }
  };
  const validateReason = (reason2) => {
    const trimmedReason = reason2.trim();
    return trimmedReason.length >= 10 && /^[\s\S]*$/.test(trimmedReason);
  };
  let selectedReasonsList = reasons.filter(
    (_reason, index) => selectedReasons[index]
  );
  if (reason) {
    selectedReasonsList = selectedReasonsList.filter(
      (reason2) => reason2 !== "Other"
    );
    selectedReasonsList.push(reason);
  }
  const handleCancel = () => {
    if (reason || isAnySelected) {
      setIsConfirmCancelOpen(true);
    } else {
      setReason("");
      setIsOther(false);
      setIsConfirmCancelOpen(false);
      setHasTouched(false);
      setError("");
      setSelectedReasons(new Array(reasons.length).fill(false));
      setIsAnySelected(false);
      setOpen(false);
    }
  };
  useEffect(() => {
    if (!open) {
      setReason("");
      setIsOther(false);
      setHasTouched(false);
      setError("");
      setSelectedReasons(new Array(reasons.length).fill(false));
      setIsAnySelected(false);
    }
  }, [open, reasons.length]);
  const isActionDisabled = !isAnySelected || isOther && (!reason || !validateReason(reason));
  return /* @__PURE__ */ jsxs3(Dialog2, { open, maxWidth: "sm", children: [
    /* @__PURE__ */ jsxs3(
      Stack,
      {
        direction: "row",
        sx: {
          justifyContent: "space-between",
          alignItems: "center",
          px: "5%",
          py: "1%",
          borderBottom: "2px solid #F2F4F7",
          background: "#F9FAFB"
        },
        children: [
          /* @__PURE__ */ jsxs3(Stack, { direction: "column", children: [
            /* @__PURE__ */ jsxs3(Typography2, { sx: { fontWeight: 700 }, children: [
              title,
              " "
            ] }),
            subtitle && /* @__PURE__ */ jsx3(Typography2, { sx: { fontSize: "14px" }, children: subtitle })
          ] }),
          /* @__PURE__ */ jsx3(
            IconButton3,
            {
              sx: {
                border: "0.8px solid #CBD5E1"
              },
              onClick: handleCancel,
              children: /* @__PURE__ */ jsx3(CloseRounded2, { fontSize: "small" })
            }
          )
        ]
      }
    ),
    /* @__PURE__ */ jsxs3(
      DialogContent2,
      {
        sx: {
          display: "flex",
          flexDirection: "column",
          gap: "20px"
        },
        children: [
          /* @__PURE__ */ jsx3(
            Typography2,
            {
              sx: {
                textWrap: "wrap"
              },
              children: descriptionText
            }
          ),
          !isAnySelected && reasons && reasons.map((reason2, index) => /* @__PURE__ */ jsxs3(
            Stack,
            {
              direction: "row",
              justifyContent: "space-between",
              alignItems: "center",
              sx: {
                border: selectedReasons[index] ? "2px solid #555C61" : "2px solid #D0D5DD",
                borderRadius: "6px",
                padding: "1px"
              },
              children: [
                /* @__PURE__ */ jsx3(Typography2, { sx: { padding: "0 4px" }, children: reason2 }),
                /* @__PURE__ */ jsx3(
                  CustomCheckBox,
                  {
                    checked: selectedReasons[index],
                    onChange: () => handleSelection(index, reason2),
                    disabled: !selectedReasons[index] && isAnySelected
                  }
                )
              ]
            },
            reason2
          )),
          isAnySelected && /* @__PURE__ */ jsx3(
            TextField,
            {
              fullWidth: true,
              onKeyDown: handleKeyDown,
              multiline: true,
              rows: 4,
              placeholder: "Type your reason here",
              value: reason,
              onChange: handleChange,
              error: Boolean(hasTouched && error),
              helperText: hasTouched && error ? error : ""
            }
          ),
          /* @__PURE__ */ jsxs3(Stack, { justifyContent: "space-between", direction: "row", children: [
            /* @__PURE__ */ jsx3(
              Button2,
              {
                variant: "outlined",
                sx: {
                  width: "45%"
                },
                onClick: handleCancel,
                children: "Cancel"
              }
            ),
            isLoading ? /* @__PURE__ */ jsx3(LoadingButton, {}) : /* @__PURE__ */ jsx3(
              Button2,
              {
                variant: "contained",
                disabled: isActionDisabled,
                sx: {
                  background: buttonProps && buttonProps.color || "#EB0045",
                  border: isActionDisabled ? "" : `1.5px solid ${buttonProps && buttonProps.color || "#EB0045"}`,
                  "&:hover": {
                    background: buttonProps && buttonProps.color || "#EB0045 ",
                    opacity: "0.5"
                  },
                  width: "45%",
                  textWrap: "nowrap"
                },
                onClick: () => {
                  if (onClick) {
                  }
                  onClick && onClick(selectedReasonsList);
                  setOpen(false);
                },
                children: buttonText
              }
            )
          ] })
        ]
      }
    ),
    /* @__PURE__ */ jsx3(
      ConfirmCancelSave,
      {
        open: isConfirmCancelOpen,
        onConfirmCancel: () => {
          setIsConfirmCancelOpen(false);
          setOpen(false);
        },
        onClose: () => {
          setIsConfirmCancelOpen(false);
        },
        onConfirmSubmit: () => {
          setIsConfirmCancelOpen(false);
          onClick && onClick(selectedReasonsList);
          setOpen(false);
        }
      }
    )
  ] });
};
export {
  ConfirmCancelSave,
  CustomDialog as Dialog,
  IDView
};
