import {
  LocalNotification
} from "../chunk-V7BCR6ZL.js";
import {
  InActivity
} from "../chunk-RS3LDYQF.js";
import {
  Logo
} from "../chunk-MXHLH3EM.js";
import {
  CustomScrollbar
} from "../chunk-3UC4443C.js";
import {
  CustomError
} from "../chunk-4YLDGS7B.js";
import {
  CustomIconButton
} from "../chunk-LU6CPN3H.js";
import {
  CustomerModuleSearchFilterBox
} from "../chunk-JFQINYBQ.js";
import "../chunk-3LNZ6JLA.js";
import "../chunk-7KHKZLSB.js";
import "../chunk-7ER5AOCB.js";
import {
  CustomAccordionWithDropdown,
  DropDownComponent
} from "../chunk-VEURLHY6.js";
import {
  AuthWrapper
} from "../chunk-TCJCJPKD.js";
import {
  CustomSkeleton,
  LoadingButton,
  LoadingCardListSkeleton,
  LoadingFullScreen,
  LoadingListsSkeleton
} from "../chunk-QY6BZGO5.js";
import "../chunk-FW72AQQN.js";
import "../chunk-YMEOZMFS.js";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "../chunk-BBZEL7EG.js";

// src/components/ReadOnlyTypography.tsx
import { TextField } from "@mui/material";
import { jsx } from "react/jsx-runtime";
var ReadOnlyTypography = (props) => {
  return /* @__PURE__ */ jsx(
    TextField,
    __spreadValues({
      fullWidth: true,
      size: "small",
      InputProps: {
        readOnly: true
      },
      InputLabelProps: {
        shrink: true
      },
      sx: {
        ".MuiOutlinedInput-notchedOutline": {
          borderColor: "#ACACAC !important"
        },
        ".MuiInputLabel-root": {
          color: "text.primary"
        }
      },
      multiline: true
    }, props)
  );
};

// src/components/CustomToggle/CustomToggleTab.tsx
import PropTypes from "prop-types";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import { styled } from "@mui/material/styles";
import { jsx as jsx2 } from "react/jsx-runtime";
function CustomTabPanel(_a) {
  var _b = _a, { children, value, index } = _b, other = __objRest(_b, ["children", "value", "index"]);
  return /* @__PURE__ */ jsx2(
    "div",
    __spreadProps(__spreadValues({
      role: "tabpanel",
      hidden: value !== index,
      id: `scrollable-auto-tabpanel-${index}`,
      "aria-labelledby": `scrollable-auto-tab-${index}`
    }, other), {
      children: value === index && /* @__PURE__ */ jsx2("div", { children })
    })
  );
}
CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired
};
var CustomToggleTabs = styled(Tabs)(({ theme }) => ({
  marginLeft: theme.spacing(0),
  marginRight: theme.spacing(0),
  marginBottom: theme.spacing(0),
  "& .MuiTabs-indicator": {
    display: "none"
  },
  border: "2px solid #D0D5DD",
  borderRadius: "6px",
  height: 40
}));
var CustomAntTab = styled((props) => /* @__PURE__ */ jsx2(Tab, __spreadValues({ disableRipple: true }, props)))(({ theme }) => ({
  textTransform: "none",
  minWidth: 0,
  [theme.breakpoints.up("sm")]: {
    minWidth: 0
  },
  fontWeight: "500",
  textWrap: "nowrap",
  width: "50%",
  height: 40,
  color: "#667085",
  fontSize: "14px",
  lineHeight: "10px",
  marginRight: theme.spacing(0),
  "&:hover": {
    color: "#667085",
    opacity: 1
    // backgroundColor: 'red',
  },
  "&.Mui-selected": {
    color: "#2A3339",
    fontWeight: theme.typography.fontWeightBold,
    borderRadius: "5px",
    border: "1px solid #D0D5DD",
    backgroundColor: "#F9FAFB"
  },
  "&.Mui-focusVisible": {
    backgroundColor: "#d1eaff"
  }
}));

// src/components/CustomToggle/index.tsx
import { styled as styled2 } from "@mui/material/styles";
import { TabsList as BaseTabsList } from "@mui/base/TabsList";
import { TabPanel as BaseTabPanel } from "@mui/base/TabPanel";
var grey = {
  50: "#F3F6F9",
  100: "#E5EAF2",
  200: "#DAE2ED",
  300: "#C7D0DD",
  400: "#B0B8C4",
  500: "#9DA8B7",
  600: "#6B7A90",
  700: "#434D5B",
  800: "#303740",
  900: "#1C2025",
  950: "#D0D5DD",
  951: "#EAECF0"
};
var TabPanel = styled2(BaseTabPanel)(
  () => `
  width: 100%;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  background: ${grey[900]};
  border: 1px solid grey[200];
  border-radius: 12px;
  opacity: 0.6;
  `
);
var TabsList = styled2(BaseTabsList)(
  () => `
  width: 28%;
  background-color: ${grey[951]};
  border: 2px solid ${grey[950]};
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-content: space-between;
  `
);
export {
  AuthWrapper,
  CustomAccordionWithDropdown,
  CustomAntTab,
  CustomError,
  CustomIconButton,
  CustomScrollbar,
  CustomSkeleton,
  CustomTabPanel,
  CustomToggleTabs,
  CustomerModuleSearchFilterBox,
  DropDownComponent,
  InActivity,
  LoadingButton,
  LoadingCardListSkeleton,
  LoadingFullScreen,
  LoadingListsSkeleton,
  LocalNotification,
  Logo,
  ReadOnlyTypography,
  TabPanel,
  TabsList
};
