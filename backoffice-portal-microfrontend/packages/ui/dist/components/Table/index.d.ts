import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default, { FC } from 'react';
import { TableCellProps } from '@mui/material';

type PaginationOptions = {
    page: number;
    size: number;
    totalPages: number;
};
interface PaginationProps {
    options: PaginationOptions;
    handlePagination: (options: PaginationOptions) => void;
    activeBgColor?: string;
}
declare const CustomPagination: (props: PaginationProps) => react_jsx_runtime.JSX.Element;

type TableHeaderProps = {
    order: 'asc' | 'desc';
    orderBy?: string;
    rowCount: number;
    headLabel: IHeadCell[];
    numSelected: number;
    onRequestSort?: (event: React__default.MouseEvent<unknown>, property: string) => void;
    onSelectAllClick?: (event: React__default.ChangeEvent<HTMLInputElement>, checked: boolean) => void;
    showCheckbox?: boolean;
};
interface IHeadCell {
    id?: string;
    label?: string;
    alignRight?: boolean;
    alignCenter?: boolean;
}
declare const CustomTableHeader: ({ order, orderBy, rowCount, headLabel, numSelected, onSelectAllClick, showCheckbox, onRequestSort, }: TableHeaderProps) => react_jsx_runtime.JSX.Element;

declare const CustomTableCell: FC<TableCellProps>;

export { CustomPagination, CustomTableCell, CustomTableHeader, type PaginationOptions, type TableHeaderProps };
