import {
  CustomCheckBox
} from "../../chunk-7KHKZLSB.js";
import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "../../chunk-BBZEL7EG.js";

// src/components/Table/Pagination.tsx
import { Box, Button, Stack } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import usePagination from "@mui/material/usePagination";
import { useEffect, useState } from "react";
import { jsx, jsxs } from "react/jsx-runtime";
import { createElement } from "react";
var CustomPagination = (props) => {
  const {
    options: initialOptions,
    handlePagination,
    activeBgColor = "#F0F3F3"
  } = props;
  const [options, setOptions] = useState(initialOptions);
  const { items } = usePagination({
    siblingCount: 0,
    boundaryCount: 2,
    count: options == null ? void 0 : options.totalPages,
    page: options == null ? void 0 : options.page,
    // Explicitly set current page
    onChange: (_, page) => {
      const updatedOptions = __spreadProps(__spreadValues({}, options), { page });
      setOptions(updatedOptions);
      handlePagination == null ? void 0 : handlePagination(updatedOptions);
    }
  });
  const next = items.find(({ type }) => type === "next");
  const previous = items.find(({ type }) => type === "previous");
  const pages = items.filter(
    ({ type }) => type === "page" || type === "start-ellipsis" || type === "end-ellipsis"
  );
  useEffect(() => {
    setOptions(initialOptions);
  }, [initialOptions]);
  return /* @__PURE__ */ jsxs(
    Box,
    {
      padding: 2,
      sx: {
        width: "100%",
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between"
      },
      children: [
        /* @__PURE__ */ jsx(
          Button,
          __spreadProps(__spreadValues({}, previous), {
            startIcon: /* @__PURE__ */ jsx(ArrowBackIcon, {}),
            variant: "outlined",
            type: "button",
            sx: {
              border: "1px solid  #D0D5DD",
              color: "#344054",
              ".&:hover": {
                border: "1px solid  #D0D5DD"
              },
              fontWeight: 500,
              textTransform: "capitalize"
            },
            children: previous == null ? void 0 : previous.type
          })
        ),
        /* @__PURE__ */ jsx(Stack, { flexDirection: "row", children: pages.map((_a, index) => {
          var _b = _a, { page, type, selected } = _b, item = __objRest(_b, ["page", "type", "selected"]);
          if (type === "start-ellipsis" || type === "end-ellipsis") {
            return /* @__PURE__ */ jsx(Button, { children: "..." }, index);
          } else {
            return /* @__PURE__ */ createElement(
              Button,
              __spreadProps(__spreadValues({}, item), {
                variant: "outlined",
                key: index,
                sx: {
                  border: "none",
                  color: selected ? "#2A3339" : "#667085",
                  px: "0px",
                  background: selected ? activeBgColor : "transparent",
                  "&:hover": {
                    border: "none",
                    boxShadow: "none",
                    background: "#667085"
                  },
                  fontWeight: "400",
                  fontSize: "14px",
                  marginRight: "3px"
                }
              }),
              page
            );
          }
        }) }),
        /* @__PURE__ */ jsx(
          Button,
          __spreadProps(__spreadValues({}, next), {
            endIcon: /* @__PURE__ */ jsx(ArrowForwardIcon, {}),
            variant: "outlined",
            type: "button",
            sx: {
              border: "1px solid  #D0D5DD",
              color: "#344054",
              ".&:hover": {
                border: "1px solid  #D0D5DD"
              },
              fontWeight: 500,
              textTransform: "capitalize"
            },
            children: next == null ? void 0 : next.type
          })
        )
      ]
    }
  );
};

// src/components/Table/TableHead.tsx
import {
  Box as Box2,
  TableCell as TableCell2,
  TableHead,
  TableRow,
  TableSortLabel
} from "@mui/material";
import { visuallyHidden } from "@mui/utils";

// src/components/Table/CustomTableCell.tsx
import { styled, TableCell } from "@mui/material";
var CustomTableCell = styled(TableCell)(() => ({
  color: "#667085"
}));

// src/components/Table/TableHead.tsx
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
var CustomTableHeader = ({
  order,
  orderBy,
  rowCount,
  headLabel,
  numSelected,
  onSelectAllClick,
  showCheckbox,
  onRequestSort
}) => {
  const createSortHandler = (property) => (event) => {
    onRequestSort == null ? void 0 : onRequestSort(event, property);
  };
  return /* @__PURE__ */ jsx2(TableHead, { sx: { background: "#F9FAFB" }, children: /* @__PURE__ */ jsxs2(TableRow, { children: [
    showCheckbox && /* @__PURE__ */ jsx2(TableCell2, { padding: "checkbox", children: /* @__PURE__ */ jsx2(
      CustomCheckBox,
      {
        indeterminate: numSelected > 0 && numSelected < rowCount,
        checked: rowCount > 0 && numSelected === rowCount,
        onChange: onSelectAllClick,
        style: {
          padding: "5px"
        }
      }
    ) }),
    headLabel.map((headCell) => {
      var _a;
      return /* @__PURE__ */ jsx2(
        CustomTableCell,
        {
          align: headCell.alignRight ? "right" : headCell.alignCenter ? "center" : "left",
          sortDirection: orderBy === headCell.id ? order : false,
          children: /* @__PURE__ */ jsxs2(
            TableSortLabel,
            {
              hideSortIcon: true,
              active: orderBy === headCell.id,
              direction: orderBy === headCell.id ? order : "asc",
              onClick: createSortHandler((_a = headCell.id) != null ? _a : ""),
              children: [
                headCell.label,
                orderBy === headCell.id ? /* @__PURE__ */ jsx2(Box2, { sx: __spreadValues({}, visuallyHidden), children: order === "desc" ? "sorted descending" : "sorted ascending" }) : null
              ]
            }
          )
        },
        headCell.id
      );
    })
  ] }) });
};
export {
  CustomPagination,
  CustomTableCell,
  CustomTableHeader
};
