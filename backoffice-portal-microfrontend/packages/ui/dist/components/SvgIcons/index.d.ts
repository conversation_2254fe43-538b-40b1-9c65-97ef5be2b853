import { JSX, ComponentProps, FC } from 'react';
import * as react_jsx_runtime from 'react/jsx-runtime';

interface IconProps {
    height?: string;
    width?: string;
    stroke?: string;
}
declare const TariffIcon: ({ height, width, stroke, }: IconProps) => JSX.Element;
declare const NotificationsIcon: ({ height, width, stroke, }: IconProps) => JSX.Element;
declare const EstatementIcon: ({ height, width, stroke, }: IconProps) => JSX.Element;
declare const BalanceAlertIcon: ({ height, width, stroke, }: IconProps) => JSX.Element;
declare const TariffCheckedIcon: ({ height, width, stroke, }: IconProps) => JSX.Element;
declare const RightIcon: ({ height, width, stroke, }: IconProps) => JSX.Element;
declare const LeftIcon: ({ height, width, stroke, }: IconProps) => JSX.Element;

interface IconsProps extends ComponentProps<'svg'> {
    width?: string | number;
    height?: string | number;
    fill?: string;
    stroke?: string;
    strokeWidth?: string;
}

declare const Broker: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ExistingBroker: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ProductCategoriesIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const CheckBoxIcon: ({ width, height, fill, stroke, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const CommentIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const IconClose: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const DeactivateBrokerIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const EditTariffIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const PersonalDetailsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const AccountsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const SummaryIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ConnectorIcon: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const DeviceIcons: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const AccountIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const AccountDetailsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const NotificationDetailsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const StatementDetailsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const SecurityDetailsIcon: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const AccountsSummaryIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const X247Icon: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const EditIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const EmptyFolder: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const Expandless: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const Expandmore: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ExportIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const FileDownloadIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ExportFinalIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const LoadingIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const Home: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const InfoIcon: ({ width, height, fill, stroke, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LandingBar: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const GenerateReportsIcons: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const DownloadCloudIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LoggedInIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LogoPlain: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const InternalNavLogo: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const InternalCollapsedLogo: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LogoWhite: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const PdfIcon: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ExcelIcon: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const CustomProductsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ProfileIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const HomesIcon: ({ width, height }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const KeyIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const CheckIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const LockIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const XIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const CustomShieldIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const HomeIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ChevronRightIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ChevronRightIconSingle: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const MenuIcon: ({ width, height, fill, stroke, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const RequestsApprovalIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const StaffUsersIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const CustomersIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const TransactionLimitsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ChargesIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ConfigurationItemIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const TarrifsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const BeneficiaryBanksIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const BranchesIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const LogsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ReportsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const AuditTrailIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const AdvertisingIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const NavMenuIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const RequestsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const SettingsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const TransactionHistoryIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const CreditCardIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const DebitCardIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const PrepaidCardIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const CardHeaderIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const UploadIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const TrashIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const AlertCircleIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const UsersIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const UserProfileIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const CustomerSettingsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const UserEditIcon: ({ width, height, fill, stroke, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const UserDeleteIcon: ({ width, height, fill, stroke, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LandingVector: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const EmptySearchVector: ({ width, height, fill, stroke, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ShoppingCartIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LinkExternalIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const DateIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const DotsVerticalIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const DeleteIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const HelpIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const PaymentsPageIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const CompaniesIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const SettingsIconEatta: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const SuccessIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ErrorIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const StepDoneIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const StepInactiveIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const StepActiveIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const CheckCompleteIcon: ({ width, height, fill, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const FolderDownloadIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const MoreVert: FC<IconsProps>;

declare const TarrifIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const PaymentService: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const PaymentServiceIcon2: ({ width, height, }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const NotificationTarrifIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const StatsIcon: ({ width, height, fill, stroke, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const KESIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const USDIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const EURIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const GBPIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ZARIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const TZSIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const UGXIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const BIFIcon: ({ width, height, fill, }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ChargeConfigIcon: () => react_jsx_runtime.JSX.Element;
declare const PercentChargeIcon: () => react_jsx_runtime.JSX.Element;
declare const LimitIcon: () => react_jsx_runtime.JSX.Element;
declare const TierChargeIcon: () => react_jsx_runtime.JSX.Element;
declare const ChargeIcon: () => react_jsx_runtime.JSX.Element;
declare const CheckConfigIcon: () => react_jsx_runtime.JSX.Element;
declare const EditConfigIcon: () => react_jsx_runtime.JSX.Element;
declare const DeleteConfigIcon: () => react_jsx_runtime.JSX.Element;

declare const RightsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const NotificationIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const CardsIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const BackIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const AddNotificationIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const MobileIcon: ({ width, height, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const LMSIcon: ({ width, height, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;
declare const ApprovalsIcon: ({ width, height, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const OutlinedSettingsIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ArrowRightIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const BoldIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ItalicsIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LargeHeadingIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const SmallHeadingIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const QuoteIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const LinkIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const InsertImageIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const BulletsIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const NumberingIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

/**
 * <AUTHOR> Kinyoro on 29/01/2025
 */
declare const LogoDark: ({ width, height }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ConfigureIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const CalendarIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ReceiptIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const StatementsIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const EmailIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const PhoneIcon: ({ width, height, fill, stroke, strokeWidth, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

declare const ChevronLeftIcon: ({ width, height, fill, stroke, strokeWidth, className, ...rest }: IconsProps) => react_jsx_runtime.JSX.Element;

export { AccountDetailsIcon, AccountIcon, AccountsIcon, AccountsSummaryIcon, AddNotificationIcon, AdvertisingIcon, AlertCircleIcon, ApprovalsIcon, ArrowRightIcon, AuditTrailIcon, BIFIcon, BackIcon, BalanceAlertIcon, BeneficiaryBanksIcon, BoldIcon, BranchesIcon, Broker, BulletsIcon, CalendarIcon, CardHeaderIcon, CardsIcon, ChargeConfigIcon, ChargeIcon, ChargesIcon, CheckBoxIcon, CheckCompleteIcon, CheckConfigIcon, CheckIcon, ChevronLeftIcon, ChevronRightIcon, ChevronRightIconSingle, CommentIcon, CompaniesIcon, ConfigurationItemIcon, ConfigureIcon, ConnectorIcon, CreditCardIcon, CustomProductsIcon, CustomShieldIcon, CustomerSettingsIcon, CustomersIcon, DateIcon, DeactivateBrokerIcon, DebitCardIcon, DeleteConfigIcon, DeleteIcon, DeviceIcons, DotsVerticalIcon, DownloadCloudIcon, EURIcon, EditConfigIcon, EditIcon, EditTariffIcon, EmailIcon, EmptyFolder, EmptySearchVector, ErrorIcon, EstatementIcon, ExcelIcon, ExistingBroker, Expandless, Expandmore, ExportFinalIcon, ExportIcon, FileDownloadIcon, FolderDownloadIcon, GBPIcon, GenerateReportsIcons, HelpIcon, Home, HomeIcon, HomesIcon, IconClose, InfoIcon, InsertImageIcon, InternalCollapsedLogo, InternalNavLogo, ItalicsIcon, KESIcon, KeyIcon, LMSIcon, LandingBar, LandingVector, LargeHeadingIcon, LeftIcon, LimitIcon, LinkExternalIcon, LinkIcon, LoadingIcon, LockIcon, LoggedInIcon, LogoDark, LogoPlain, LogoWhite, LogsIcon, MenuIcon, MobileIcon, MoreVert, NavMenuIcon, NotificationDetailsIcon, NotificationIcon, NotificationTarrifIcon, NotificationsIcon, NumberingIcon, OutlinedSettingsIcon, PaymentService, PaymentServiceIcon2, PaymentsPageIcon, PdfIcon, PercentChargeIcon, PersonalDetailsIcon, PhoneIcon, PrepaidCardIcon, ProductCategoriesIcon, ProfileIcon, QuoteIcon, ReceiptIcon, ReportsIcon, RequestsApprovalIcon, RequestsIcon, RightIcon, RightsIcon, SecurityDetailsIcon, SettingsIcon, SettingsIconEatta, ShoppingCartIcon, SmallHeadingIcon, StaffUsersIcon, StatementDetailsIcon, StatementsIcon, StatsIcon, StepActiveIcon, StepDoneIcon, StepInactiveIcon, SuccessIcon, SummaryIcon, TZSIcon, TariffCheckedIcon, TariffIcon, TarrifIcon, TarrifsIcon, TierChargeIcon, TransactionHistoryIcon, TransactionLimitsIcon, TrashIcon, UGXIcon, USDIcon, UploadIcon, UserDeleteIcon, UserEditIcon, UserProfileIcon, UsersIcon, X247Icon, XIcon, ZARIcon };
