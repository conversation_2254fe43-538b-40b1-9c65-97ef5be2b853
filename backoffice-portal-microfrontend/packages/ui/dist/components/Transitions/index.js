import {
  __spreadProps,
  __spreadValues
} from "../../chunk-BBZEL7EG.js";

// src/components/Transitions/GrowProvider.tsx
import { Grow } from "@mui/material";
import { jsx } from "react/jsx-runtime";
var GrowProvider = ({
  children,
  in: isIn
}) => {
  if (!children) {
    return null;
  }
  return /* @__PURE__ */ jsx(Grow, __spreadProps(__spreadValues({ in: isIn }, isIn ? { timeout: 600 } : {}), { children }));
};
export {
  GrowProvider
};
