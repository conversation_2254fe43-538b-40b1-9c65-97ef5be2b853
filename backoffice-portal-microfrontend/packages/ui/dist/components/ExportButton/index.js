import {
  ExportIcon,
  FileDownloadIcon
} from "../../chunk-YMEOZMFS.js";
import "../../chunk-BBZEL7EG.js";

// src/components/ExportButton/ExportButton.tsx
import Button from "@mui/material/Button";
import { jsx } from "react/jsx-runtime";
var ExportButton = ({
  onClick,
  text,
  openFilter
}) => {
  return /* @__PURE__ */ jsx(
    Button,
    {
      onClick,
      type: "submit",
      startIcon: openFilter ? /* @__PURE__ */ jsx(FileDownloadIcon, {}) : /* @__PURE__ */ jsx(ExportIcon, {}),
      size: "medium",
      variant: "contained",
      sx: {
        backgroundColor: "#E7E8E9",
        color: "#555C61",
        paddingX: "44px",
        paddingY: "8px",
        fontSize: "1rem",
        fontWeight: "medium",
        textTransform: "none",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        textWrap: "noWrap",
        boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
        "&:hover": {
          backgroundColor: "#E7E8E9"
        }
      },
      children: text
    }
  );
};
export {
  ExportButton
};
