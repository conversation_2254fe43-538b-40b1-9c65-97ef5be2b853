import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default from 'react';
export { C as CustomFilterBoxProps, a as CustomerModuleSearchFilterBox, I as IFilterOption } from '../../CustomerModuleSearchFilter-O-Oq0KOj.js';
import { FormControlLabelProps, OutlinedInputProps, SxProps, Theme, InputBaseProps } from '@mui/material';
import { StyledComponent } from '@emotion/styled';

interface IEmail {
    email: string;
    firstName: string;
    lastName: string;
}
declare const emails: IEmail[];
interface IAutoComplete<T, K extends keyof T> {
    label: string;
    options: T[];
    value?: T | null;
    optionLabel: K;
    fullWidth?: boolean;
    isOptionEqualToValue?: (option: T, value: T | null) => boolean;
    onChange: (event: React__default.SyntheticEvent<Element, Event>, value: T | null) => void;
    renderListItem: (option: T) => React__default.ReactNode;
}
declare const AutoComplete: <T, K extends keyof T>({ label, options, value, optionLabel, fullWidth, onChange, renderListItem, isOptionEqualToValue, }: IAutoComplete<T, K>) => react_jsx_runtime.JSX.Element;

declare const CustomFormControlLabel: StyledComponent<FormControlLabelProps>;

interface CustomSearchByInputProps<T> {
    searchByDropDownItems: {
        label: string;
        value: Array<keyof T>;
    }[];
    width: string;
    onKeyDown?: (e: React__default.KeyboardEvent<HTMLInputElement>) => void;
    value?: string;
    searchByValue: {
        label: string;
        value: Array<keyof T>;
    };
    onChange?: (searchText: string) => void;
    onSearchButtonClick?: () => void;
    onSearchBySelect?: (value: {
        label: string;
        value: Array<keyof T>;
    }) => void;
    placeholder?: string;
}
declare const CustomSearchByInput: <T>({ searchByDropDownItems, width, onChange, value, onKeyDown, onSearchBySelect, placeholder, }: CustomSearchByInputProps<T>) => react_jsx_runtime.JSX.Element;

interface CustomSearchInputProps extends OutlinedInputProps {
    width?: string;
    height?: string | number;
    bgColor?: string;
}
declare const CustomSearchInput: StyledComponent<CustomSearchInputProps>;
interface CustomSearchBoxProps<T> {
    startAdornment?: React__default.ReactNode;
    endAdornment?: React__default.ReactNode;
    onKeyDown?: (e: React__default.KeyboardEvent<HTMLInputElement>) => void;
    value?: string;
    onChange?: (e: React__default.ChangeEvent<HTMLInputElement>) => void;
    onSearchButtonClick?: () => void;
    placeholder?: string;
    sx?: SxProps<Theme>;
}
declare const CustomSearchBox: <T>({ startAdornment, endAdornment, onKeyDown, value, onChange, placeholder, sx, }: CustomSearchBoxProps<T>) => react_jsx_runtime.JSX.Element;

declare const InputField: StyledComponent<InputBaseProps>;

interface FileUploadProps {
    name: string;
    initialFileName?: string;
    initialFile?: File | string;
    onFileUpload: (file: File | null) => void;
}
declare const UploadDocumentForm: ({ name, initialFileName, initialFile, onFileUpload, }: FileUploadProps) => react_jsx_runtime.JSX.Element;

interface MultiSelectAutocompleteProps<T> {
    label: string;
    options: T[];
    selectedItems: T[];
    getOptionLabel: (option: T) => string;
    onChange: (event: unknown, newValue: T[]) => void;
    onDelete: (item: T) => void;
    isOptionEqualToValue: (option: T, value: T) => boolean;
    renderOption?: (props: React__default.HTMLAttributes<HTMLLIElement>, option: T) => React__default.ReactNode;
}
declare const MultiSelectAutocomplete: <T>({ label, options, selectedItems, getOptionLabel, onChange, onDelete, isOptionEqualToValue, renderOption, }: MultiSelectAutocompleteProps<T>) => react_jsx_runtime.JSX.Element;

export { AutoComplete, CustomFormControlLabel, CustomSearchBox, CustomSearchByInput, CustomSearchInput, type IAutoComplete, type IEmail, InputField, MultiSelectAutocomplete, UploadDocumentForm, emails };
