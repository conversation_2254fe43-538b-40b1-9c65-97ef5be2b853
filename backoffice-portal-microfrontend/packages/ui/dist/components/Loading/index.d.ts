import * as react_jsx_runtime from 'react/jsx-runtime';
import { SkeletonProps } from '@mui/material';
import { StyledComponent } from '@emotion/styled';

/**
 * <AUTHOR> on 19/09/2024
 */
declare const LoadingListsSkeleton: () => react_jsx_runtime.JSX.Element;
declare const LoadingCardListSkeleton: () => react_jsx_runtime.JSX.Element;
declare const LoadingFullScreen: () => react_jsx_runtime.JSX.Element;

interface LoadingButtonProps {
    width?: string;
    height?: string;
    size?: 'small' | 'medium' | 'large';
}
declare const LoadingButton: ({ width, height, size, ...rest }: LoadingButtonProps) => react_jsx_runtime.JSX.Element;

declare const CustomSkeleton: StyledComponent<SkeletonProps>;

export { CustomSkeleton, LoadingButton, LoadingCardListSkeleton, LoadingFullScreen, LoadingListsSkeleton };
