import {
  HomeIcon
} from "../../chunk-YMEOZMFS.js";
import "../../chunk-BBZEL7EG.js";

// src/components/Breacrumb/AppBreadcrumb.tsx
import { Breadcrumbs, Typography } from "@mui/material";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { jsx } from "react/jsx-runtime";
{
}
var AppBreadcrumb = () => {
  const pathName = usePathname();
  const pathNames = pathName.split("/");
  pathNames.shift();
  return /* @__PURE__ */ jsx(
    Breadcrumbs,
    {
      "aria-label": "breadcrumb",
      sx: { textTransform: "capitalize" },
      separator: /* @__PURE__ */ jsx(NavigateNextIcon, { color: "disabled", fontSize: "small" }),
      children: pathNames.map((name, index) => {
        const first = index === 0;
        const last = index === pathNames.length - 1;
        const to = `/${pathNames.slice(0, index + 1).join("/")}`;
        const home = `/${pathNames.slice(0, index + 1).join("/")}`;
        return first ? /* @__PURE__ */ jsx(
          Link,
          {
            color: "inherit",
            href: home,
            style: { display: "flex" },
            children: /* @__PURE__ */ jsx(HomeIcon, {})
          },
          index
        ) : last ? /* @__PURE__ */ jsx(Typography, { sx: { color: "primary.main" }, children: name }, index) : /* @__PURE__ */ jsx(Link, { href: to, style: { textDecoration: "none" }, children: /* @__PURE__ */ jsx(Typography, { sx: { color: "text.main" }, children: name }) }, index);
      })
    }
  );
};
export {
  AppBreadcrumb
};
