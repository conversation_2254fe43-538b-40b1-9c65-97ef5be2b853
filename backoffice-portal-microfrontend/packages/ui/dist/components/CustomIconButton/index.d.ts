import React__default from 'react';
import { IconButtonProps } from '@mui/material';

interface ICustomIconButtonProps extends IconButtonProps {
    icon: React__default.FC;
    width?: number;
    borderRadius?: string;
    color?: IconButtonProps['color'];
    border?: string;
    backgroundColor?: string;
    onClick?: () => void;
}
declare const CustomIconButton: React__default.FC<ICustomIconButtonProps>;

export { CustomIconButton };
