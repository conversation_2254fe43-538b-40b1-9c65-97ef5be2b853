export { InActivity } from './InActivity/index.js';
import * as react_jsx_runtime from 'react/jsx-runtime';
import { TextFieldProps } from '@mui/material';
export { CustomScrollbar } from './Scrollbar/index.js';
export { CustomError } from './CustomError/index.js';
export { AuthWrapper, AuthWrapperProps } from './AuthWrapper/index.js';
export { LocalNotification } from './SnackBar/index.js';
export { Logo } from './Logo/index.js';
export { CustomIconButton } from './CustomIconButton/index.js';
export { CustomSkeleton, LoadingButton, LoadingCardListSkeleton, LoadingFullScreen, LoadingListsSkeleton } from './Loading/index.js';
import React__default, { FC } from 'react';
import PropTypes from 'prop-types';
import { TabsOwnProps } from '@mui/material/Tabs';
import { TabOwnProps } from '@mui/material/Tab';
import { TabsListOwnProps } from '@mui/base/TabsList';
import { TabPanelOwnProps } from '@mui/base/TabPanel';
export { C as CustomFilterBoxProps, a as CustomerModuleSearchFilterBox, I as IFilterOption } from '../CustomerModuleSearchFilter-O-Oq0KOj.js';
export { CustomAccordionWithDropdown, DropDownComponent, IAccordionProps, ICategory, IDropDownProps } from './Accordion/index.js';
import '@emotion/styled';

declare const ReadOnlyTypography: (props: TextFieldProps) => react_jsx_runtime.JSX.Element;

declare function CustomTabPanel({ children, value, index, ...other }: {
    [x: string]: unknown;
    children: React__default.ReactNode;
    value: number;
    index: number;
}): react_jsx_runtime.JSX.Element;
declare namespace CustomTabPanel {
    var propTypes: {
        children: PropTypes.Requireable<PropTypes.ReactNodeLike>;
        index: PropTypes.Validator<any>;
        value: PropTypes.Validator<any>;
    };
}
declare const CustomToggleTabs: FC<TabsOwnProps>;
declare const CustomAntTab: FC<TabOwnProps>;

interface ITableData {
    id: string;
    event: string;
    eventSource?: string;
    eventDate: string;
    maker?: string;
    makerTimestamp?: string;
    checker?: string;
    checkerTimestamp?: string;
}
declare const TabPanel: FC<TabPanelOwnProps>;
declare const TabsList: FC<TabsListOwnProps>;

export { CustomAntTab, CustomTabPanel, CustomToggleTabs, type ITableData, ReadOnlyTypography, TabPanel, TabsList };
