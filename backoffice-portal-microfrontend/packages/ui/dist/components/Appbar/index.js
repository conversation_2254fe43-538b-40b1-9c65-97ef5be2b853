import {
  LogoPlain
} from "../../chunk-YMEOZMFS.js";
import "../../chunk-BBZEL7EG.js";

// src/components/Appbar/AppBar.tsx
import {
  AppBar,
  Box,
  Chip,
  IconButton,
  Link,
  Menu,
  MenuItem,
  Paper,
  Toolbar,
  Tooltip
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import { jsx, jsxs } from "react/jsx-runtime";
var Navbar = ({ profile, refreshToken }) => {
  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };
  const formRef = useRef(null);
  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };
  const [anchorElUser, setAnchorElUser] = useState(null);
  useEffect(() => {
    const refresh = setInterval(refreshToken, 1e3 * 60 * 3);
    return () => clearInterval(refresh);
  }, []);
  const handleLogout = (e) => {
    var _a;
    e.preventDefault();
    (_a = formRef.current) == null ? void 0 : _a.submit();
    localStorage.clear();
    sessionStorage.clear();
  };
  return /* @__PURE__ */ jsx(
    AppBar,
    {
      position: "static",
      sx: {
        boxShadow: "none",
        backgroundColor: "#FCFCFC",
        borderBottom: "1px solid #E3E4E4"
      },
      children: /* @__PURE__ */ jsx(
        Paper,
        {
          elevation: 0,
          sx: {
            paddingLeft: "1%",
            paddingRight: "2%",
            borderBottom: "1px solid #E3E4E4"
          },
          children: /* @__PURE__ */ jsx(
            Toolbar,
            {
              disableGutters: true,
              sx: {
                display: "flex",
                flexDirection: "column",
                border: "none",
                boxShadow: "none"
              },
              children: /* @__PURE__ */ jsxs(
                Box,
                {
                  sx: {
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                    width: "100%",
                    height: "100%",
                    gap: "10px"
                  },
                  children: [
                    /* @__PURE__ */ jsx(
                      Box,
                      {
                        sx: {
                          width: "243px",
                          height: "100%",
                          display: "flex"
                        },
                        children: /* @__PURE__ */ jsx(Link, { href: "/landing/", children: /* @__PURE__ */ jsx(LogoPlain, {}) })
                      }
                    ),
                    /* @__PURE__ */ jsxs(
                      Box,
                      {
                        sx: {
                          display: "flex",
                          flexDirection: "row",
                          justifyContent: "flex-end",
                          gap: "10px",
                          width: "50%",
                          height: "100%",
                          alignItems: "center",
                          marginTop: "10px"
                        },
                        children: [
                          /* @__PURE__ */ jsx(Tooltip, { title: "Open profile", children: /* @__PURE__ */ jsx(
                            IconButton,
                            {
                              onClick: handleOpenUserMenu,
                              sx: {
                                p: 0,
                                borderRadius: "16px"
                              },
                              children: /* @__PURE__ */ jsx(
                                Chip,
                                {
                                  icon: /* @__PURE__ */ jsx(
                                    AccountCircleIcon,
                                    {
                                      sx: {
                                        color: "#000A12",
                                        fontSize: 32,
                                        marginLeft: "0px !important"
                                      }
                                    }
                                  ),
                                  label: `${profile && profile.first_name || ""} ${profile && profile.last_name || ""}`,
                                  sx: {
                                    fontWeight: "500",
                                    fontSize: "14px",
                                    backgroundColor: "#EAECF0",
                                    gap: "8px"
                                  }
                                }
                              )
                            }
                          ) }),
                          /* @__PURE__ */ jsxs(
                            "form",
                            {
                              method: "POST",
                              ref: formRef,
                              action: `${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/users/logout`,
                              children: [
                                /* @__PURE__ */ jsx(
                                  "input",
                                  {
                                    type: "hidden",
                                    name: "redirectUrl",
                                    value: `${process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL}`
                                  }
                                ),
                                /* @__PURE__ */ jsx(
                                  Menu,
                                  {
                                    sx: {
                                      mt: "45px",
                                      display: "flex",
                                      flexDirection: "column"
                                    },
                                    id: "menu-appbar",
                                    anchorEl: anchorElUser,
                                    anchorOrigin: {
                                      vertical: "top",
                                      horizontal: "right"
                                    },
                                    keepMounted: true,
                                    transformOrigin: {
                                      vertical: "top",
                                      horizontal: "right"
                                    },
                                    open: Boolean(anchorElUser),
                                    onClose: handleCloseUserMenu,
                                    children: /* @__PURE__ */ jsx(
                                      MenuItem,
                                      {
                                        component: "button",
                                        type: "submit",
                                        onClick: (e) => handleLogout(e),
                                        children: "Logout"
                                      }
                                    )
                                  }
                                )
                              ]
                            }
                          )
                        ]
                      }
                    )
                  ]
                }
              )
            }
          )
        }
      )
    }
  );
};

// src/components/Appbar/InternalNav.tsx
import {
  AppBar as AppBar2,
  Chip as Chip2,
  IconButton as IconButton2,
  Menu as Menu2,
  MenuItem as MenuItem2,
  Toolbar as Toolbar2,
  Tooltip as Tooltip2
} from "@mui/material";
import { useEffect as useEffect2, useRef as useRef2, useState as useState2 } from "react";
import AccountCircleIcon2 from "@mui/icons-material/AccountCircle";
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
var InternalNavBar = ({
  profile,
  refreshToken,
  refreshInterval = 1e3 * 60 * 20
}) => {
  const handleOpenUserMenu = (event) => {
    setAnchorElUser(event.currentTarget);
  };
  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };
  const formRef = useRef2(null);
  const [anchorElUser, setAnchorElUser] = useState2(null);
  useEffect2(() => {
    const refresh = setInterval(refreshToken, refreshInterval);
    return () => clearInterval(refresh);
  }, []);
  const handleLogout = (e) => {
    var _a;
    e.preventDefault();
    (_a = formRef.current) == null ? void 0 : _a.submit();
    localStorage.clear();
    sessionStorage.clear();
  };
  return /* @__PURE__ */ jsx2(
    AppBar2,
    {
      position: "static",
      sx: {
        boxShadow: "none",
        height: "70px",
        backgroundColor: "#FFFFFF",
        px: "2%",
        display: "flex",
        flexDirection: "row",
        borderBottom: "1px solid #E3E4E4",
        justifyContent: "space-between"
      },
      children: /* @__PURE__ */ jsxs2(
        Toolbar2,
        {
          disableGutters: true,
          sx: {
            justifyContent: "flex-end",
            alignItems: "centre",
            border: "none",
            boxShadow: "none",
            width: "100%"
          },
          children: [
            /* @__PURE__ */ jsx2(Tooltip2, { title: "Open profile", children: /* @__PURE__ */ jsx2(
              IconButton2,
              {
                onClick: handleOpenUserMenu,
                sx: {
                  p: 0,
                  borderRadius: "16px"
                },
                children: /* @__PURE__ */ jsx2(
                  Chip2,
                  {
                    icon: /* @__PURE__ */ jsx2(
                      AccountCircleIcon2,
                      {
                        sx: {
                          color: "#000A12",
                          fontSize: 32,
                          marginLeft: "0px !important"
                        }
                      }
                    ),
                    label: `${profile && profile.first_name || ""} ${profile && profile.last_name || profile.username}`,
                    sx: {
                      fontWeight: "500",
                      fontSize: "14px",
                      backgroundColor: "#EAECF0",
                      gap: "8px"
                    }
                  }
                )
              }
            ) }),
            /* @__PURE__ */ jsxs2(
              "form",
              {
                method: "POST",
                ref: formRef,
                action: `${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/users/logout`,
                children: [
                  /* @__PURE__ */ jsx2(
                    "input",
                    {
                      type: "hidden",
                      name: "redirectUrl",
                      value: `${process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL}`
                    }
                  ),
                  /* @__PURE__ */ jsx2(
                    Menu2,
                    {
                      sx: {
                        mt: "45px",
                        display: "flex",
                        flexDirection: "column"
                      },
                      id: "menu-appbar",
                      anchorEl: anchorElUser,
                      anchorOrigin: {
                        vertical: "top",
                        horizontal: "right"
                      },
                      keepMounted: true,
                      transformOrigin: {
                        vertical: "top",
                        horizontal: "right"
                      },
                      open: Boolean(anchorElUser),
                      onClose: handleCloseUserMenu,
                      children: /* @__PURE__ */ jsx2(
                        MenuItem2,
                        {
                          component: "button",
                          type: "submit",
                          onClick: (e) => handleLogout(e),
                          children: "Logout"
                        }
                      )
                    }
                  )
                ]
              }
            )
          ]
        }
      )
    }
  );
};
export {
  InternalNavBar,
  Navbar
};
