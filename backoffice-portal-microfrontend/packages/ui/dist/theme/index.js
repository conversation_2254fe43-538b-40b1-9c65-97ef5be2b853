import {
  __spreadProps,
  __spreadValues
} from "../chunk-BBZEL7EG.js";

// src/theme/CustomTheme.tsx
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { CssBaseline } from "@mui/material";

// src/theme/CreatePallete.tsx
var brandThemePalette = {
  primary: {
    main: "#e6452f",
    primary2: "#f92406",
    primary3: "#ff2e0d",
    primary4: "#ff5d39",
    primary5: "#ff7e5d",
    primary6: "#ffa48c",
    primary7: "#ffc9b9",
    contrastText: "#ffe8e6"
  },
  secondary: {
    main: "#000A12",
    secondary2: "#2A3339",
    secondary3: "#555C61",
    secondary4: "#808488",
    secondary5: "#AAADB0",
    contrastText: "#FFFFFF"
  }
};
var greenThemePalette = {
  primary: {
    main: "#00BC2D",
    primary2: "#26b43b",
    primary3: "#50c05b",
    primary4: "#73cb79",
    primary5: "#9dd9a0",
    primary6: "#c4e8c5",
    primary7: "#e6f6e7",
    contrastText: "#FFFFFF"
  },
  secondary: {
    main: "#000A12",
    secondary2: "#2A3339",
    secondary3: "#555C61",
    secondary4: "#808488",
    secondary5: "#AAADB0",
    contrastText: "#FFFFFF"
  }
};
function createPalette() {
  return {
    primary: {
      main: "#000A12",
      primary2: "#2A3339",
      primary3: "#555C61",
      primary4: "#808488",
      primary5: "#AAADB0",
      primary6: "#BDC0C3",
      primary7: "#E7E8E9",
      contrastText: "#FFFFFF"
    },
    secondary: {
      main: "#EB0045",
      secondary2: "#F13C57",
      secondary3: "#FA727B",
      secondary4: "#FF9DA0",
      secondary5: "#FFC5C5",
      contrastText: "#FFFFFF"
    },
    neutral: {
      main: "#E3E4E4",
      neutral2: "#F1F1F2",
      neutral3: "#F7F7F7",
      neutral4: "#FCFCFC",
      neutral5: "#FFFFFF"
    },
    text: {
      primary: "#000A12",
      secondary: "#2A3339",
      tertiary: "#475467",
      disabled: "#BDC0C3"
    },
    error: {
      main: "#EB0045"
    },
    success: {
      lighter: "#ECFDF3",
      light: "#12B76A",
      main: "#027A48"
    },
    warning: {
      main: "#FDB022"
    },
    info: {
      main: "#0000FF"
    }
  };
}

// src/theme/CreateComponents.tsx
function createComponents(theme) {
  return {
    // MuiInputBase: {
    //   styleOverrides: {
    //     root: {
    //       borderRadius: '4px',
    //       border: '1px solid #E3E4E4',
    //       boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
    //     },
    //   },
    // },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none",
          variants: []
        },
        containedPrimary: {
          borderRadius: "6px",
          boxShadow: "none",
          maxHeight: "40px",
          background: theme.palette.primary.main,
          "&:hover": {
            background: theme.palette.primary.primary2,
            boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)"
          }
        },
        containedSecondary: {
          borderRadius: "6px",
          boxShadow: "none",
          maxHeight: "40px",
          background: theme.palette.secondary.main,
          "&:hover": {
            background: theme.palette.secondary.secondary2,
            boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)"
          }
        },
        outlinedPrimary: {
          color: theme.palette.primary.primary3,
          borderRadius: "6px",
          maxHeight: "40px",
          border: `2px solid ${theme.palette.primary.primary3}`,
          background: "#FFFFFF",
          "&:hover": {
            border: `1.5px solid ${theme.palette.primary.main}`,
            color: theme.palette.primary.main,
            boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)"
          }
        },
        outlinedSecondary: {
          color: theme.palette.secondary.secondary3,
          borderRadius: "6px",
          maxHeight: "40px",
          border: `2px solid ${theme.palette.secondary.secondary3}`,
          background: "#FFFFFF",
          "&:hover": {
            border: `1.5px solid ${theme.palette.secondary.main}`,
            color: theme.palette.secondary.main,
            boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)"
          }
        },
        textPrimary: {
          color: theme.palette.primary.main,
          gap: "8px"
        },
        sizeSmall: {
          padding: "8px 28px",
          gap: "8px"
        },
        sizeMedium: {
          padding: "10px 42px",
          gap: "10px"
        },
        sizeLarge: {
          padding: "16px 64px",
          gap: "14px"
        }
      }
    },
    // MuiTextField: {
    //   styleOverrides: {
    //     root: {},
    //   },
    // },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          // marginBottom: '5px',
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: "#E3E4E4"
          },
          "&.Mui-disabled": {
            "& .MuiOutlinedInput-notchedOutline": {
              borderColor: "#E3E4E4"
            }
          }
        }
      }
    }
  };
}

// src/theme/CreateTypography.tsx
function getFontFamily(themeType) {
  let fontFamily = "BlissPro";
  if (themeType === "masterLink") {
    fontFamily = "Work Sans";
  }
  return fontFamily;
}
var Typography = (theme, themeType) => {
  return {
    fontFamily: getFontFamily(themeType),
    h1: {
      fontWeight: 500,
      lineHeight: "64px",
      fontSize: "57px",
      color: theme.palette.text.primary
    },
    h2: {
      fontWeight: 500,
      lineHeight: "52px",
      fontSize: "45px",
      color: theme.palette.text.primary
    },
    h3: {
      fontWeight: 500,
      fontSize: "36px",
      lineHeight: "44px",
      color: theme.palette.text.primary
    },
    h4: {
      fontWeight: 500,
      lineHeight: "40px",
      fontSize: "32px",
      color: theme.palette.text.primary
    },
    h5: {
      fontWeight: 500,
      lineHeight: "36px",
      fontSize: "28px",
      color: theme.palette.text.primary
    },
    h6: {
      lineHeight: "32px",
      fontSize: "24px",
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    subtitle1: {
      lineHeight: "28px",
      fontSize: "22px",
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    subtitle2: {
      lineHeight: "24px",
      fontSize: "16px",
      fontWeight: 500,
      color: theme.palette.text.secondary
    },
    subtitle3: {
      lineHeight: "20px",
      fontSize: "14px",
      fontWeight: 500,
      color: theme.palette.text.secondary
    },
    body1: {
      lineHeight: "24px",
      fontSize: "16px",
      fontWeight: 500,
      color: "#475467"
    },
    body2: {
      lineHeight: "20px",
      fontSize: "14px",
      fontWeight: 500,
      color: theme.palette.text.secondary
    },
    body3: {
      lineHeight: "16px",
      fontSize: "12px",
      fontWeight: 500,
      color: theme.palette.text.secondary
    },
    label1: {
      lineHeight: "20px",
      fontSize: "14px",
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    label2: {
      lineHeight: "16px",
      fontSize: "12px",
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    label3: {
      lineHeight: "16px",
      fontSize: "11px",
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    button: {
      lineHeight: "24px",
      fontSize: "16px",
      fontWeight: 700,
      color: theme.palette.text.primary
    },
    caption: {
      letterSpacing: "0.4px",
      color: theme.palette.text.secondary
    }
  };
};

// src/theme/CustomTheme.tsx
import { jsx, jsxs } from "react/jsx-runtime";
var BREAKPOINTS = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1440
};
function getPalette(themeType) {
  let palette = createPalette();
  if (themeType === "green") {
    palette = __spreadProps(__spreadValues({}, palette), {
      primary: greenThemePalette.primary,
      secondary: greenThemePalette.secondary
    });
  } else if (themeType === "brand") {
    palette = __spreadProps(__spreadValues({}, palette), {
      primary: brandThemePalette.primary,
      secondary: brandThemePalette.secondary
    });
  }
  return palette;
}
function createCustomTheme(themeType) {
  const palette = getPalette(themeType);
  const theme = createTheme({ palette });
  const components = createComponents(theme);
  const typography = Typography(theme, themeType);
  return createTheme({
    breakpoints: { values: BREAKPOINTS },
    components,
    palette,
    // shadows,
    typography
  });
}
function ThemeConfig({
  children,
  themeType = "main"
}) {
  const theme = createCustomTheme(themeType);
  return /* @__PURE__ */ jsxs(ThemeProvider, { theme, children: [
    /* @__PURE__ */ jsx(CssBaseline, {}),
    children
  ] });
}

// src/theme/EmotionCache.tsx
import * as React from "react";
import createCache from "@emotion/cache";
import { useServerInsertedHTML } from "next/navigation";
import { CacheProvider as DefaultCacheProvider } from "@emotion/react";
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
function NextAppDirEmotionCacheProvider(props) {
  const { options, CacheProvider = DefaultCacheProvider, children } = props;
  const [registry] = React.useState(() => {
    const cache = createCache(options);
    cache.compat = true;
    const prevInsert = cache.insert;
    let inserted = [];
    cache.insert = (...args) => {
      const [selector, serialized] = args;
      if (cache.inserted[serialized.name] === void 0) {
        inserted.push({
          name: serialized.name,
          isGlobal: !selector
        });
      }
      return prevInsert(...args);
    };
    const flush = () => {
      const prevInserted = inserted;
      inserted = [];
      return prevInserted;
    };
    return { cache, flush };
  });
  useServerInsertedHTML(() => {
    const inserted = registry.flush();
    if (inserted.length === 0) {
      return null;
    }
    let styles = "";
    let dataEmotionAttribute = registry.cache.key;
    const globals = [];
    inserted.forEach(({ name, isGlobal }) => {
      const style = registry.cache.inserted[name];
      if (typeof style !== "boolean") {
        if (isGlobal && typeof style === "string") {
          globals.push({ name, style });
        } else {
          styles += style;
          dataEmotionAttribute += ` ${name}`;
        }
      }
    });
    return /* @__PURE__ */ jsxs2(React.Fragment, { children: [
      globals.map(({ name, style }) => /* @__PURE__ */ jsx2(
        "style",
        {
          "data-emotion": `${registry.cache.key}-global ${name}`,
          dangerouslySetInnerHTML: { __html: style }
        },
        name
      )),
      styles && /* @__PURE__ */ jsx2(
        "style",
        {
          "data-emotion": dataEmotionAttribute,
          dangerouslySetInnerHTML: { __html: styles }
        }
      )
    ] });
  });
  return /* @__PURE__ */ jsx2(CacheProvider, { value: registry.cache, children });
}
export {
  NextAppDirEmotionCacheProvider,
  ThemeConfig
};
