import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/components/CustomIconButton/CustomIconButton.tsx
import { IconButton } from "@mui/material";
import { jsx } from "react/jsx-runtime";
var CustomIconButton = (_a) => {
  var _b = _a, {
    icon: Icon,
    width = 40,
    borderRadius = "10px",
    border,
    color = "primary",
    backgroundColor = "transparent",
    onClick
  } = _b, props = __objRest(_b, [
    "icon",
    "width",
    "borderRadius",
    "border",
    "color",
    "backgroundColor",
    "onClick"
  ]);
  return /* @__PURE__ */ jsx(
    IconButton,
    __spreadProps(__spreadValues({
      onClick,
      sx: {
        width,
        height: width,
        borderRadius,
        border,
        color,
        backgroundColor,
        "&:hover": {
          backgroundColor: color === "primary" ? "#d3d3d3" : backgroundColor
        }
      }
    }, props), {
      children: Icon && /* @__PURE__ */ jsx(Icon, {})
    })
  );
};

export {
  CustomIconButton
};
