// src/components/CustomError/CustomError.tsx
import { <PERSON>, Button } from "@mui/material";
import { jsx, jsxs } from "react/jsx-runtime";
function CustomError({ reset }) {
  return /* @__PURE__ */ jsxs(
    Box,
    {
      sx: {
        display: "flex",
        flex: "auto",
        alignItems: "center",
        justifyContent: "center",
        flexDirection: "column",
        height: "100vh"
      },
      children: [
        /* @__PURE__ */ jsx("h2", { children: "Something went wrong!" }),
        /* @__PURE__ */ jsx(Button, { onClick: () => reset(), children: "Try again" })
      ]
    }
  );
}

export {
  CustomError
};
