import {
  __objRest,
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/components/CheckBox/CustomCheckBox.tsx
import { Checkbox, useTheme } from "@mui/material";
import { jsx, jsxs } from "react/jsx-runtime";
var CustomCheckBoxIcon = () => {
  return /* @__PURE__ */ jsx(
    "svg",
    {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      children: /* @__PURE__ */ jsx(
        "path",
        {
          d: "M0.5 6C0.5 2.96243 2.96243 0.5 6 0.5H14C17.0376 0.5 19.5 2.96243 19.5 6V14C19.5 17.0376 17.0376 19.5 14 19.5H6C2.96243 19.5 0.5 17.0376 0.5 14V6Z",
          stroke: "#D0D5DD"
        }
      )
    }
  );
};
var CustomCheckedCheckBoxIcon = ({ fillColor = "#000A12" }) => {
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      width: "20",
      height: "20",
      viewBox: "0 0 20 20",
      fill: "none",
      xmlns: "http://www.w3.org/2000/svg",
      children: [
        /* @__PURE__ */ jsx(
          "path",
          {
            d: "M0 6C0 2.68629 2.68629 0 6 0H14C17.3137 0 20 2.68629 20 6V14C20 17.3137 17.3137 20 14 20H6C2.68629 20 0 17.3137 0 14V6Z",
            fill: fillColor
          }
        ),
        /* @__PURE__ */ jsx(
          "path",
          {
            d: "M14.6666 6.5L8.24992 12.9167L5.33325 10",
            stroke: "white",
            strokeWidth: "2",
            strokeLinecap: "round",
            strokeLinejoin: "round"
          }
        )
      ]
    }
  );
};
var CustomCheckBox = (_a) => {
  var _b = _a, {
    customColor
  } = _b, props = __objRest(_b, [
    "customColor"
  ]);
  const theme = useTheme();
  return /* @__PURE__ */ jsx(
    Checkbox,
    __spreadValues({
      sx: {
        "&:hover": { backgroundColor: "transparent" },
        color: customColor || "default",
        "&.Mui-checked": {
          color: customColor || "primary.main"
        }
      },
      disableRipple: true,
      checkedIcon: /* @__PURE__ */ jsx(
        CustomCheckedCheckBoxIcon,
        {
          fillColor: customColor || theme.palette.primary.main
        }
      ),
      icon: /* @__PURE__ */ jsx(CustomCheckBoxIcon, {}),
      inputProps: { "aria-label": "Checkbox" }
    }, props)
  );
};

export {
  CustomCheckBox
};
