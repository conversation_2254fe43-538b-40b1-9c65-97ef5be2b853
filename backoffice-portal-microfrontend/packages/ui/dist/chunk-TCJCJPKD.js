import {
  LoadingFullScreen
} from "./chunk-QY6BZGO5.js";
import {
  useCustomRouter
} from "./chunk-FW72AQQN.js";

// src/components/AuthWrapper/AuthWrapper.tsx
import { useEffect } from "react";
import { Fragment, jsx } from "react/jsx-runtime";
var AuthWrapper = ({
  children,
  requiresAuth = true,
  isLoggedIn,
  loginUrl = "/",
  homeUrl = "/landing"
}) => {
  const router = useCustomRouter();
  const isAuthenticated = isLoggedIn();
  useEffect(() => {
    if (requiresAuth && !isAuthenticated) {
      console.log("Should route here");
      window.location.assign(loginUrl);
    } else if (!requiresAuth && isAuthenticated) {
      console.log("Should route here");
      router.push(homeUrl);
    }
  }, [isAuthenticated, requiresAuth, router]);
  if (requiresAuth && !isAuthenticated || !requiresAuth && isAuthenticated) {
    return /* @__PURE__ */ jsx(Fragment, { children: /* @__PURE__ */ jsx(LoadingFullScreen, {}) });
  }
  return /* @__PURE__ */ jsx(Fragment, { children });
};

export {
  AuthWrapper
};
