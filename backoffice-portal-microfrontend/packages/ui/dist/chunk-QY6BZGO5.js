import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/components/Loading/Loading.tsx
import { Box } from "@mui/material";

// src/components/Loading/CustomSkeleton.tsx
import { styled } from "@mui/material/styles";
import { Skeleton } from "@mui/material";
var CustomSkeleton = styled(Skeleton)(
  () => ({
    borderRadius: "8px !important"
  })
);
CustomSkeleton.displayName = "CustomSkeleton";

// src/components/Loading/Loading.tsx
import { jsx, jsxs } from "react/jsx-runtime";
var LoadingListsSkeleton = () => {
  return /* @__PURE__ */ jsxs(Box, { children: [
    /* @__PURE__ */ jsx(
      CustomSkeleton,
      {
        animation: "wave",
        variant: "text",
        width: "100%",
        height: "15vh"
      }
    ),
    /* @__PURE__ */ jsx(
      CustomSkeleton,
      {
        animation: "wave",
        variant: "rectangular",
        width: "100%",
        height: "60vh"
      }
    )
  ] });
};
var LoadingCardListSkeleton = () => {
  return /* @__PURE__ */ jsxs(
    Box,
    {
      sx: {
        display: "flex",
        flexDirection: "row",
        gap: "20px",
        py: "3%",
        px: "3%"
      },
      children: [
        /* @__PURE__ */ jsx(
          CustomSkeleton,
          {
            animation: "wave",
            variant: "rectangular",
            width: "33%",
            height: "30vh"
          }
        ),
        /* @__PURE__ */ jsx(
          CustomSkeleton,
          {
            animation: "wave",
            variant: "rectangular",
            width: "33%",
            height: "30vh"
          }
        ),
        /* @__PURE__ */ jsx(
          CustomSkeleton,
          {
            animation: "wave",
            variant: "rectangular",
            width: "33%",
            height: "30vh"
          }
        )
      ]
    }
  );
};
var LoadingFullScreen = () => {
  return /* @__PURE__ */ jsxs(
    Box,
    {
      sx: {
        display: "flex",
        flexDirection: "column",
        gap: "20px",
        py: "1%",
        px: "2%"
      },
      children: [
        /* @__PURE__ */ jsx(
          CustomSkeleton,
          {
            animation: "wave",
            variant: "rectangular",
            width: "100%",
            height: "10vh"
          }
        ),
        /* @__PURE__ */ jsx(
          CustomSkeleton,
          {
            animation: "wave",
            variant: "rectangular",
            width: "100%",
            height: "5vh"
          }
        ),
        /* @__PURE__ */ jsx(
          CustomSkeleton,
          {
            animation: "wave",
            variant: "rectangular",
            width: "100%",
            height: "80vh"
          }
        )
      ]
    }
  );
};

// src/components/Loading/LoadingButton.tsx
import { Button, CircularProgress } from "@mui/material";
import { jsx as jsx2 } from "react/jsx-runtime";
var LoadingButton = (_a) => {
  var _b = _a, { width = "auto", height = "auto", size = "medium" } = _b, rest = __objRest(_b, ["width", "height", "size"]);
  return /* @__PURE__ */ jsx2(
    Button,
    __spreadProps(__spreadValues({
      variant: "contained",
      size,
      disabled: true,
      fullWidth: true,
      sx: {
        py: "1%",
        background: "#EAECF0",
        minWidth: "auto",
        width,
        height,
        borderRadius: "6px",
        padding: size === "small" ? "4px 10px" : size === "medium" ? "6px 4rem" : size === "large" ? "8px 22px" : "6px 16px"
      }
    }, rest), {
      children: /* @__PURE__ */ jsx2(CircularProgress, { color: "primary", size: 20, thickness: 3 })
    })
  );
};

export {
  CustomSkeleton,
  LoadingListsSkeleton,
  LoadingCardListSkeleton,
  LoadingFullScreen,
  LoadingButton
};
