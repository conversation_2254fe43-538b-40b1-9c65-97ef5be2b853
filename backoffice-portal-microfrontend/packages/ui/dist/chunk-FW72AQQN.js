import {
  __spreadProps,
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/hooks/CustomRouter.ts
import { useRouter } from "next/navigation";
function useCustomRouter() {
  const router = useRouter();
  function pushWithTrailingSlash(url) {
    const urlWithTrailingSlash = url.endsWith("/") ? url : `${url}/`;
    router.push(urlWithTrailingSlash);
  }
  return __spreadProps(__spreadValues({}, router), {
    pushWithTrailingSlash
  });
}

// src/hooks/debounceSearch.ts
import { useState, useEffect } from "react";
var useDebounce = (value, delay) => {
  const [debounceValue, setDebounceValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebounceValue(value);
    }, delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounceValue;
};

export {
  useCustomRouter,
  useDebounce
};
