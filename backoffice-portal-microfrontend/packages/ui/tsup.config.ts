/**
 * <AUTHOR> on 12/06/2025
 */

import { defineConfig } from 'tsup'
import { execSync } from 'child_process'

export default defineConfig((options) => ({
  entry: [
    'src/theme/index.ts',
    'src/hooks/index.ts',
    'src/components/index.ts',
    'src/components/*/index.ts',
    'src/components/SvgIcons/index.ts',
  ],
  format: ['esm'],
  dts: true,
  external: [
    'react',
    '@mui/icons-material',
    '@mui/material',
    'next',
    'react-dom',
  ],
  onSuccess: async () => {
    execSync('mkdir -p dist && cp types.d.ts dist/types.d.ts')
  },
  ...options,
}))
