import {
  IUsersResponse,
  IUser,
  ICreateUser,
  IUpdateUser,
  IRole
} from '@dtbx/store/interfaces'

export const usersResponseStub: IUsersResponse = {
  pageNumber: 1,
  pageSize: 10,
  totalNumberOfPages: 3,
  totalElements: 25,
  data: [
    {
      id: 'user1',
      firstName: '<PERSON>',
      lastName: 'Doe',
      middleName: '<PERSON>',
      roles: [],
      email: '<EMAIL>',
      phoneNumber: '1234567890',
      status: 'active',
    },
  ],
}

export const userStub: IUser = {
  id: 'user1',
  firstName: '<PERSON>',
  lastName: 'Doe',
  middleName: '<PERSON>',
  roles: [],
  email: '<EMAIL>',
  phoneNumber: '1234567890',
  status: 'active',
}

export const roleStub: IRole = {
  id: 'role1',
  name: 'Admin',
  description: 'Administrator role',
  creationDate: '2023-01-01',
  custom: false,
  permissions: [],
  permissionsGroup: [],
}

export const createUserStub: ICreateUser = {
  firstName: 'Jane',
  lastName: 'Doe',
  middleName: '<PERSON>',
  roleId: 'role1',
  email: '<EMAIL>',
  phoneNumber: '0745454545',
  comments: 'test comments',
}

export const updateUserStub: IUpdateUser = {
  roleId: 'role1',
  comments: 'test comments',
}

