import {
  ILoanProduct,
  ICustomerLoanProfile,
  ICustomerDocument,
  ICustomerCheck,
  IRepaymentHistory,
  ILoanRequestsSummary,
  ILoanProductCreate,
  IProductCategoryType,
  IOrganization,
  IDecodeToken,
} from '@dtbx/store/interfaces'

export const loanProductStub: ILoanProduct = {
  id: '12345',
  dateCreated: '2023-01-01',
  dateModified: '2023-06-01',
  createdBy: 'test user',
  modifiedBy: 'test modifier',
  code: 'LP-001',
  name: 'Personal Loan',
  country: 'RSA',
  currency: 'RND',
  organization: {
    id: '12345',
    dateCreated: '2023-01-01',
    dateModified: '2023-06-01',
    createdBy: 'test user',
    modifiedBy: 'test modifier',
    name: 'Test Organization',
    cbsIdentifier: '12345',
    limit: '1000000',
  },
  type: {
    id: '12345',
    dateCreated: '2023-01-01',
    dateModified: '2023-06-01',
    createdBy: 'test user',
    modifiedBy: 'test modifier',
    code: 'LP-001',
    name: 'Personal Loan',
  },
  expiryDate: '2025-12-31',
  exposureLimit: 1000000,
  status: 'active',
  customerType: 'individual',
  minimumAmount: 5000,
  maximumAmount: 50000,
  measureOfTenure: 'months',
  minimumTenure: 6,
  maximumTenure: 60,
  interestRateType: 'fixed',
  dynamicInterestFixedComponent: 0,
  interestRate: 5.5,
  facilityFee: 1.5,
  exciseDuty: 0.2,
  facilityFeeRecoveryType: 'upfront',
  interestRecoveryType: 'monthly',
  minimumInterestRecoveryType: 'fixed',
  upfrontInterestRecognitionType: 'accrued',
  minimumInterestCalculationMode: 'simple',
  externalProductName: 'Personal Fixed Loan',
  rollOverFee: 100,
  rollOverPeriod: 12,
  maxRollOverCount: 3,
  prepaymentType: 'partial',
  prepaymentCalculation: 'simple',
  prepaymentValue: 2.5,
  penalty: 1.0,
  interestCalculation: 'simple',
  amortizationMode: 'equal installments',
  multipleDrawDown: true,
  tranches: true,
  trancheInterval: 'quarterly',
  repaymentCycle: 'monthly',
  numberOfInstallments: 24,
  earlyPaymentsAllowed: true,
  periodInArrears: 3,
  minimumInterestValue: 100,
  interestGl: 'GL001',
  facilityFeeGl: 'GL002',
  exciseDutyGl: 'GL003',
  rollOverGl: 'GL004',
  penaltyGl: 'GL005',
  prepaymentGl: 'GL006',
  disbursementGl: 'GL007',
  upfrontInterestLiabilityGl: 'GL008',
  disbursementCreditAccountType: 'savings',
  repaymentGl: 'GL009',
  gracePeriodType: 'days',
  hasRecoveryTracking: true,
  externalProductCode: 'EXTPROD-001',
  disbursementCreditAccount: 'DISBA001',
  disbursementCreditAccountBranch: 'BR7001',
  isManaged: true,
  manualApprovalAmount: 2000,
  typeId: 'type-001',
  organizationId: 'org-001',
  loanCreationBranch: 'BR001',
}

export const customerLoanProfileStub: ICustomerLoanProfile = {
  branch: 'hq',
  cif: 'XOXOXO',
  pepPipRemarks: 'TEST',
  customerType: 'individual',
  dateOfBirth: '1990-01-01',
  email: '<EMAIL>',
  firstName: 'John',
  gender: 'male',
  idDocumentExpiryDate: '2020-01-01',
  idDocumentNumber: '*********',
  kraPinNumber: '*********',
  lastName: 'Doe',
  middleName: 'Kieran',
  mobile: '*********',
  nationality: 'RSA',
  pepPipDeclarationStatus: 'accepted',
  physicalAddress: {
    country: 'RSA',
    city: 'Johannesburg',
    street: 'blome fontain',
  },
  pipPepCategory: 'PEP',
  ssn: '*********',
  status: 'active',
  usCitizen: false,
}

export const customerDocumentsStub: ICustomerDocument = {
  id: '12345',
  type: 'type-001',
  file: 'file-001',
}
export const customerChecksStub: ICustomerCheck = {
  code: 'ID_DOCUMENT_FRONT_OCR',
  dateCreated: '2023-01-01',
  dateModified: '2023-06-01',
  id: '12345',
  message: 'test message',
  status: 'APPROVED',
}
export const repaymentHistoryStub: IRepaymentHistory = {
  amount: 36537827,
  currency: 'ZAR',
  transactionDate: '2023-01-01',
  transactionDesc: 'test description',
  transactionReference: 'test reference',
  transactionType: 'test type',
  valueDate: '2023-01-01',
}
export const loanRequestsSummaryStub: ILoanRequestsSummary = {
  pageNumber: 1,
  pageSize: 10,
  totalElements: 15,
  totalNumberOfPages: 2,
}

export const decodedTokenStub: IDecodeToken = {
  last_name: 'Doe',
  first_name: 'John',
  user_id: '12345',
  authorities: ['ADMIN'],
  sub: '12345',
  iat: 1,
  exp: 2,
  resources: [
    {
      resourceIds: ['12345', '67890'],
      resourceType: 'PRODUCTS',
    },
  ],
}
export const loanProductCreateStub: ILoanProductCreate = {
  name: 'Test Loan Product',
  organizationId: 'org123',
  country: 'CountryX',
  currency: 'USD',
  disbursementCreditAccount: '*********0',
  disbursementCreditAccountBranch: 'BranchX',
  code: 'LP001',
  externalProductCode: 'EXT001',
  typeId: 'type123',
  status: 'active',
  customerType: 'individual',
  measureOfTenure: 'months',
  interestRateType: 'fixed',
  facilityFeeRecoveryType: 'upfront',
  interestRecoveryType: 'monthly',
  prepaymentType: 'full',
  prepaymentCalculation: 'simple',
  interestCalculation: 'compound',
  amortizationMode: 'equal',
  trancheInterval: 'monthly',
  repaymentCycle: 'monthly',
  disbursementCreditAccountType: 'savings',
  gracePeriodType: 'none',
  minimumInterestRecoveryType: 'none',
  upfrontInterestRecognitionType: 'none',
  minimumInterestCalculationMode: 'none',
  externalProductName: 'External Loan Product',
  isManaged: true,
  earlyPaymentsAllowed: true,
  multipleDrawDown: false,
  tranches: false,
  hasRecoveryTracking: false,
  manualApprovalAmount: 1000,
  minimumAmount: 500,
  maximumAmount: 5000,
  minimumTenure: 6,
  maximumTenure: 24,
  dynamicInterestFixedComponent: 0.5,
  interestRate: 5.5,
  facilityFee: 1.5,
  exciseDuty: 0.2,
  rollOverFee: 0.1,
  rollOverPeriod: 12,
  maxRollOverCount: 3,
  prepaymentValue: 100,
  penalty: 50,
  periodInArrears: 30,
  minimumInterestValue: 10,
  interestGl: 'GL001',
  facilityFeeGl: 'GL002',
  exciseDutyGl: 'GL003',
  rollOverGl: 'GL004',
  penaltyGl: 'GL005',
  prepaymentGl: 'GL006',
  disbursementGl: 'GL007',
  repaymentGl: 'GL008',
  upfrontInterestLiabilityGl: 'GL009',
}

export const productCategoryStub: IProductCategoryType = {
  code: 'productCategory',
  createdBy: 'John Doe',
  dateCreated: '2015-12-2',
  dateModified: '2015-12-2',
  id: '12345',
  name: 'productCategory',
  parentCategory: {
    id: '12345',
    dateCreated: '2015-12-2',
    dateModified: '2015-12-2',
    createdBy: 'John Doe',
    modifiedBy: 'John Doe',
    code: 'productCategory',
    name: 'productCategory',
    parentCategory: '12345',
  },
}
export const organizationStub: IOrganization = {
  id: '12345',
  dateCreated: '2023-01-01',
  dateModified: '2023-01-01',
  createdBy: 'John Doe',
  modifiedBy: 'John Doe',
  name: 'Test Organization',
  status: 'active',
  cbsIdentifier: '*********0',
  email: '<EMAIL>',
  mobile: '*********0',
  bankName: 'Test Bank',
  bankCode: '*********0',
  swiftCode: '*********0',
  bankAccountNumber: '*********0',
  branchCode: '*********0',
  accountBranchName: 'Test Branch',
  physicalAddress: {
    country: 'Kenya',
    town: 'Nairobi',
    physicalAddress: 'Buruburu',
  },
  limit: 1000,
  limitCurrency: 'KSH',
}

export const organizationMakeStub = {
  cbsIdentifier: 'organization',
  comments: 'test',
}
