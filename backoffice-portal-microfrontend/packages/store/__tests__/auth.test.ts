import { it, vi, describe, expect, beforeEach, afterEach } from 'vitest'
import { secureapi } from '../src/utils/api'
import { handleLogin, handleLogout, refreshToken } from '@dtbx/store/actions'
import {
  clearNotification,
  setNotification,
  resetRolesStore,
  resetUsersStore,
  clearCustomerState,
  setDecodedToken,
} from '@dtbx/store/reducers'

import { openapi2 } from '../src/utils/api'
import { jwtDecode } from 'jwt-decode'
import { Dispatch } from 'redux'

vi.mock('jwt-decode')
vi.mock('@app/redux/actions/auth')

describe.skip('Auth', () => {
  it('should handle network failure gracefully', async () => {
    const dispatch = vi.fn()
    const router = { push: vi.fn() }

    const clearMock = vi.fn()
    Object.defineProperty(window, 'localStorage', {
      value: {
        clear: clearMock,
      },
      writable: true,
    })

    const consoleErrorSpy = vi
      .spyOn(console, 'error')
      .mockImplementation(() => {})

    secureapi.post = vi.fn().mockRejectedValue(new Error('Network Error'))

    await handleLogout(dispatch, router)

    expect(secureapi.post).toHaveBeenCalledWith('/backoffice-auth/users/logout')
    expect(dispatch).not.toHaveBeenCalledWith(resetUsersStore())
    expect(dispatch).not.toHaveBeenCalledWith(resetRolesStore())
    expect(dispatch).not.toHaveBeenCalledWith(clearNotification())
    expect(dispatch).not.toHaveBeenCalledWith(clearCustomerState())
    expect(clearMock).not.toHaveBeenCalled()
    expect(router.push).not.toHaveBeenCalled()
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'ERROR ON LOGOUT',
      expect.any(Error)
    )

    consoleErrorSpy.mockRestore()
  })

  it('should call dispatch actions in correct order and clear states on successful logout', async () => {
    const dispatch = vi.fn()
    const router = { push: vi.fn() }
    secureapi.post = vi.fn().mockResolvedValue({})

    await handleLogout(dispatch, router)

    expect(secureapi.post).toHaveBeenCalledWith('/backoffice-auth/users/logout')
    expect(dispatch).toHaveBeenCalledWith(resetUsersStore())
    expect(dispatch).toHaveBeenCalledWith(resetRolesStore())
    expect(dispatch).toHaveBeenCalledWith(clearNotification())
    expect(dispatch).toHaveBeenCalledWith(clearCustomerState())
    expect(window.localStorage.clear).toHaveBeenCalled()
    expect(router.push).toHaveBeenCalledWith('/')
  })

  describe('refreshToken', () => {
    it('should retrieve refresh token from localStorage when it exists', async () => {
      const mockRefreshToken = 'mockRefreshToken'

      const mockStorage: Record<string, string> = {
        refreshToken: mockRefreshToken,
        accessToken: '',
      }

      const config = {
        headers: { Authorization: `Bearer ${mockRefreshToken}` },
      }
      const getMock = vi.fn((key) => mockStorage[key] || null)
      const setMock = vi.fn((key, value) => {
        mockStorage[key] = value
      })

      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: getMock,
          setItem: setMock,
        },
        writable: true,
      })

      const postSpy = vi.spyOn(openapi2, 'post').mockResolvedValue({
        data: {
          access_token: 'newAccessToken',
          refresh_token: 'newRefreshToken',
        },
      })

      await refreshToken()

      expect(postSpy).toHaveBeenCalledWith(
        '/backoffice-auth/login/refresh-token',
        {},
        config
      )
      expect(getMock('accessToken')).toBe('newAccessToken')
      expect(getMock('refreshToken')).toBe('newRefreshToken')

      expect(setMock).toHaveBeenCalledWith('accessToken', 'newAccessToken')
      expect(setMock).toHaveBeenCalledWith('refreshToken', 'newRefreshToken')

      postSpy.mockRestore()
    })
    it('should handle missing refresh token in localStorage gracefully', async () => {
      const mockStorage: Record<string, string> = {
        accessToken: 'accessToken',
      }
      const removeMock = vi.fn((key) => delete mockStorage[key])
      const setMock = vi.fn((key, value) => {
        mockStorage[key] = value
      })
      const getMock = vi.fn((key) => mockStorage[key] || null)

      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: getMock,
          removeItem: removeMock,
          setItem: setMock,
        },
      })

      removeMock('refreshToken')

      const postSpy = vi.spyOn(openapi2, 'post').mockResolvedValue({
        data: {
          access_token: 'newAccessToken',
          refresh_token: 'newRefreshToken',
        },
      })

      await refreshToken()

      expect(postSpy).toHaveBeenCalledWith(
        '/backoffice-auth/login/refresh-token',
        {},
        { headers: { Authorization: 'Bearer ' } }
      )

      expect(setMock).toHaveBeenCalledWith('accessToken', 'newAccessToken')
      expect(setMock).toHaveBeenCalledWith('refreshToken', 'newRefreshToken')
    })
  })

  describe('handleLogin', () => {
    type TokenObject = {
      accessToken: string
      refreshToken: string
      success?: boolean
    }

    let tokenObject: TokenObject
    let dispatch: Dispatch
    let router: { push: vi.Mock }

    beforeEach(() => {
      tokenObject = {
        accessToken: 'validAccessToken',
        refreshToken: 'validRefreshToken',
      }
      dispatch = vi.fn()
      router = { push: vi.fn() }
    })

    afterEach(() => {
      vi.clearAllMocks()
    })

    // Successfully retrieves access token using openapi2
    it('should store access and refresh tokens and navigate to landing page when access token is valid', async () => {
      const tokenObject = {
        accessToken: 'validAccessToken',
        refreshToken: 'validRefreshToken',
        success: true,
      }
      const dispatch = vi.fn()
      const router = { push: vi.fn() }
      const accessTokenResponse = { data: { access_token: 'newAccessToken' } }
      const mockStorage: Record<string, string> = {
        accessToken: 'accessToken',
        refreshToken: 'newAccessToken',
      }
      const setMock = vi.fn((key, value) => {
        mockStorage[key] = value
      })

      Object.defineProperty(window, 'localStorage', {
        value: {
          // getItem: getMock,
          setItem: setMock,
        },
      })

      vi.spyOn(openapi2, 'get').mockResolvedValue(accessTokenResponse)
      vi.spyOn(localStorage, 'setItem')

      await handleLogin(tokenObject, dispatch, router)

      expect(openapi2.get).toHaveBeenCalledWith(
        '/backoffice-auth/login/token?tokenKey=validAccessToken'
      )
      expect(setMock).toHaveBeenCalledWith('accessToken', 'newAccessToken')
      expect(localStorage.setItem).toHaveBeenCalledWith(
        'refreshToken',
        'validRefreshToken'
      )
      expect(dispatch).toHaveBeenCalledWith(
        setDecodedToken(jwtDecode('newAccessToken'))
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: 'Login success', type: 'success' })
      )
      expect(router.push).toHaveBeenCalledWith('/landing')
    })

    it('should call openapi2.get with correct URL', async () => {
      const tokenObject = {
        accessToken: 'validAccessToken',
        refreshToken: 'validRefreshToken',
      }
      const router = { push: vi.fn() }
      const dispatch = vi.fn()
      const mockGetAxiosSuccessHandler = vi
        .spyOn(openapi2, 'get')
        .mockResolvedValue({
          data: { access_token: 'newAccessToken' },
        })

      await handleLogin(tokenObject, dispatch, router)

      expect(mockGetAxiosSuccessHandler).toHaveBeenCalledWith(
        '/backoffice-auth/login/token?tokenKey=validAccessToken'
      )
    })

    // Handles invalid or expired access token gracefully
    it('should log error when access token is invalid or expired', async () => {
      const tokenObject = {
        accessToken: 'invalidAccessToken',
        refreshToken: 'validRefreshToken',
        success: false,
      }
      const dispatch = vi.fn()
      const router = { push: vi.fn() }

      vi.spyOn(openapi2, 'get').mockRejectedValue(new Error('Invalid token'))
      console.error = vi.fn()

      await handleLogin(tokenObject, dispatch, router)

      expect(openapi2.get).toHaveBeenCalledWith(
        '/backoffice-auth/login/token?tokenKey=invalidAccessToken'
      )
      expect(console.error).toHaveBeenCalledWith(
        'ERROR ON LOGIN',
        expect.any(Error)
      )
    })
    it('should store access and refresh tokens in localStorage', async () => {
      const dispatch = vi.fn()
      const router = {
        push: vi.fn(),
      }
      const tokenObject = {
        accessToken: 'accessToken',
        refreshToken: 'refreshToken',
        success: true,
      }
      const mockGetAxiosSuccessHandler = vi
        .spyOn(openapi2, 'get')
        .mockResolvedValue({
          data: { access_token: 'newAccessToken' },
        })
      const mockStorage: Record<string, string> = {
        accessToken: 'accessToken',
        refreshToken: 'newAccessToken',
      }
      const setMock = vi.fn((key, value) => {
        mockStorage[key] = value
      })
      const getMock = vi.fn((key) => mockStorage[key] || null)

      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: getMock,
          setItem: setMock,
        },
      })

      await handleLogin(tokenObject, dispatch, router)
      setMock('accessToken', 'newAccessToken')
      setMock('refreshToken', 'validRefreshToken')

      expect(mockGetAxiosSuccessHandler).toHaveBeenCalledTimes(1)

      expect(setMock).toHaveBeenCalledWith('accessToken', 'newAccessToken')
      expect(setMock).toHaveBeenCalledWith('refreshToken', 'validRefreshToken')
    })
  })
})
