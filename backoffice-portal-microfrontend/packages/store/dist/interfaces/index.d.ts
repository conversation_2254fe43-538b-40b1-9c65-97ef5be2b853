export { C as ClientType, r as CustomerErrorResponse, F as FilterType, O as IAcceptRejectRestrictAccountApprovals, a4 as IAccountActivation, a3 as IAccountLinkingCompletion, P as IActivateCustomerProfile, L as IApproveCustomerPinReset, Q as IApproveRejectCustomerProfileActivation, D as IApproveRejectCustomerUpdate, l as IChannelModule, a0 as ICreateCustomerAccount, a1 as ICreateCustomerApprovals, a2 as ICreateCustomerDeactivate, t as ICustomer, z as ICustomerAccount, G as ICustomerAccountDetails, E as ICustomerAccountEventHistory, H as ICustomerAccountHistoryLogs, u as ICustomerAccountLink, v as ICustomerCreate, M as ICustomerPinDetails, R as ICustomerPinLog, T as ICustomerPinLogResponse, K as ICustomerPinReset, A as ICustomerProfileAccount, s as ICustomersDataResponse, m as ICustomersFilter, Z as IDeactivateCustomer, _ as IDeactivateCustomerApprovals, X as IDeactivateCustomerDeviceParams, j as IDecodeToken, y as IDevice, x as IDevicesResponse, $ as IFetchCustomerAccount, c as IFilter, b as IFilterOption, W as IGetCustomerDeviceDetail, U as IGetCustomerDevicesParams, V as IGetCustomerDevicesProps, q as IGetCustomerResponse, p as IGetCustomersRespons, I as IHeadCell, d as ILandingApps, J as ILogs, a5 as INotificationEventSettings, ab as INotificationEventSubscriberPayload, a6 as INotificationEventSubscribers, a7 as INotificationEventTemplates, ac as INotificationEventType, a8 as INotificationEvents, aa as INotificationEventsPayload, ad as INotificationEventsPerAccount, a9 as INotificationFrequencies, n as IPendingCustomersFilter, g as IPermission, h as IPermissionGroup, ae as IPlatform, af as IPlatformResponse, ag as IPlatformSummary, Y as IRejectCustomerDeviceParams, k as IResource, N as IRestrictAccountParams, f as IRole, o as ISetCustomerSearch, a as ISidebarConfigItem, e as ITableData, B as IUpdateCustomerDetails, i as IUser, w as IprofileAccountStoreIds, S as StoreOfValue } from '../platform-BWaO4Lr3.js';
import 'react';
import 'redux';

interface INotification {
    id: string;
    userId: string;
    initiatorFullName: string;
    title: string;
    content: string;
    message: string;
    isRead: boolean;
    dateCreated: Date;
    updatedAt: string;
}
interface IApiError {
    status: string;
    message: string;
}
declare const initialApiError: Readonly<IApiError>;

/**
 * <AUTHOR> Kinyoro on 03/12/2024
 */
interface PaginatedResponse<T> {
    totalElements: number;
    size: number;
    page: number;
    totalNumberOfPages: number;
    data: T[];
}

/**
 * <AUTHOR> Kinyoro on 12/12/2024
 */
interface TokenResponse {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
}

/**
 * <AUTHOR> Kinyoro on 19/12/2024
 */
interface ApiResponse<T> {
    status: string;
    message: string;
    data: T;
    errors: string[];
}

type Changes = {
    actionee: actionee;
    action: Action;
    previousState?: null | string;
    actionSubject: ActionSubject;
    date: DateEnum;
    time: Time;
    resource?: null | string;
    comment?: null | string;
    type: 'creation' | 'approval' | 'edited' | 'deletion';
};
type Action = 'create' | 'edit' | 'delete';
type ActionSubject = 'Patsheba Gikunda';
type actionee = 'John Doe' | 'Jane Smith';
type DateEnum = 'February 25, 2023 ' | 'February 26, 2023 ';
type Time = '10:00 PM' | '10:00 AM' | '11:00 AM';
declare const changes: Changes[];

export { type Action, type ActionSubject, type ApiResponse, type Changes, type DateEnum, type IApiError, type INotification, type PaginatedResponse, type Time, type TokenResponse, type actionee, changes, initialApiError };
