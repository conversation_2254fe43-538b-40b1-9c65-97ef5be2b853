import { Dispatch } from '@reduxjs/toolkit';

declare const refreshToken: () => Promise<void>;
type TokenObject = {
    accessToken: string;
    refreshToken: string;
    success: boolean;
};
declare const handleLogin: (tokenObject: TokenObject, dispatch: Dispatch) => Promise<string>;
declare const fetchChannelModules: (dispatch: Dispatch) => Promise<void>;

declare const getUserPlatform: (dispatch: Dispatch, params?: string) => Promise<any>;

export { fetchChannelModules, getUserPlatform, handleLogin, refreshToken };
