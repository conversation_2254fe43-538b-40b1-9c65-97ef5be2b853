import {
  setChannelModules,
  setDecodedToken,
  setIsLoginError,
  setLoginErrorMessage,
  setNotification
} from "./chunk-AWTHLBRD.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-ZTCPGNST.js";

// src/utils/AccessControlHelper.tsx
import { jwtDecode } from "jwt-decode";
import {
  Children,
  cloneElement,
  isValidElement
} from "react";
import { Fragment, jsx } from "react/jsx-runtime";
var AccessControlHandler = (rights, makerId, isMake) => {
  const accessToken = localStorage.getItem("accessToken");
  if (accessToken) {
    const token = jwtDecode(accessToken);
    const userRoles = token.authorities;
    const hasRights = rights.some((right) => userRoles.includes(right));
    if (makerId) {
      const isMaker = makerId === `${token.first_name} ${token.last_name}`;
      return isMake ? hasRights && isMaker : hasRights && !isMaker;
    } else {
      return hasRights;
    }
  }
  return false;
};
var HasAccessToRights = (rights) => {
  const accessToken = localStorage.getItem("accessToken");
  if (accessToken) {
    const token = jwtDecode(accessToken);
    const userRoles = token.authorities;
    return rights.some((right) => userRoles.includes(right));
  }
  return false;
};
var checkIsMaker = (makerId) => {
  if (!makerId) {
    return false;
  }
  const accessToken = localStorage.getItem("accessToken");
  if (accessToken) {
    const token = jwtDecode(accessToken);
    return makerId === `${token.first_name} ${token.last_name}`;
  }
  return false;
};
var isButtonComponent = (element) => {
  return element.props.variant || element.props.onClick || element.props.startIcon || element.props.endIcon || element.props.onChange;
};
var AccessControlWrapper = ({
  rights,
  children,
  makerId,
  isMake = false
}) => {
  const hasAccess = AccessControlHandler(rights, makerId, isMake);
  const processChildren = (children2) => {
    return Children.map(children2, (child) => {
      if (isValidElement(child) && isButtonComponent(child)) {
        const element = child;
        return !hasAccess ? cloneElement(element, { disabled: true }) : child;
      }
      return child;
    });
  };
  return /* @__PURE__ */ jsx(Fragment, { children: processChildren(children) });
};

// src/utils/authHelper.ts
import { jwtDecode as jwtDecode2 } from "jwt-decode";
var isLoggedIn = () => {
  if (typeof window !== "undefined") {
    const token = localStorage.getItem("accessToken") || "";
    try {
      if (typeof token === "undefined" || token === null || token === "") {
        return false;
      } else {
        const decodedToken = jwtDecode2(token);
        if (decodedToken.exp * 1e3 > (/* @__PURE__ */ new Date()).getTime()) {
          return true;
        }
      }
    } catch (error) {
      localStorage.clear();
      console.error("Error decoding token: ", error);
    }
  }
  return false;
};
var clearStore = () => {
  if (typeof window !== "undefined") {
    const isFirstVisit = !localStorage.getItem("appInitialized");
    if (isFirstVisit) {
      window.localStorage.clear();
      window.sessionStorage.clear();
      localStorage.setItem("appInitialized", "true");
      window.location.reload();
    }
  }
};

// src/utils/helpers.ts
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
dayjs.extend(customParseFormat);
var getInitials = (name = "") => name.replace(/\s+/, " ").split(" ").slice(0, 2).map((v) => v && v[0].toUpperCase()).join("");
var rightsFormatter = (str) => {
  str = str.replace(/([A-Z]+)/g, "_$1").toLowerCase();
  str = str.startsWith("_") ? str.slice(1) : str;
  return str;
};
function getTimeOfDay() {
  const date = /* @__PURE__ */ new Date();
  const currentHour = date.getHours();
  let timeOfDay;
  if (currentHour < 12) {
    timeOfDay = "morning";
  } else if (currentHour < 18) {
    timeOfDay = "afternoon";
  } else {
    timeOfDay = "evening";
  }
  const hours = date.getHours() % 12 || 12;
  const minutes = date.getMinutes();
  const amPm = date.getHours() >= 12 ? "PM" : "AM";
  const formattedTime = `${hours}:${minutes < 10 ? "0" : ""}${minutes} ${amPm}`;
  return { timeOfDay, formattedTime };
}
function formatCurrency(value, currency = "KES", locale = "en-KE") {
  const numberValue = typeof value === "string" ? parseFloat(value) : value;
  if (!numberValue && numberValue !== 0) return `${currency} 0.00`;
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency
  }).format(numberValue);
}
function formatTimestamp(timestamp) {
  if (!timestamp) {
    return "N/A";
  }
  const date = new Date(timestamp);
  if (isNaN(date.getTime())) {
    return "N/A";
  }
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}
var formatTimeOnly = (dateString) => {
  if (!dateString) return "";
  try {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true
    });
  } catch (e) {
    return "";
  }
};
var formatDate = (dateString) => {
  const date = new Date(dateString);
  const options = {
    month: "long",
    day: "numeric",
    year: "numeric"
  };
  return date.toLocaleDateString("en-US", options);
};
function getWeekOfYear(date = /* @__PURE__ */ new Date()) {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / (1e3 * 60 * 60 * 24) + 1;
  const firstDayOfWeek = firstDayOfYear.getDay() === 0 ? 7 : firstDayOfYear.getDay();
  return Math.ceil((pastDaysOfYear + firstDayOfWeek - 1) / 7);
}
function getAuctionWeek() {
  return getWeekOfYear() - 1;
}
var formatCustomDate = (date, inputFormat = "YYYY-MM-DD", outputFormat = "MMMM D, YYYY") => {
  return dayjs(date, inputFormat).format(outputFormat);
};
var formatDateTime = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString("en-KE", { hour12: true });
};
var isObjEmpty = (obj) => {
  return Object.keys(obj).length === 0;
};
var trimSpace = (obj) => {
  const trimmedObj = {};
  for (const key in obj) {
    if (typeof obj[key] === "string") {
      trimmedObj[key] = obj[key].trim();
    } else if (typeof obj[key] === "object" && obj[key] !== null) {
      if (Array.isArray(obj[key])) {
        trimmedObj[key] = obj[key].map(trimSpace);
      } else {
        trimmedObj[key] = trimSpace(obj[key]);
      }
    } else {
      trimmedObj[key] = obj[key];
    }
  }
  return trimmedObj;
};
var generateMarks = (step, start, end) => {
  const marks = [];
  for (let value = start; value <= end; value += step) {
    marks.push({
      value,
      label: `${value}`
    });
  }
  return marks;
};
var downloadBlob = (blob, filename) => {
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};
var handleDiff = (diff = []) => {
  return diff.map((change, index) => {
    if (Array.isArray(change.newValue)) {
      let newValueString = "";
      let oldValueString = change.oldValue || "null";
      if (change.field === "permissions") {
        newValueString = change.newValue.map((val) => `${val.name}`).join(", ");
        if (Array.isArray(change.oldValue)) {
          oldValueString = change.oldValue.map((val) => `${val.name}`).join(", ");
        } else {
          oldValueString = change.oldValue || "null";
        }
      } else {
        newValueString = change.newValue.map((val) => `${val.field}: ${val.newValue}`).join(", ");
      }
      return `${index + 1}. ${change.field} was changed from ${oldValueString} to ${newValueString}`;
    } else {
      return `${index + 1}. ${change.field} was changed from ${change.oldValue || "null"} to ${change.newValue || "null"}`;
    }
  }).join("\n");
};
var extractFields = (field, row) => {
  const result = row.diff.find((val) => val.field === field);
  return result == null ? void 0 : result.newValue;
};
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
var isAccountLinkingApprovalRequest = (entity) => {
  const accountLinkingEntities = ["accounts"];
  const parsedEntity = JSON.parse(entity);
  return Object.keys(parsedEntity).some(
    (key) => accountLinkingEntities.includes(key)
  );
};
var matchActivePath = (path, pathname, searchParams) => {
  const url = `${pathname}?${searchParams}`;
  const matchPath = (options, pathname2) => {
    const { path: path2, end } = options;
    const regex = new RegExp(`^${path2}${end ? "$" : ""}`, "i");
    return regex.test(pathname2);
  };
  return path && searchParams.toString().length > 0 ? path === url : path && searchParams.toString().length === 0 ? matchPath({ path, end: false }, pathname) : false;
};
var formatText = (text) => {
  return text.replace(/_/g, " ").replace(/([a-z])([A-Z])/g, "$1 $2").toUpperCase();
};
var addUnderscores = (text) => {
  return text.trim().replace(/\s+/g, "_");
};
var formatCamelCaseToWords = (text) => {
  return text.replace(/([a-z])([A-Z])/g, "$1 $2").replace(/^./, (str) => str.toUpperCase());
};

// src/utils/const/index.ts
var ACCESS_CONTROLS = {
  //customers
  REJECT_APPROVALREQUEST_CUSTOMERS: [
    "REJECT_UPDATE_CUSTOMERS",
    "REJECT_ACTIVATE_CUSTOMERS",
    "REJECT_DEACTIVATE_CUSTOMERS",
    "REJECT_CREATE_CUSTOMERS"
  ],
  ACCEPT_APPROVALREQUEST_CUSTOMERS: [
    "ACCEPT_UPDATE_CUSTOMERS",
    "ACCEPT_CREATE_CUSTOMERS",
    "ACCEPT_DEACTIVATE_CUSTOMER",
    "ACCEPT_ACTIVATE_CUSTOMERS"
  ],
  CREATE_CUSTOMERS: ["SUPER_CREATE_CUSTOMERS", "MAKE_CREATE_CUSTOMERS"],
  UPDATE_CUSTOMERS: ["SUPER_UPDATE_CUSTOMERS", "MAKE_UPDATE_CUSTOMERS"],
  DEACTIVATE_CUSTOMERS: [
    "MAKE_DEACTIVATE_CUSTOMERS",
    "SUPER_DEACTIVATE_CUSTOMERS"
  ],
  ACTIVATE_CUSTOMERS: ["MAKE_ACTIVATE_CUSTOMERS", "SUPER_ACTIVATE_CUSTOMERS"],
  DELETE_CUSTOMERS: ["SUPER_DELETE_CUSTOMERS", "MAKE_DELETE_CUSTOMERS"],
  //profile devices
  CREATE_DEVICE: ["SUPER_CREATE_DEVICE", "MAKE_CREATE_DEVICE"],
  ACTIVATE_DEVICE: ["SUPER_ACTIVATE_DEVICE", "MAKE_ACTIVATE_DEVICE"],
  DEACTIVATE_DEVICE: ["SUPER_DEACTIVATE_DEVICE", "MAKE_DEACTIVATE_DEVICE"],
  ACCEPT_APPROVAL_REQUEST_DEVICES: [
    "ACCEPT_DEACTIVATED_DEVICE",
    "ACCEPT_ACTIVATED_DEVICE"
  ],
  REJECT_APPROVAL_REQUEST_DEVICES: [
    "REJECT_ACTIVATE_DEVICE",
    "REJECT_DEACTIVATE_DEVICE"
  ],
  RESET_SECURITY_QUESTIONS: [
    "SUPER_RESET_SECURITY_QUESTIONS",
    "MAKE_RESET_SECURITY_QUESTIONS"
  ],
  ACCEPT_RESET_SECURITY_QUESTIONS: ["ACCEPT_RESET_SECURITY_QUESTIONS"],
  REJECT_RESET_SECURITY_QUESTIONS: ["REJECT_RESET_SECURITY_QUESTIONS"],
  //user-management
  CREATE_USERS: ["SUPER_CREATE_USERS", "MAKE_CREATE_USERS"],
  UPDATE_USERS: ["SUPER_UPDATE_USERS", "MAKE_UPDATE_USERS"],
  DEACTIVATE_ACTIVATE_USERS: [
    "MAKE_DEACTIVATE_USERS",
    "MAKE_ACTIVATE_USERS",
    "SUPER_DEACTIVATE_USERS",
    "SUPER_ACTIVATE_USERS"
  ],
  ACCEPT_APPROVALREQUEST_USERS: [
    "ACCEPT_DELETE_USERS",
    "ACCEPT_ACTIVATE_USERS",
    "ACCEPT_CREATE_USERS",
    "ACCEPT_UPDATE_USERS",
    "ACCEPT_DEACTIVATE_USERS"
  ],
  REJECT_APPROVALREQUEST_USERS: [
    "REJECT_CREATE_USERS",
    "REJECT_UPDATE_USERS",
    "REJECT_DEACTIVATE_USERS",
    "REJECT_ACTIVATE_USERS",
    "REJECT_DELETE_USERS"
  ],
  //roles
  CREATE_ROLES: ["MAKE_CREATE_GROUPS", "SUPER_CREATE_GROUPS"],
  UPDATE_ROLES: ["MAKE_UPDATE_GROUPS", "SUPER_UPDATE_GROUPS"],
  DELETE_ROLE: ["MAKE_DELETE_GROUPS", "SUPER_DELETE_GROUPS"],
  REJECT_APPROVALREQUEST_ROLES: [
    "REJECT_ACTIVATE_GROUPS",
    "REJECT_UPDATE_GROUPS",
    "REJECT_DEACTIVATE_GROUPS",
    "REJECT_DELETE_GROUPS"
  ],
  ACCEPT_APPROVALREQUEST_ROLES: [
    "ACCEPT_ACTIVATE_GROUPS",
    "ACCEPT_UPDATE_GROUPS",
    "ACCEPT_DEACTIVATE_GROUPS",
    "ACCEPT_DELETE_GROUPS"
  ],
  //notifications
  CREATE_NOTIFICATIONS: [
    "MAKE_CREATE_NOTIFICATIONS",
    "SUPER_CREATE_NOTIFICATIONS"
  ],
  UPDATE_NOTIFICATIONS: [
    "MAKE_UPDATE_NOTIFICATIONS",
    "SUPER_UPDATE_NOTIFICATIONS"
  ],
  DELETE_NOTIFICATIONS: [
    "MAKE_DELETE_NOTIFICATIONS",
    "SUPER_DELETE_NOTIFICATIONS"
  ],
  ACCEPT_NOTIFICATIONS: [
    "ACCEPT_CREATE_NOTIFICATIONS",
    "ACCEPT_DELETE_NOTIFICATIONS",
    "ACCEPT_ACTIVATE_NOTIFICATIONS",
    "ACCEPT_DEACTIVATE_NOTIFICATIONS",
    "ACCEPT_UPDATE_NOTIFICATIONS"
  ],
  REJECT_NOTIFICATIONS: [
    "REJECT_UPDATE_NOTIFICATIONS",
    "REJECT_DELETE_NOTIFICATIONS",
    "REJECT_ACTIVATE_NOTIFICATIONS",
    "REJECT_CREATE_NOTIFICATIONS"
  ],
  UPDATE_ACCOUNT_PREFERENCES: [
    "SUPER_UPDATE_ACCOUNTS_PREFERENCES",
    "MAKE_UPDATE_ACCOUNTS_PREFERENCES"
  ],
  REJECT_ACCOUNT_PREFERENCES: ["REJECT_UPDATE_ACCOUNTS_PREFERENCES"],
  ACCEPT_ACCOUNT_PREFERENCES: ["ACCEPT_UPDATE_ACCOUNTS_PREFERENCES"],
  //Profiles
  ACCEPT_APPROVALREQUEST_PROFILES: ["ACCEPT_RESET_SECURITY_QUESTIONS"],
  REJECT_APPROVALREQUEST_PROFILES: ["REJECT_RESET_SECURITY_QUESTIONS"],
  //Cards module
  ACTIVATE_CARDS: ["SUPER_ACTIVATE_CARDS", "MAKE_ACTIVATE_CARDS"],
  REJECT_APPROVALREQUEST_CARDS: ["REJECT_ACTIVATE_CARDS"],
  ACCEPT_APPROVALREQUEST_CARDS: ["ACCEPT_ACTIVATE_CARDS"],
  RESET_PIN_TRY_COUNTER: [
    "MAKE_CARDS_RESET_PIN_TRY_COUNTER",
    "SUPER_CARDS_RESET_PIN_TRY_COUNTER"
  ],
  // Tariffs and Charge configurations
  CREATE_TARIFF: ["MAKE_CREATE_TARIFF", "SUPER_CREATE_TARIFFS"],
  UPDATE_SERVICE_CONFIGS: [
    "MAKE_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS",
    "SUPER_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS"
  ],
  CREATE_SERVICE_CONFIGS: [
    "SUPER_CREATE_PAYMENT_SERVICE_CONFIGURATIONS",
    "MAKE_CREATE_PAYMENT_SERVICE_CONFIGURATIONS"
  ],
  REJECT_TARIFFS: [
    "REJECT_CREATE_TARIFFS",
    "REJECT_DEACTIVATE_TARIFFS",
    "REJECT_CREATE_PAYMENT_SERVICE_CONFIGURATIONS",
    "REJECT_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS",
    "REJECT_DEACTIVATE_PAYMENT_SERVICE_CONFIGURATIONS"
  ],
  ACCEPT_TARIFFS: [
    "ACCEPT_CREATE_TARIFFS",
    "ACCEPT_DEACTIVATE_TARIFFS",
    "ACCEPT_CREATE_PAYMENT_SERVICE_CONFIGURATIONS",
    "ACCEPT_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS",
    "ACCEPT_DEACTIVATE_PAYMENT_SERVICE_CONFIGURATIONS"
  ]
};
var restrictReasons = [
  "Fraudulent Activity",
  "Suspicious Behavior",
  "Violation of terms",
  "Legal Requirement",
  "Other"
];
var reasonsForUnlinking = [
  "User Request",
  "Security Concerns",
  "Account Inactivity",
  "Other"
];
var reasonsForActivation = [
  "Customer Request",
  "Promotion Campaign",
  "False Alarm",
  "Other"
];
var reasonsForDeleting = [
  "User Request",
  "Account Compromised",
  "Duplicate Account",
  "Violation of Terms of Service",
  "Other"
];
var reasonsForDeactivating = [
  "User Request",
  "Temporary Suspension",
  "Fraudulent Activity",
  "Security Concerns",
  "Other"
];
var reasonsForUnsubscribing = [
  "Too many notifications",
  "Irrelevant content",
  "Temporary Break",
  "Not interested in updates anymore",
  "Other"
];
var alertTypes = [
  { label: "Credit", type: "ACCOUNT_CREDIT", frequency: "" },
  { label: "Debit", type: "ACCOUNT_DEBIT", frequency: "" },
  {
    label: "Daily balance",
    type: "BALANCE_ALERT",
    frequency: "Daily Frequency"
  },
  {
    label: "Weekly balance",
    type: "BALANCE_ALERT",
    frequency: "Weekly Frequency"
  },
  {
    label: "Monthly balance",
    type: "BALANCE_ALERT",
    frequency: "Monthly Frequency"
  },
  { label: "Overdrawn Account", type: "OVERDRAFT_ALERT", frequency: "" }
];

// src/utils/api/authHeader.ts
var authConfig = {
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  headers: {
    "Content-type": "application/json",
    Accept: "application/json"
  }
};
var authConfig2 = {
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL_2,
  headers: {
    "Content-type": "application/json",
    Accept: "application/json"
  }
};

// src/utils/api/openApi.ts
import axios from "axios";
var openAuth = __spreadProps(__spreadValues({}, authConfig), {
  baseURL: process.env.NEXT_PUBLIC_OPEN_API_BASE_URL
});
var openAuth2 = __spreadProps(__spreadValues({}, authConfig), {
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL
});
var instance = axios.create(openAuth);
var instance2 = axios.create(openAuth2);
var APIError = class _APIError extends Error {
  constructor(status, message, description) {
    super(message);
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, _APIError);
    }
    this.statusCode = status;
    this.description = description;
    this.message = message;
  }
};
var errorHandler = (err) => {
  var _a;
  let message = "Unknown error occurred";
  try {
    if (err.response) {
      const { status, data } = err.response;
      if (status === 400) {
        message = err.response.data.error ? err.response.data.error : err.response.data.errors ? err.response.data.errors[0] : err.response.data.message || "Bad Request";
      }
      if (status === 429) {
        message = err.response.data.error ? err.response.data.error : err.response.data.errors ? err.response.data.errors[0] : err.response.data.message || "Too Many Requests";
      }
      throw new APIError(status, message, (_a = data.description) != null ? _a : "");
    } else if (err.request) {
      throw new APIError(503, "No response from server", "");
    } else {
      throw new APIError(400, message, "");
    }
  } catch (e) {
    return e instanceof Error ? e : new Error(message);
  }
};
instance.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject(errorHandler(error))
);
instance2.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject(errorHandler(error))
);
var openapi = instance;
var openapi2 = instance2;

// src/utils/api/secureApi.ts
import axios2 from "axios";
import { jwtDecode as jwtDecode4 } from "jwt-decode";

// src/actions/auth.ts
import { jwtDecode as jwtDecode3 } from "jwt-decode";
var refreshToken = () => __async(void 0, null, function* () {
  try {
    const refreshToken2 = localStorage.getItem("refreshToken") ? localStorage.getItem("refreshToken") : "";
    const config = { headers: { Authorization: `Bearer ${refreshToken2}` } };
    const resp = yield openapi2.post(
      "/backoffice-auth/login/refresh-token",
      {},
      config
    );
    localStorage.setItem("accessToken", resp.data.access_token);
    localStorage.setItem("refreshToken", resp.data.refresh_token);
  } catch (e) {
    console.error("ERROR ON REFRESH TOKEN", e);
  }
});
var handleLogin = (tokenObject, dispatch, router) => __async(void 0, null, function* () {
  try {
    const tokenSecret = sessionStorage.getItem("tokenSecret");
    const accessTokenResponse = yield openapi2.post(
      `/backoffice-auth/login/token`,
      { key: tokenObject.accessToken, secret: tokenSecret }
    );
    localStorage.setItem("accessToken", accessTokenResponse.data.access_token);
    localStorage.setItem("refreshToken", tokenObject.refreshToken);
    const decodedToken = jwtDecode3(
      accessTokenResponse.data.access_token
    );
    dispatch(setDecodedToken(decodedToken));
    dispatch(
      setNotification({
        message: "Login success",
        type: "success"
      })
    );
    return router.push("/landing");
  } catch (e) {
    console.error("ERROR ON LOGIN", e);
    const message = e.message;
    dispatch(setLoginErrorMessage(message));
    dispatch(setIsLoginError(true));
    return router.push("/");
  }
});
var fetchChannelModules = (dispatch) => __async(void 0, null, function* () {
  var _a;
  try {
    const modules = yield secureapi.get(
      `/backoffice-auth/users/user/channel-modules`
    );
    const moduleList = (modules == null ? void 0 : modules.data) && ((_a = modules == null ? void 0 : modules.data) == null ? void 0 : _a.length) > 0 ? modules == null ? void 0 : modules.data : [];
    dispatch(setChannelModules(moduleList));
  } catch (e) {
    console.error(e);
  }
});

// src/utils/api/secureApi.ts
var instance1 = axios2.create(authConfig);
var instance22 = axios2.create(authConfig2);
var APIError2 = class _APIError extends Error {
  constructor(statusCode, message) {
    super(message);
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, _APIError);
    }
    this.statusCode = statusCode;
  }
};
var errorHandler2 = (err) => {
  var _a;
  let message = "Something went wrong, try again later";
  let statusCode = 400;
  if (err.response) {
    statusCode = err.response.status;
    message = err.response.data.message || message;
    const responseData = err.response.data;
    if (statusCode === 401) {
      if (responseData.message === "The received token is expired") {
        message = "You are not allowed to perform this request.";
      } else if (responseData.code === "no_token") {
        message = "Your session is no longer valid due to login on another device or browser. Please log in again.";
        setTimeout(() => {
          window.history.pushState({}, "", "/");
          localStorage.clear();
          sessionStorage.clear();
        }, 1e3);
      } else {
        message = "You are not allowed to perform this request";
      }
    }
    if (statusCode === 403) {
      message = err.response.data.error ? err.response.data.error : err.response.data.errors ? err.response.data.errors[0] : err.response.data.message || "Access Denied!!";
    }
    if (statusCode === 404) {
      message = ((_a = err.response.data.errors) == null ? void 0 : _a[0]) || err.response.data.message || "Resource not found";
    }
    if (statusCode === 400) {
      message = err.response.data.error ? err.response.data.error : err.response.data.errors ? err.response.data.errors[0] : err.response.data.message || message;
    }
    if (statusCode === 409) {
      message = err.response.data.error ? err.response.data.error : err.response.data.errors ? err.response.data.errors[0] : err.response.data.message || message;
    }
    if (statusCode === 500) {
      message = err.response.data.errors ? err.response.data.errors[0] : err.response.data.message ? err.response.data.message : message;
    }
  } else if (err.request) {
    statusCode = 503;
    message = "No response from server";
  }
  return new APIError2(statusCode, message);
};
function refreshTokenIfNeeded(refreshToken2) {
  return __async(this, null, function* () {
    let token = localStorage.getItem("accessToken") ? localStorage.getItem("accessToken") : "";
    if (!token || jwtDecode4(token).exp * 1e3 < (/* @__PURE__ */ new Date()).getTime()) {
      yield refreshToken2();
      token = localStorage.getItem("accessToken") || "";
    }
    return token;
  });
}
var tokenInterceptor = (config) => __async(void 0, null, function* () {
  const token = yield refreshTokenIfNeeded(refreshToken);
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
instance1.interceptors.request.use(tokenInterceptor);
instance1.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject(errorHandler2(error))
);
instance22.interceptors.request.use(tokenInterceptor);
instance22.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject(errorHandler2(error))
);
var secureapi = instance1;
var secureapi2 = instance22;

export {
  AccessControlHandler,
  HasAccessToRights,
  checkIsMaker,
  AccessControlWrapper,
  isLoggedIn,
  clearStore,
  getInitials,
  rightsFormatter,
  getTimeOfDay,
  formatCurrency,
  formatTimestamp,
  formatTimeOnly,
  formatDate,
  getWeekOfYear,
  getAuctionWeek,
  formatCustomDate,
  formatDateTime,
  isObjEmpty,
  trimSpace,
  generateMarks,
  downloadBlob,
  handleDiff,
  extractFields,
  getBase64,
  isAccountLinkingApprovalRequest,
  matchActivePath,
  formatText,
  addUnderscores,
  formatCamelCaseToWords,
  ACCESS_CONTROLS,
  restrictReasons,
  reasonsForUnlinking,
  reasonsForActivation,
  reasonsForDeleting,
  reasonsForDeactivating,
  reasonsForUnsubscribing,
  alertTypes,
  authConfig,
  authConfig2,
  openapi,
  openapi2,
  refreshTokenIfNeeded,
  secureapi,
  secureapi2,
  refreshToken,
  handleLogin,
  fetchChannelModules
};
