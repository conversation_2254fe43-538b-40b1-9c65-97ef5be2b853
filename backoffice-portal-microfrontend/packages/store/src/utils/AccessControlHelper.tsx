import { jwtDecode } from 'jwt-decode'
import React, {
  Children,
  cloneElement,
  isValidElement,
  ReactElement,
  ReactNode,
} from 'react'

import { IDecodeToken } from '../interfaces'

export interface IAccessControlWrapperProps {
  rights: string[]
  modules?: string[]
  children: ReactNode
  makerId?: string
  isMake?: boolean
}
/**
 * This function gets the access token from localstorage and decodes it to get the roles assigned to the user and the modules they have access to
 * The function accepts an array of roles and an array of modules and returns a boolean for whether the user has access to the module or not
 */
export const AccessControlHandler = (
  rights: string[],
  makerId?: string,
  isMake?: boolean
) => {
  if (typeof window !== 'undefined') {
    const accessToken = localStorage.getItem('accessToken')
    if (accessToken) {
      const token: IDecodeToken = jwtDecode(accessToken)
      const userRoles = token.authorities
      const hasRights = rights.some((right) => userRoles.includes(right))
      if (makerId) {
        const isMaker = makerId === `${token.first_name} ${token.last_name}`
        return isMake ? hasRights && isMaker : hasRights && !isMaker
      } else {
        return hasRights
      }
    }
  }
  return false
}
//checks if the user access token has the rights passed and if not returns false
export const HasAccessToRights = (rights: string[]) => {
  if (typeof window !== 'undefined') {
    const accessToken = localStorage.getItem('accessToken')
    if (accessToken) {
      const token: IDecodeToken = jwtDecode(accessToken)
      const userRoles = token.authorities
      return rights.some((right) => userRoles.includes(right))
    }
  }
  return false
}

export const checkIsMaker = (makerId?: string): boolean => {
  if (!makerId) {
    return false
  }
  if (typeof window !== 'undefined') {
    const accessToken = localStorage.getItem('accessToken')
    if (accessToken) {
      const token: IDecodeToken = jwtDecode(accessToken)
      return makerId === `${token.first_name} ${token.last_name}`
    }
  }
  return false
}

// Define the prop types

const isButtonComponent = (element: ReactElement<any>) => {
  // Adjust this check based on how you can identify your Button components
  return (
    element.props.variant ||
    element.props.onClick ||
    element.props.startIcon ||
    element.props.endIcon ||
    element.props.onChange
  )
}
export const AccessControlWrapper: React.FC<IAccessControlWrapperProps> = ({
  rights,
  children,
  makerId,
  isMake = false,
}) => {
  const hasAccess = AccessControlHandler(rights, makerId, isMake)
  // Process children to conditionally disable buttons
  const processChildren = (children: ReactNode) => {
    return Children.map(children, (child) => {
      // Check if the child is a valid React element
      if (isValidElement(child) && isButtonComponent(child)) {
        // Check if the element is a button and clone it with the disabled prop
        const element = child as ReactElement<{ disabled?: boolean }>
        return !hasAccess ? cloneElement(element, { disabled: true }) : child
      }
      return child
    })
  }

  return <>{processChildren(children)}</>
}

export default AccessControlWrapper
