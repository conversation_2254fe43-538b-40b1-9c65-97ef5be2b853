import { jwtDecode } from 'jwt-decode'

import { IDecodeToken } from '../interfaces'

/**
 * Checks if the user is logged in
 */
export const isLoggedIn = () => {
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('accessToken') || ''
    try {
      if (typeof token === 'undefined' || token === null || token === '') {
        return false
      } else {
        const decodedToken: IDecodeToken = jwtDecode(token)
        if (decodedToken.exp * 1000 > new Date().getTime()) {
          return true
        }
      }
    } catch (error) {
      localStorage.clear()
      console.error('Error decoding token: ', error)
    }
  }
  return false
}

export const clearStore = () => {
  if (typeof window !== 'undefined') {
    const isFirstVisit = !localStorage.getItem('appInitialized')

    if (isFirstVisit) {
      // Clear all storage
      window.localStorage.clear()
      window.sessionStorage.clear()

      // Set initialization flag
      localStorage.setItem('appInitialized', 'true')

      // Optional: Force reload to ensure clean state
      window.location.reload()
    }
  }
}
