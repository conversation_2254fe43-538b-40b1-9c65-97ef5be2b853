import axios, { AxiosError, AxiosInstance } from 'axios'

import { authConfig } from './authHeader'
import { ErrorResponse } from './secureApi'

const openAuth = {
  ...authConfig,
  baseURL: process.env.NEXT_PUBLIC_OPEN_API_BASE_URL,
}
const openAuth2 = {
  ...authConfig,
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
}
const instance: AxiosInstance = axios.create(openAuth)
const instance2: AxiosInstance = axios.create(openAuth2)

class APIError extends Error {
  public statusCode: number
  public description: string

  constructor(status: number, message: string, description: string) {
    super(message)

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, APIError)
    }

    this.statusCode = status
    this.description = description
    this.message = message
  }
}

const errorHandler = (err: AxiosError<ErrorResponse>) => {
  let message = 'Unknown error occurred'
  try {
    if (err.response) {
      const { status, data } = err.response
      if (status === 400) {
        message = err.response.data.error
          ? err.response.data.error
          : err.response.data.errors
            ? err.response.data.errors[0]
            : err.response.data.message || 'Bad Request'
      }
      if (status === 429) {
        message = err.response.data.error
          ? err.response.data.error
          : err.response.data.errors
            ? err.response.data.errors[0]
            : err.response.data.message || 'Too Many Requests'
      }
      throw new APIError(status, message, data.description ?? '')
    } else if (err.request) {
      throw new APIError(503, 'No response from server', '')
    } else {
      throw new APIError(400, message, '')
    }
  } catch (e) {
    return e instanceof Error ? e : new Error(message)
  }
}

instance.interceptors.response.use(
  (response) => response,
  (error: AxiosError<ErrorResponse>) => Promise.reject(errorHandler(error))
)
instance2.interceptors.response.use(
  (response) => response,
  (error: AxiosError<ErrorResponse>) => Promise.reject(errorHandler(error))
)
export const openapi = instance
export const openapi2 = instance2
