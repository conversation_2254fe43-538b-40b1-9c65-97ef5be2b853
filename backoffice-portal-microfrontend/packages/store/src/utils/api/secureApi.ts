import axios, {
  AxiosError,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios'
import { jwtDecode } from 'jwt-decode'

import { authConfig, authConfig2 } from './authHeader'
import { IDecodeToken } from '../../interfaces'
import { refreshToken } from '../../actions'

const instance1 = axios.create(authConfig)
const instance2 = axios.create(authConfig2)

class APIError extends Error {
  private statusCode: number
  constructor(statusCode: number, message: string) {
    super(message)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, APIError)
    }
    this.statusCode = statusCode
  }
}

export interface ErrorResponse {
  message?: string
  error?: string
  errors?: string[]
  status: number
  code?: string
  description?: string
  data: {
    message?: string
    error?: string
    errors?: string
  }
}
const errorHandler = (err: AxiosError<ErrorResponse>) => {
  let message = 'Something went wrong, try again later'
  let statusCode = 400

  if (err.response) {
    statusCode = err.response.status
    message = err.response.data.message || message
    const responseData = err.response.data

    if (statusCode === 401) {
      if (responseData.message === 'The received token is expired') {
        message = 'You are not allowed to perform this request.'
      } else if (responseData.code === 'no_token') {
        message =
          'Your session is no longer valid due to login on another device or browser. Please log in again.'

        setTimeout(() => {
          window.history.pushState({}, '', '/')
          localStorage.clear()
          sessionStorage.clear()
        }, 1000)
      } else {
        message = 'You are not allowed to perform this request'
      }
    }

    if (statusCode === 403) {
      message = err.response.data.error
        ? err.response.data.error
        : err.response.data.errors
          ? err.response.data.errors[0]
          : err.response.data.message || 'Access Denied!!'
    }
    if (statusCode === 404) {
      message =
        err.response.data.errors?.[0] ||
        err.response.data.message ||
        'Resource not found'
    }

    if (statusCode === 400) {
      message = err.response.data.error
        ? err.response.data.error
        : err.response.data.errors
          ? err.response.data.errors[0]
          : err.response.data.message || message
    }
    if (statusCode === 409) {
      message = err.response.data.error
        ? err.response.data.error
        : err.response.data.errors
          ? err.response.data.errors[0]
          : err.response.data.message || message
    }
    if (statusCode === 500) {
      message = err.response.data.errors
        ? err.response.data.errors[0]
        : err.response.data.message
          ? err.response.data.message
          : message
    }
  } else if (err.request) {
    statusCode = 503
    message = 'No response from server'
  }
  return new APIError(statusCode, message)
}

export async function refreshTokenIfNeeded(
  refreshToken: () => Promise<void>
): Promise<string | null> {
  let token = localStorage.getItem('accessToken')
    ? localStorage.getItem('accessToken')
    : ''
  if (
    !token ||
    jwtDecode<IDecodeToken>(token).exp * 1000 < new Date().getTime()
  ) {
    await refreshToken()
    token = localStorage.getItem('accessToken') || ''
  }
  return token
}

const tokenInterceptor = async (config: InternalAxiosRequestConfig) => {
  const token = await refreshTokenIfNeeded(refreshToken)
  if (token && config.headers) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
}

// Set the AUTH token for any request
instance1.interceptors.request.use(tokenInterceptor)

instance1.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError<ErrorResponse>) => Promise.reject(errorHandler(error))
)
instance2.interceptors.request.use(tokenInterceptor)

instance2.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError<ErrorResponse>) => Promise.reject(errorHandler(error))
)

export const secureapi = instance1
export const secureapi2 = instance2
