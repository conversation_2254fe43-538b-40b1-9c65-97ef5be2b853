export const ACCESS_CONTROLS = {
  //customers
  REJECT_APPROVALREQUEST_CUSTOMERS: [
    'REJECT_UPDATE_CUSTOMERS',
    'REJECT_ACTIVATE_CUSTOMERS',
    'REJECT_DEACTIVATE_CUSTOMERS',
    'REJECT_CREATE_CUSTOMERS',
  ],
  ACCEPT_APPROVALREQUEST_CUSTOMERS: [
    'ACCEPT_UPDATE_CUSTOMERS',
    'ACCEPT_CREATE_CUSTOMERS',
    'ACCEPT_DEACTIVATE_CUSTOMER',
    'ACCEPT_ACTIVATE_CUSTOMERS',
  ],
  CREATE_CUSTOMERS: ['SUPER_CREATE_CUSTOMERS', 'MAKE_CREATE_CUSTOMERS'],
  UPDATE_CUSTOMERS: ['SUPER_UPDATE_CUSTOMERS', 'MAKE_UPDATE_CUSTOMERS'],
  DEACTIVATE_CUSTOMERS: [
    'MAKE_DEACTIVATE_CUSTOMERS',
    'SUPER_DEACTIVATE_CUSTOMERS',
  ],
  ACTIVATE_CUSTOMERS: ['MAKE_ACTIVATE_CUSTOMERS', 'SUPER_ACTIVATE_CUSTOMERS'],
  DELETE_CUSTOMERS: ['SUPER_DELETE_CUSTOMERS', 'MAKE_DELETE_CUSTOMERS'],
  //profile devices
  CREATE_DEVICE: ['SUPER_CREATE_DEVICE', 'MAKE_CREATE_DEVICE'],
  ACTIVATE_DEVICE: ['SUPER_ACTIVATE_DEVICE', 'MAKE_ACTIVATE_DEVICE'],
  DEACTIVATE_DEVICE: ['SUPER_DEACTIVATE_DEVICE', 'MAKE_DEACTIVATE_DEVICE'],

  ACCEPT_APPROVAL_REQUEST_DEVICES: [
    'ACCEPT_DEACTIVATED_DEVICE',
    'ACCEPT_ACTIVATED_DEVICE',
  ],
  REJECT_APPROVAL_REQUEST_DEVICES: [
    'REJECT_ACTIVATE_DEVICE',
    'REJECT_DEACTIVATE_DEVICE',
  ],
  RESET_SECURITY_QUESTIONS: [
    'SUPER_RESET_SECURITY_QUESTIONS',
    'MAKE_RESET_SECURITY_QUESTIONS',
  ],
  ACCEPT_RESET_SECURITY_QUESTIONS: ['ACCEPT_RESET_SECURITY_QUESTIONS'],
  REJECT_RESET_SECURITY_QUESTIONS: ['REJECT_RESET_SECURITY_QUESTIONS'],

  //user-management
  CREATE_USERS: ['SUPER_CREATE_USERS', 'MAKE_CREATE_USERS'],
  UPDATE_USERS: ['SUPER_UPDATE_USERS', 'MAKE_UPDATE_USERS'],
  DEACTIVATE_ACTIVATE_USERS: [
    'MAKE_DEACTIVATE_USERS',
    'MAKE_ACTIVATE_USERS',
    'SUPER_DEACTIVATE_USERS',
    'SUPER_ACTIVATE_USERS',
  ],
  ACCEPT_APPROVALREQUEST_USERS: [
    'ACCEPT_DELETE_USERS',
    'ACCEPT_ACTIVATE_USERS',
    'ACCEPT_CREATE_USERS',
    'ACCEPT_UPDATE_USERS',
    'ACCEPT_DEACTIVATE_USERS',
  ],
  REJECT_APPROVALREQUEST_USERS: [
    'REJECT_CREATE_USERS',
    'REJECT_UPDATE_USERS',
    'REJECT_DEACTIVATE_USERS',
    'REJECT_ACTIVATE_USERS',
    'REJECT_DELETE_USERS',
  ],
  //roles
  CREATE_ROLES: ['MAKE_CREATE_GROUPS', 'SUPER_CREATE_GROUPS'],
  UPDATE_ROLES: ['MAKE_UPDATE_GROUPS', 'SUPER_UPDATE_GROUPS'],
  DELETE_ROLE: ['MAKE_DELETE_GROUPS', 'SUPER_DELETE_GROUPS'],

  REJECT_APPROVALREQUEST_ROLES: [
    'REJECT_ACTIVATE_GROUPS',
    'REJECT_UPDATE_GROUPS',
    'REJECT_DEACTIVATE_GROUPS',
    'REJECT_DELETE_GROUPS',
  ],
  ACCEPT_APPROVALREQUEST_ROLES: [
    'ACCEPT_ACTIVATE_GROUPS',
    'ACCEPT_UPDATE_GROUPS',
    'ACCEPT_DEACTIVATE_GROUPS',
    'ACCEPT_DELETE_GROUPS',
  ],
  //notifications
  CREATE_NOTIFICATIONS: [
    'MAKE_CREATE_NOTIFICATIONS',
    'SUPER_CREATE_NOTIFICATIONS',
  ],
  UPDATE_NOTIFICATIONS: [
    'MAKE_UPDATE_NOTIFICATIONS',
    'SUPER_UPDATE_NOTIFICATIONS',
  ],
  DELETE_NOTIFICATIONS: [
    'MAKE_DELETE_NOTIFICATIONS',
    'SUPER_DELETE_NOTIFICATIONS',
  ],
  ACCEPT_NOTIFICATIONS: [
    'ACCEPT_CREATE_NOTIFICATIONS',
    'ACCEPT_DELETE_NOTIFICATIONS',
    'ACCEPT_ACTIVATE_NOTIFICATIONS',
    'ACCEPT_DEACTIVATE_NOTIFICATIONS',
    'ACCEPT_UPDATE_NOTIFICATIONS',
  ],
  REJECT_NOTIFICATIONS: [
    'REJECT_UPDATE_NOTIFICATIONS',
    'REJECT_DELETE_NOTIFICATIONS',
    'REJECT_ACTIVATE_NOTIFICATIONS',
    'REJECT_CREATE_NOTIFICATIONS',
  ],
  UPDATE_ACCOUNT_PREFERENCES: [
    'SUPER_UPDATE_ACCOUNTS_PREFERENCES',
    'MAKE_UPDATE_ACCOUNTS_PREFERENCES',
  ],
  REJECT_ACCOUNT_PREFERENCES: ['REJECT_UPDATE_ACCOUNTS_PREFERENCES'],
  ACCEPT_ACCOUNT_PREFERENCES: ['ACCEPT_UPDATE_ACCOUNTS_PREFERENCES'],

  //Profiles
  ACCEPT_APPROVALREQUEST_PROFILES: ['ACCEPT_RESET_SECURITY_QUESTIONS'],
  REJECT_APPROVALREQUEST_PROFILES: ['REJECT_RESET_SECURITY_QUESTIONS'],

  //Cards module

  ACTIVATE_CARDS: ['SUPER_ACTIVATE_CARDS', 'MAKE_ACTIVATE_CARDS', 'BRANCH_MAKE_ACTIVATE_CARDS'],
  REJECT_APPROVALREQUEST_CARDS: ['REJECT_ACTIVATE_CARDS', 'BRANCH_REJECT_ACTIVATE_CARDS'],
  ACCEPT_APPROVALREQUEST_CARDS: ['ACCEPT_ACTIVATE_CARDS', 'BRANCH_ACCEPT_ACTIVATE_CARDS'],
  RESET_PIN_TRY_COUNTER: [
    'MAKE_CARDS_RESET_PIN_TRY_COUNTER',
    'SUPER_CARDS_RESET_PIN_TRY_COUNTER',
  ],

  // Branch-specific card rights
  BRANCH_ACTIVATE_CARDS: ['BRANCH_MAKE_ACTIVATE_CARDS'],
  BRANCH_ACCEPT_ACTIVATE_CARDS: ['BRANCH_ACCEPT_ACTIVATE_CARDS'],
  BRANCH_REJECT_ACTIVATE_CARDS: ['BRANCH_REJECT_ACTIVATE_CARDS'],
  BRANCH_VIEW_CARDS: ['BRANCH_VIEW_CARDS'],

  // Tariffs and Charge configurations

  CREATE_TARIFF: ['MAKE_CREATE_TARIFF', 'SUPER_CREATE_TARIFFS'],
  UPDATE_SERVICE_CONFIGS: [
    'MAKE_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS',
    'SUPER_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS',
  ],
  CREATE_SERVICE_CONFIGS: [
    'SUPER_CREATE_PAYMENT_SERVICE_CONFIGURATIONS',
    'MAKE_CREATE_PAYMENT_SERVICE_CONFIGURATIONS',
  ],
  REJECT_TARIFFS: [
    'REJECT_CREATE_TARIFFS',
    'REJECT_DEACTIVATE_TARIFFS',
    'REJECT_CREATE_PAYMENT_SERVICE_CONFIGURATIONS',
    'REJECT_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS',
    'REJECT_DEACTIVATE_PAYMENT_SERVICE_CONFIGURATIONS',
  ],
  ACCEPT_TARIFFS: [
    'ACCEPT_CREATE_TARIFFS',
    'ACCEPT_DEACTIVATE_TARIFFS',
    'ACCEPT_CREATE_PAYMENT_SERVICE_CONFIGURATIONS',
    'ACCEPT_UPDATE_PAYMENT_SERVICE_CONFIGURATIONS',
    'ACCEPT_DEACTIVATE_PAYMENT_SERVICE_CONFIGURATIONS',
  ],
}

export const restrictReasons: string[] = [
  'Fraudulent Activity',
  'Suspicious Behavior',
  'Violation of terms',
  'Legal Requirement',
  'Other',
]
export const reasonsForUnlinking: string[] = [
  'User Request',
  'Security Concerns',
  'Account Inactivity',
  'Other',
]

export const reasonsForActivation: string[] = [
  'Customer Request',
  'Promotion Campaign',
  'False Alarm',
  'Other',
]

export const reasonsForDeleting: string[] = [
  'User Request',
  'Account Compromised',
  'Duplicate Account',
  'Violation of Terms of Service',
  'Other',
]
export const reasonsForDeactivating: string[] = [
  'User Request',
  'Temporary Suspension',
  'Fraudulent Activity',
  'Security Concerns',
  'Other',
]
export const reasonsForUnsubscribing: string[] = [
  'Too many notifications',
  'Irrelevant content',
  'Temporary Break',
  'Not interested in updates anymore',
  'Other',
]
const reasonsForActivating: string[] = [
  'Reinstatement',
  'Resolved Issues',
  'Other',
]

export const alertTypes = [
  { label: 'Credit', type: 'ACCOUNT_CREDIT', frequency: '' },
  { label: 'Debit', type: 'ACCOUNT_DEBIT', frequency: '' },
  {
    label: 'Daily balance',
    type: 'BALANCE_ALERT',
    frequency: 'Daily Frequency',
  },
  {
    label: 'Weekly balance',
    type: 'BALANCE_ALERT',
    frequency: 'Weekly Frequency',
  },
  {
    label: 'Monthly balance',
    type: 'BALANCE_ALERT',
    frequency: 'Monthly Frequency',
  },
  { label: 'Overdrawn Account', type: 'OVERDRAFT_ALERT', frequency: '' },
]
