import { Dispatch } from '@reduxjs/toolkit'
import { secureapi2 } from '../utils'
import { 
  setPlatforms, 
  setIsLoadingPlatforms, 
  setPlatformsSummary 
} from '../reducers/platform'
import { IPlatformSummary } from '../interfaces'

export const getUserPlatform = async (dispatch: Dispatch, params?: string) => {
  dispatch(setIsLoadingPlatforms(true))
  try {
    const response = await secureapi2.get(`/dbp/platforms?${params || ''}`)
    const { data, ...rest } = response.data
    dispatch(setPlatforms(data))
    dispatch(setPlatformsSummary(rest as IPlatformSummary))
    dispatch(setIsLoadingPlatforms(false))
    return response.data
  } catch (e) {
    dispatch(setIsLoadingPlatforms(false))
    console.error('Error fetching platforms: ', e)
    throw e
  }
}
