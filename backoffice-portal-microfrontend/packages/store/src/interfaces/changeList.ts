export type Changes = {
    actionee: actionee
    action: Action
    previousState?: null | string
    actionSubject: ActionSubject
    date: DateEnum
    time: Time
    resource?: null | string
    comment?: null | string
    type: 'creation' | 'approval' | 'edited' | 'deletion'
  }
  
  export type Action = 'create' | 'edit' | 'delete'
  
  export type ActionSubject = 'Patsheba Gikunda' // actionee
  
  export type actionee = '<PERSON>' | '<PERSON>'
  
  export type DateEnum = 'February 25, 2023 ' | 'February 26, 2023 '
  
  export type Time = '10:00 PM' | '10:00 AM' | '11:00 AM'
  
  export const changes: Changes[] = [
    {
      actionee: '<PERSON>',
      action: 'create',
      previousState: null,
      actionSubject: 'Patsheba Gikunda',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      resource: null,
      comment: 'Created customer',
      type: 'creation',
    },
    {
      actionee: '<PERSON>',
      action: 'create',
      previousState: null,
      actionSubject: 'Patsheba Gikunda',
      resource: null,
      date: 'February 25, 2023 ',
      time: '10:00 AM',
      comment: null,
      type: 'approval',
    },
    {
      actionee: '<PERSON>',
      action: 'edit',
      previousState: 'Patshe<PERSON> Gikunda2',
      actionSubject: 'Patsheba Gikunda',
      resource: 'name',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      comment: 'Edited customer',
      type: 'approval',
    },
    {
      actionee: '<PERSON> Doe',
      action: 'edit',
      previousState: 'Patsheba Gikunda2',
      actionSubject: 'Patsheba Gikunda',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      resource: 'email',
      comment: 'Edited customer',
      type: 'edited',
    },
    {
      actionee: 'John Doe',
      action: 'create',
      previousState: null,
      actionSubject: 'Patsheba Gikunda',
      resource: null,
      date: 'February 25, 2023 ',
      time: '10:00 AM',
      comment: null,
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'edit',
      previousState: 'Patsheba Gikunda2',
      actionSubject: 'Patsheba Gikunda',
      resource: 'name',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      comment: 'Edited customer',
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'create',
      previousState: null,
      actionSubject: 'Patsheba Gikunda',
      resource: null,
      date: 'February 25, 2023 ',
      time: '10:00 AM',
      comment: null,
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'edit',
      previousState: 'Patsheba Gikunda2',
      actionSubject: 'Patsheba Gikunda',
      resource: 'name',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      comment: 'Edited customer',
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'create',
      previousState: null,
      actionSubject: 'Patsheba Gikunda',
      resource: null,
      date: 'February 25, 2023 ',
      time: '10:00 AM',
      comment: null,
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'edit',
      previousState: 'Patsheba Gikunda2',
      actionSubject: 'Patsheba Gikunda',
      resource: 'name',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      comment: 'Edited customer',
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'create',
      previousState: null,
      actionSubject: 'Patsheba Gikunda',
      resource: null,
      date: 'February 25, 2023 ',
      time: '10:00 AM',
      comment: null,
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'edit',
      previousState: 'Patsheba Gikunda2',
      actionSubject: 'Patsheba Gikunda',
      resource: 'name',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      comment: 'Edited customer',
      type: 'approval',
    },
    {
      actionee: 'Jane Smith',
      action: 'delete',
      previousState: 'Patsheba Gikunda',
      actionSubject: 'Patsheba Gikunda',
      resource: 'customer',
      date: 'February 26, 2023 ',
      time: '11:00 AM',
      comment: 'Deleted customer',
      type: 'deletion',
    },
    {
      actionee: 'John Doe',
      action: 'create',
      previousState: null,
      actionSubject: 'Patsheba Gikunda',
      resource: null,
      date: 'February 25, 2023 ',
      time: '10:00 AM',
      comment: null,
      type: 'approval',
    },
    {
      actionee: 'John Doe',
      action: 'edit',
      previousState: 'Patsheba Gikunda2',
      actionSubject: 'Patsheba Gikunda',
      resource: 'name',
      date: 'February 25, 2023 ',
      time: '10:00 PM',
      comment: 'Edited customer',
      type: 'approval',
    },
  ]
  