import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { IChannelModule, IDecodeToken } from '../interfaces'

export interface IAuth {
  isLoadingLogin: boolean
  isLoginSuccess: boolean
  isLoginError: boolean
  loginErrorMessage: string
  decodedToken: IDecodeToken
  channelModules: IChannelModule[]

  // login actions
  userInfo: {
    username: string
    email: string
    phoneNumber: string
  } | null
}
const initialState: IAuth = {
  isLoadingLogin: false,
  isLoginSuccess: false,
  loginErrorMessage: '',
  isLoginError: false,
  decodedToken: {} as IDecodeToken,
  channelModules: [],

  // login actions
  userInfo:
    typeof window !== 'undefined' && localStorage.getItem('userInfo')
      ? JSON.parse(localStorage.getItem('userInfo') || '{}')
      : null,
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setIsLoadingLogin: (state, action: PayloadAction<boolean>) => {
      state.isLoadingLogin = action.payload
    },
    setIsLoginSuccess: (state, action: PayloadAction<boolean>) => {
      state.isLoginSuccess = action.payload
    },
    setIsLoginError: (state, action: PayloadAction<boolean>) => {
      state.isLoginError = action.payload
    },
    setDecodedToken: (state, action: PayloadAction<IDecodeToken>) => {
      state.decodedToken = action.payload
    },
    setChannelModules: (state, action: PayloadAction<IChannelModule[]>) => {
      state.channelModules = action.payload
    },
    // login actions
    setCredentials: (state, action) => {
      state.userInfo = action.payload
      localStorage.setItem('userInfo', JSON.stringify(action.payload))
    },
    setLoginErrorMessage: (state, action: PayloadAction<string>) => {
      state.loginErrorMessage = action.payload
    },
  },
})
export const {
  setIsLoadingLogin,
  setDecodedToken,
  setIsLoginSuccess,
  setIsLoginError,
  setChannelModules,
  setLoginErrorMessage,

  // login actions
  setCredentials,
} = authSlice.actions
export default authSlice.reducer
