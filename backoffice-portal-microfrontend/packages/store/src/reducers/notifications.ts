'use client'

import { createSlice, PayloadAction } from '@reduxjs/toolkit'
export interface ILocalNotification {
  message: string
  type: ILocalNotificationType
}
type ILocalNotificationType = 'info' | 'success' | 'warning' | 'error'

const initialLocalNotification: ILocalNotification = {
  message: '',
  type: 'info',
}

const initialState = {
  localNotification: initialLocalNotification.message,
  localNotificationType: initialLocalNotification.type,
}

const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setNotification: (state, action: PayloadAction<ILocalNotification>) => {
      state.localNotification = action.payload.message
      state.localNotificationType = action.payload.type
    },
    clearNotification: () => initialState,
  },
})
export const { setNotification, clearNotification } = notificationsSlice.actions
export default notificationsSlice.reducer
