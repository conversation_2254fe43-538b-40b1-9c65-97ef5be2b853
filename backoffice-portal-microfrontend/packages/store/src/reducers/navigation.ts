import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { ICustomer, IRole, IUser } from '../interfaces'

export interface INavigationState {
  switchToCustomerDetails: {
    customer: ICustomer | null
    open: boolean
    isPendingCustomer: boolean
  }
  switchToUserDetails: {
    user: IUser | null
    open: boolean
  }
  switchToRoleDetails: {
    role: IRole | null
    open: boolean
    type: string
  }
  isSidebarCollapsed: boolean
  documentToggle: {
    open: boolean
    imageUrl: string
  }
}

const initialState: INavigationState = {
  switchToCustomerDetails: {
    customer: null,
    open: false,
    isPendingCustomer: false,
  },
  switchToUserDetails: {
    open: false,
    user: null,
  },
  switchToRoleDetails: {
    open: false,
    role: null,
    type: '',
  },
  isSidebarCollapsed: false,
  documentToggle: {
    open: false,
    imageUrl: '',
  },
}

const navigationSlice = createSlice({
  name: 'navigation',
  initialState,
  reducers: {
    setSwitchToUserDetails: (
      state,
      action: PayloadAction<{ open: boolean; user: IUser | null }>
    ) => {
      state.switchToUserDetails = action.payload
    },
    setSwitchToCustomerDetails: (state, action) => {
      state.switchToUserDetails = action.payload
      state.switchToRoleDetails = {
        open: false,
        role: null,
        type: '',
      }
    },
    setSwitchToRoleDetails: (state, action) => {
      state.switchToRoleDetails = action.payload
      state.switchToUserDetails = {
        open: false,
        user: null,
      }
    },
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.isSidebarCollapsed = action.payload
    },
    setDocumentToggle: (state, action: PayloadAction<{open: boolean; imageUrl: string}>) => {
      state.documentToggle = action.payload
    },
  },
})

export const {
  setSwitchToUserDetails,
  setSwitchToCustomerDetails,
  setSwitchToRoleDetails,
  setSidebarCollapsed,
  setDocumentToggle,
} = navigationSlice.actions
export default navigationSlice.reducer
