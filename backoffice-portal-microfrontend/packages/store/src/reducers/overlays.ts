import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { IRole, IUser } from '../interfaces'

type TComponent =
  | 'create_user'
  | 'create_role'
  | 'view_right'
  | 'change_log'
  | 'edit_role'
  | 'edit_user'
  | 'view_roles'
  | 'event_history'
  | 'security_question_history'
  | 'pin_history'
  | 'customer_accounts_history'
  | 'pending_approval_requests'

interface DrawerChildren {
  open: boolean
  drawerChildren: {
    childType: TComponent
    data?:
      | string
      | null
      | IRole
      | IUser
      | IRole[]
      | {
          event: string
          event_source: string
          event_date: string
          id: string
        }[]
  } | null
  header: string
}

export interface OverlayState {
  openUserChangeLogDrawer: boolean
  drawer: DrawerChildren
}

const initialState: OverlayState = {
  openUserChangeLogDrawer: false,
  drawer: {
    drawerChildren: null,
    header: '',
    open: false,
  },
}

const overlaySlice = createSlice({
  name: 'overlays',
  initialState,
  reducers: {
    setOpenUserChangeLogs: (state, action) => {
      state.openUserChangeLogDrawer = action.payload
    },
    setDrawer: (state, action: PayloadAction<DrawerChildren>) => {
      state.drawer = action.payload
    },
    resetDrawer: (state) => {
      state.drawer = initialState.drawer
    },
  },
})

export const { setOpenUserChangeLogs, setDrawer, resetDrawer } =
  overlaySlice.actions

export default overlaySlice.reducer
