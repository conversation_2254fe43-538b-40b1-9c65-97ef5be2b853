{"name": "@dtbx/vitest-config", "version": "0.0.3", "type": "module", "license": "UNLICENSED", "exports": {"./base": {"types": "./dist/base.d.ts", "default": "./dist/base.js"}, "./next": {"types": "./next.d.ts", "default": "./dist/next.js"}, "./setup": {"types": "./vitest.setup.d.ts", "default": "./dist/vitest.setup.js"}}, "files": ["dist/**"], "publishConfig": {"access": "restricted"}, "scripts": {"compile": "tsc", "collect-json-reports": "node dist/scripts/collect-json-outputs.js", "merge-json-reports": "nyc merge coverage/raw coverage/merged/merged-coverage.json", "report": "nyc report -t coverage/merged --report-dir coverage/report --reporter=html --reporter=text-summary --exclude-after-remap false", "view-report": "open coverage/report/index.html", "clean": "rimraf .turbo __tests__/coverage"}, "devDependencies": {"@dtbx/typescript-config": "workspace:*", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@vitejs/plugin-react": "^4.7.0", "@vitest/coverage-istanbul": "^3.2.4", "@vitest/ui": "3.2.4", "glob": "^11.0.3", "jsdom": "^26.1.0", "nyc": "^17.1.0", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}