{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"esModuleInterop": true, "incremental": true, "isolatedModules": true, "lib": ["es2022", "DOM", "DOM.Iterable"], "module": "NodeNext", "moduleDetection": "force", "moduleResolution": "NodeNext", "noUncheckedIndexedAccess": false, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2022", "strictNullChecks": true, "noEmit": false}, "exclude": ["node_modules"]}