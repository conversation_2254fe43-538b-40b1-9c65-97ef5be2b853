trigger:
  branches:
    include:
      - dev
      - uat
      - prod
variables:
  vmImageName: 'ubuntu-latest'
  dockerComposeFilePath: '**/docker-compose.yml'
  tag: '$(Build.BuildId)'
  nodeVersion: '22'

resources:
  repositories:
    - repository: templates
      type: git
      name: DevSecOps/azure-pipelines-configs

stages:
  - stage: DEV_CI
    displayName: Build DEV
    condition: and(always(), contains(variables['Build.SourceBranch'], 'refs/heads/dev'))
    jobs:
      - template: microfrontend-build-template.yml@templates
        parameters:
          environment: 'dev'
          vmImageName: $(vmImageName)
          dockerRegistryServiceConnection: 'dtbxmacr-dev'
          imageRepository: 'backoffice-portal-microfrontend'
          dockerComposeFilePath: $(dockerComposeFilePath)
          containerRegistry: 'dtbxdevmcacr.azurecr.io'
          tag: $(tag)

  - stage: Version_and_Release
    displayName: 'Package Release'
    dependsOn: DEV_CI
    condition: and(succeeded(), contains(variables['Build.SourceBranch'], 'refs/heads/dev'))
    jobs:
      - job: Release
        displayName: 'Version, Publish, and Release'
        pool:
          vmImage: $(vmImageName)
        steps:
          - checkout: self
            persistCredentials: true
            fetchDepth: 0

          - bash: |
              # This script finds all .md files in .changeset, excluding README.md, and counts them.
              # If one or more are found, it sets the 'hasChangesets' variable to 'true'.
              if [ $(find .changeset -maxdepth 1 -type f -name "*.md" ! -name "README.md" | wc -l) -gt 0 ]; then
                echo "Changeset files found. Proceeding with release."
                echo "##vso[task.setvariable variable=hasChangesets]true"
              else
                echo "No changeset files found. Skipping release steps."
                echo "##vso[task.setvariable variable=hasChangesets]false"
              fi
            name: ChangesetCheck
            displayName: 'Check for Package Changes'

          - script: |
              git config --global user.name "Azure Pipeline"
              git config --global user.email "<EMAIL>"
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Configure Git User'

          - task: npmAuthenticate@0
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Authenticate with Azure Artifacts'
            inputs:
              workingFile: '.npmrc'

          - task: NodeTool@0
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Install Node.js $(nodeVersion)'
            inputs:
              versionSpec: $(nodeVersion)

          - script: corepack enable
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Enable Corepack for pnpm'

          - script: pnpm install --frozen-lockfile
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Install Dependencies'

          - script: |
              echo "INFO: Versioning packages based on changeset files..."
              pnpm version-packages
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Version Packages'

          - script: |
              echo "INFO: Compiling and publishing packages..."
              pnpm release
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Compile and Publish'
            env:
              NODE_OPTIONS: --max-old-space-size=4096

          - script: |
              echo "INFO: Switching to the source branch to commit changes..."
              git checkout $(Build.SourceBranch)
              echo "INFO: Staging all changes for commit."
              git add .
              echo "INFO: Unstaging .npmrc to prevent committing auth tokens."
              git restore --staged .npmrc
              echo "INFO: Displaying git status before commit."
              git status
              echo "INFO: Committing version updates with '[skip ci]'..."
              git commit --no-verify -m "chore(release): version packages [skip ci]"
              echo "INFO: Pushing commit and tags to the remote repository... Branch $(Build.SourceBranch)"
              git push origin HEAD:$(Build.SourceBranch) --follow-tags
              echo "SUCCESS: Version, publish, and push completed."
            condition: eq(variables.hasChangesets, 'true')
            displayName: 'Commit and Push Release'

  - stage: UAT_CI
    displayName: Build UAT
    condition: and(always(), contains(variables['Build.SourceBranch'], 'refs/heads/uat'))
    jobs:
      - template: microfrontend-build-template.yml@templates
        parameters:
          environment: 'uat'
          vmImageName: $(vmImageName)
          dockerRegistryServiceConnection: 'dtbxmacr-uat'
          imageRepository: 'backoffice-portal-microfrontend'
          dockerComposeFilePath: $(dockerComposeFilePath)
          containerRegistry: 'dtbxmcacr.azurecr.io'
          tag: $(tag)

  - stage: PROD_CI
    displayName: Build PROD
    condition: and(always(), contains(variables['Build.SourceBranch'], 'refs/heads/prod'))
    jobs:
      - template: microfrontend-build-template.yml@templates
        parameters:
          environment: 'prod'
          vmImageName: $(vmImageName)
          dockerRegistryServiceConnection: 'dtbxmacr-prod'
          imageRepository: 'backoffice-portal-microfrontend'
          dockerComposeFilePath: $(dockerComposeFilePath)
          containerRegistry: 'dtbxprodmcacr.azurecr.io'
          tag: $(tag)