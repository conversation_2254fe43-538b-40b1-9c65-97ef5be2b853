services:
  landing:
    container_name: landing
    image: backoffice-portal-microfrontend-landing:latest
    build:
      context: .
      dockerfile: './apps/landing/Dockerfile'
      args:
        BUILD_ENV: ${BUILD_ENV}
    restart: always
    ports:
      - '3000:3000'
    networks:
      - app_network

# Define a network, which allows containers to communicate
# with each other, by using their container name as a hostname
networks:
  app_network:
    external: true
