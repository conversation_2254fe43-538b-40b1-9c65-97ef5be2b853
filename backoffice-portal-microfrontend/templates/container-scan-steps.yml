parameters:
  - name: containerRegistry
    default: ''
  - name: imageRepository
    default: ''

steps:
  - script: |
      mkdir -p /tmp/trivy-cache  # Create the cache directory
    displayName: 'Create Trivy Cache Directory'

  - task: Cache@2
    inputs:
      key: 'trivy-db'
      path: '/tmp/trivy-cache'
    displayName: 'Cache Trivy DB'

  - script: |
      sudo apt-get install rpm
      wget https://github.com/aquasecurity/trivy/releases/download/v$(trivyVersion)/trivy_$(trivyVersion)_Linux-64bit.deb
      sudo dpkg -i trivy_$(trivyVersion)_Linux-64bit.deb
      trivy -v
    displayName: 'Download and install Trivy'

  - task: CmdLine@2
    displayName: 'Container Image scan'
    inputs:
      script: |
        RETRIES=5
        COUNT=0
        until trivy image --scanners vuln --ignore-unfixed --exit-code 0 --severity LOW,MEDIUM,HIGH,CRITICAL ${{parameters.containerRegistry}}/${{parameters.imageRepository}}:$(tag) || [ $COUNT -eq $RETRIES ]; do
          echo "Scan attempt $((COUNT+1)) failed. Retrying in 30 seconds..."
          sleep 30
          COUNT=$((COUNT+1))
        done
        if [ $COUNT -eq $RETRIES ]; then
          echo "Trivy scan failed after $RETRIES attempts."
          exit 1
        fi