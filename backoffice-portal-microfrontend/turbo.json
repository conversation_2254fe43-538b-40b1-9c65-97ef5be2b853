{"$schema": "https://turbo.build/schema.json", "ui": "tui", "globalEnv": ["NEXT_PUBLIC_ENVIRONMENT", "NEXT_PUBLIC_API_BASE_URL", "NEXT_PUBLIC_API_BASE_URL_2", "NEXT_PUBLIC_OPEN_API_BASE_URL"], "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "build-client": {"dependsOn": ["^build-client"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next-client/**", "!.next-client/cache/**", "dist/**"]}, "compile": {"dependsOn": ["^compile"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "test": {"dependsOn": ["transit", "@dtbx/vitest-config#build"], "outputs": ["__tests__/coverage"]}, "test:view-report": {"dependsOn": ["test"]}, "test:watch": {"dependsOn": ["transit", "@dtbx/vitest-config#build"], "outputs": ["__tests__/coverage"]}, "test:vitest-ui": {"dependsOn": ["transit", "@dtbx/vitest-config#build"], "outputs": ["__tests__/coverage"]}, "transit": {"dependsOn": ["^transit"]}, "test:e2e": {"outputs": ["cypress-coverage/**"], "cache": false}, "cy:run": {"cache": false}, "cy:open": {"cache": false}, "e2e:coverage": {"cache": false}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"inputs": ["$TURBO_DEFAULT$", ".env*"], "cache": false, "persistent": true}, "dev-client": {"inputs": ["$TURBO_DEFAULT$", ".env*"], "cache": false, "persistent": true}, "start": {"cache": false}, "clean": {"cache": false}}}