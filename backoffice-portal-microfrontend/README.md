# DTBX Backoffice

DTBX Backoffice Monorepo

## Generated using Turborepo.

```sh
npx create-turbo@latest
```

## What's inside?

This Turborepo includes the following packages/apps:

### Apps

- `landing`: Root application with single sign on [Next.js](https://nextjs.org/) app
- `lms`: Loan Management [Next.js](https://nextjs.org/) app
- `x247`: DTB Mobile backoffice [Next.js](https://nextjs.org/) app
- `eatta`: Tea settlement engine [Next.js](https://nextjs.org/) app
- `ams`: Access management [Next.js](https://nextjs.org/) app
- `nms`: Notification management [Next.js](https://nextjs.org/) app
- `cms`: Cards Management System [Next.js](https://nextjs.org/) app
### Packages
- `@dtbx/ui`: a stub React component library shared by `landing` `lms` `ams` `cards management` and `x247` applications
- `@dtbx/store`: a library for redux shared by `landing` `lms` and `x247` applications
- `@dtbx/eslint-config`: `eslint` configurations (includes `eslint-config-next` and `eslint-config-prettier`)
- `@dtbx/typescript-config`: `tsconfig.json`s used throughout the monorepo
- `@dtbx/vitest-config`: `vitest.config.ts`s base configs
- `@dtbx/e2e`: cypress setup for e2e

  Each package/app is 100% [TypeScript](https://www.typescriptlang.org/).

### Utilities

This Turborepo has some additional tools already setup for you:

- [TypeScript](https://www.typescriptlang.org/) for static type checking
- [ESLint](https://eslint.org/) for code linting
- [Prettier](https://prettier.io) for code formatting
- [Vitest](https://vitest.dev) for unit testing
- [Cypress](https://www.cypress.io) for end to end testing

### Build

To build all apps and packages, run the following command:

```
cd backoffice-portal-microfrontend
pnpm build
```

### Develop

Create ``.env.local`` for each of the app.

To develop all apps and packages, run the following command:

```
cd backoffice-portal-microfrontend
pnpm install
pnpm dev
```

### Docker

Each application in the apps has a Dockerfile with 3 steps. Builder, Installer and Runner

Building using docker e.g. landing app
```

docker build . -f apps/landing/Dockerfile --build-arg BUILD_ENV="local"

```
A docker compose configuration is also include `docker-compose.yml`

To run all applications using docker compose:

1. Create a network, which allows containers to communicate with each other.
```
docker network create app_network
```

2. Run docker compose locally

```
docker compose -f docker/local/compose.yml up -d
```

### Unit Testing
This monorepo uses [Vitest](https://vitest.dev) for unit testing
#### Run Mode
To run tests for all apps and packages, run the following command:
```
pnpm test
```
or to run tests for a specific app:
```
pnpm test --filter=landing //to run tests for a specific app
```
This will always run the tests in run mode and will generate coverage for each testable app in the output folder  `__test__/coverage`. This command  should be used in the CI/CD pipeline.

To view reports for a specific app, open the `index.html` file in the `__test__/coverage` folder of the app.
```
turbo test:view-report --filter=landing
```

#### Watch Mode
To run tests in watch mode, run the following command:
```
pnpm test:watch
```
or to watch tests for a specific app:
```
pnpm test:watch --filter=landing //to watch tests for a specific app
```

#### Vitest UI
To launch tests in Vitest UI, use the following command:
```
turbo test:vitest-ui --filter=landing
```
Note: The `--filter` flag is recommended if you don't want to launch all tests at once in separate Vitest UI tabs.
#### Combined Coverage Report
To view combined test coverage for all apps, run the following command:
```
pnpm test:coverage
```

### End to End Testing
This monorepo uses [Cypress](https://www.cypress.io) for end to end testing.

Todo: Add instructions for running e2e tests
